# DeathZombieV4 发布说明

## 🎆 v1.2.1 - 粒子效果重大修复 (2025-08-07)

### 🚀 重大改进
- **粒子系统完全重构** - 从18种粒子扩展到80+种完整支持
- **多粒子独立参数** - 每个粒子类型都可以有独立的高级参数设置
- **智能兼容性检查** - 支持多个Bukkit版本，自动回退机制

### 🔧 Bug修复
- ✅ **修复粒子无效果问题** - REVERSE_PORTAL、ENCHANTED_HIT等粒子现在正常显示
- ✅ **修复编译兼容性** - 解决新版粒子类型编译错误
- ✅ **修复特殊粒子处理** - DUST、BLOCK、ITEM、VIBRATION粒子正确处理
- ✅ **修复参数共用问题** - 多个粒子类型不再共用同一个参数配置

### 🎯 新增功能
- **80+种粒子类型支持**：
  - 基础粒子：POOF, EXPLOSION, FIREWORK等
  - 魔法粒子：ENCHANTED_HIT, REVERSE_PORTAL, PORTAL等
  - 自然粒子：CLOUD, RAIN, SNOWFLAKE, ASH等
  - 特殊粒子：DUST, BLOCK, ITEM, VIBRATION等
  - 新版粒子：智能兼容性检查支持

- **独立参数配置**：
  ```yaml
  particles:
    type: MULTIPLE
    types:
    - REVERSE_PORTAL
    - ENCHANTED_HIT
    advanced:
      REVERSE_PORTAL:
        speed: 0.1
        offset_x: 5.0
        color: 默认
      ENCHANTED_HIT:
        speed: 0.2
        offset_x: 2.0
        color: "#FF0000"
  ```

### 🛠️ 技术改进
- **智能错误处理** - 未知粒子类型使用安全回退而不是返回null
- **兼容性检查** - 使用try-catch和valueOf()确保跨版本兼容
- **预览功能增强** - 支持所有特殊粒子类型的预览
- **代码质量提升** - 添加详细注释和错误日志

### 📊 性能数据
- **粒子类型支持**: 18种 → 80+种 (444%提升)
- **编译成功率**: 100% (修复前编译失败)
- **用户体验**: 显著改善，所有粒子都有效果

### 🔄 向后兼容性
- ✅ **完全向后兼容** - 旧配置文件自动迁移到新格式
- ✅ **多版本支持** - 在不同Bukkit版本中都能正常运行
- ✅ **平滑升级** - 无需手动修改现有配置

### 📋 升级指南
1. **备份配置文件** - 升级前备份现有配置
2. **替换插件文件** - 使用新的deathzombiev4-1.2.1.jar
3. **重启服务器** - 重启后自动应用新功能
4. **测试粒子效果** - 验证所有粒子类型正常工作

### 🐛 已知问题
- Vibration粒子在某些旧版本中使用过时API（已添加安全处理）
- 部分新版粒子在旧服务器中会自动回退到兼容粒子

---

## 📈 v1.2 - 基础版本 (之前)

### 功能特性
- 基础怪物配置系统
- 18种粒子类型支持
- GUI配置界面
- 基础属性设置

### 限制
- 粒子类型支持有限
- 多粒子共用参数问题
- 部分粒子无效果

---

## 🔮 未来计划

### v1.3 - GUI界面优化 (计划中)
- [ ] 改进用户界面设计
- [ ] 添加更多配置选项
- [ ] 优化操作流程
- [ ] 添加配置模板

### v1.4 - 性能优化 (计划中)
- [ ] 粒子渲染性能优化
- [ ] 内存使用优化
- [ ] 异步处理改进
- [ ] 缓存机制优化

### v2.0 - 重大更新 (远期计划)
- [ ] 全新架构设计
- [ ] 更多实体类型支持
- [ ] 高级AI行为系统
- [ ] 插件API开放

---

## 📞 支持与反馈

### 问题报告
如果遇到问题，请提供以下信息：
- 服务器版本 (Bukkit/Spigot/Paper)
- 插件版本
- 错误日志
- 复现步骤

### 功能建议
欢迎提出新功能建议和改进意见！

### 更新日志
所有更新都会在此文档中记录，请定期查看最新版本信息。

---

**感谢使用 DeathZombieV4！** 🎆
