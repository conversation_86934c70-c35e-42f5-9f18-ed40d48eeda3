# ========================================
#  用户自定义实体系统配置文件
# ========================================
system_settings:
  #是否启用默认IDC实体设置
  use_default_settings: false

  # 是否启用用户自定义设置（新的配置驱动系统）
  use_user_custom_settings: true

  # 是否在游戏中启用用户自定义实体生成
  enable_in_game_user_entities: false

  debug_mode: true

# ========================================
# 全局默认配置（未开发，毕竟也没啥用）
# ========================================
# 为所有IDC实体提供基础的默认值，当特定实体没有配置时使用
global_defaults:
  # 全局默认生命值倍数
  # 1.0 = 原始生命值，2.0 = 双倍生命值，0.5 = 半血
  health_multiplier: 1.0

  # ⚔全局默认伤害倍数
  # 1.0 = 原始伤害，2.0 = 双倍伤害，0.5 = 半伤害
  damage_multiplier: 1.0

  # 全局默认速度倍数
  # 1.0 = 原始速度，1.5 = 1.5倍速度，0.8 = 减速
  speed_multiplier: 1.0

  # 默认名称前缀
  # 支持Minecraft颜色代码，如 §c=红色，§6=金色，§a=绿色
  name_prefix: "§c"

  # 全局默认名称后缀
  # 在实体名称后添加的文本
  name_suffix: ""

  # 全局默认实体类型（当覆盖失败时使用）
  # 当指定的实体类型无效时，回退到此类型
  # 常用类型：ZOMBIE, SKELETON, CREEPER, SPIDER等
  fallback_entity_type: "ZOMBIE"

  # 默认武器
  # 当实体没有指定武器时使用的默认武器
  # 常用武器：WOODEN_SWORD, STONE_SWORD, IRON_SWORD, DIAMOND_SWORD
  default_weapon: "WOODEN_SWORD"

  # 全局默认护甲材质
  # 当实体没有指定护甲时使用的默认材质
  # 可选材质：LEATHER, CHAINMAIL, IRON, DIAMOND, NETHERITE
  default_armor_material: "LEATHER"

# ========================================
# 用户自定义覆盖配置(核心)
# ========================================
# 这里是配置文件的核心部分，用于自定义每个IDC实体的属性和行为
user_custom_overrides:
  # 全局覆盖设置
  # 应用于所有IDC实体的全局配置，优先级低于特定实体配置
  global_overrides:
    # 是否启用全局覆盖
    # true: 启用全局覆盖设置
    # false: 禁用全局覆盖（推荐，使用特定实体配置）
    enabled: false

    # 全局生命值覆盖
    # -1: 不覆盖，使用各实体的默认值
    # >0: 覆盖所有实体的生命值为指定数值
    global_health_override: -1

    # 全局伤害覆盖
    # -1: 不覆盖，使用各实体的默认伤害
    # >0: 覆盖所有实体的伤害值为指定数值
    global_damage_override: -1

    # 全局速度倍数覆盖
    # -1: 不覆盖，使用各实体的默认速度
    # >0: 覆盖所有实体的速度倍数
    global_speed_multiplier: -1

  specific_overrides:
    # IDC1 - 变异僵尸01
    idc1:
      # true: 启用自定义配置  false: 使用默认配置
      enabled: true

      # 设置实体的最大生命值，建议范围：50-500
      health_override: 120.0

      # 设置实体的基础攻击伤害，建议范围：5-30
      damage_override: 10.0

      # 1.0=正常速度，1.3=30%加速，0.8=20%减速
      speed_multiplier: 1.3

      custom_name_override: "§c§l§k|§r§c§l强化变异僵尸§k|"

      # 改变实体的基础类型，常用：ZOMBIE, ZOMBIFIED_PIGLIN, SKELETON
      entity_type_override: "ZOMBIFIED_PIGLIN"

      # 装备覆盖配置
      weapon_override: "IRON_SWORD"         # 主手武器
      helmet_override: "LEATHER_HELMET"     # 头盔
      chestplate_override: "LEATHER_CHESTPLATE"  # 胸甲
      leggings_override: "LEATHER_LEGGINGS"      # 护腿
      boots_override: "LEATHER_BOOTS"            # 靴子

      # ✨ 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 2                  # 锋利2
        FIRE_ASPECT: 1                #火焰附加1

      # 🛡️ 护甲附魔配置
      helmet_enchantments:
        PROTECTION: 1                 # 保护1
      chestplate_enchantments:
        PROTECTION: 1                 #保护1
      leggings_enchantments:
        PROTECTION: 1
      boots_enchantments:
        PROTECTION: 1

      # 药水效果配置
      # 为实体添加永久或临时的药水效果
      potion_effects:
        #永久速度效果
        speed:
          level: 1                    # 效果等级
          duration: -1                # 持续时间
        #火焰抗性
        fire_resistance:
          level: 0
          duration: -1

      # 自定义实体技能的冷却时间和参数
      skill_cooldown_overrides:
        poison_duration: 80           #剧毒持续时间（80tick=4秒）
        poison_level: 1               #剧毒等级（0=1级，1=2级）
        basic_attack: 15              #基础攻击冷却时间（15tick=0.75秒）

      #特殊能力配置
      special_abilities:
        #剧毒攻击配置
        poison_enabled: true          # 是否启用剧毒攻击
        poison_level: 1               # 剧毒等级（0=1级，1=2级，2=3级...）
        poison_duration: 80           # 剧毒持续时间（80tick=4秒）
        poison_chance: 0.8            # 剧毒攻击触发概率（0.8=80%）

        # 粒子效果配置
        particle_enabled: true        # 是否启用粒子效果
        particle_interval: 5          # 粒子效果更新间隔（5tick=0.25秒）

        #抗性配置
        fire_resistance: true         # 火焰抗性（免疫火焰伤害）
        knockback_resistance: 0.3     # 击退抗性（0.3=30%减少击退）

        #攻击增强
        attack_damage_bonus: 2.0      # 额外攻击伤害（+2点伤害）
        critical_chance: 0.15         # 暴击概率（0.15=15%）
        critical_multiplier: 1.5      # 暴击倍数（1.5=150%伤害）

    # IDC2 - 变异僵尸02
    idc2:
      enabled: true
      health_override: 100.0
      damage_override: 8.0
      speed_multiplier: 1.2
      custom_name_override: "§d§l变异僵尸02"
      entity_type_override: "SKELETON"

      weapon_override: "BOW"
      helmet_override: "CREEPER_HEAD"


      weapon_enchantments:
        POWER: 2                      # 力量2
        PUNCH: 2                      # 冲击2

      # 药水效果配置
      potion_effects:
        # 永久速度效果
        speed:
          level: 1                    # 速度等级2
          duration: -1                # 持续时间
        # 跳跃提升效果
        jump_boost:
          level: 3
          duration: -1

      # 特殊能力配置
      special_abilities:
        particle_enabled: true        # 是否启用粒子效果
        particle_interval: 5          # 粒子效果更新间隔（tick）

    # IDC3 - 变异烈焰人
    idc3:
      enabled: true
      health_override: 100.0
      damage_override: 12.0
      custom_name_override: "§c§l变异烈焰人"
      entity_type_override: "BLAZE"   # 覆盖实体类型为烈焰人

      # 药水效果配置
      potion_effects:
        speed:
          level: 3
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 攻击技能配置
        attack_enabled: true          # 是否启用攻击技能
        attack_interval: 60           # 攻击间隔（3秒，60tick）
        attack_range: 15.0            # 攻击范围（15格）

        # 烈焰粒子攻击配置
        particle_damage: 4            # 烈焰粒子伤害（4点）

        # 烈焰弹攻击配置
        fireball_damage: 8            # 烈焰弹爆炸伤害（8点）

    # IDC4 - 变异爬行者
    idc4:
      enabled: true
      health_override: 50.0
      damage_override: 15.0
      custom_name_override: "§b§l变异爬行者"
      entity_type_override: "CREEPER"

      # 药水效果配置
      potion_effects:
        speed:
          level: 5
          duration: -1

      # 特殊能力配置
      special_abilities:
        lightning_enabled: true       # 是否启用闪电攻击
        lightning_interval: 100       # 闪电攻击间隔（5秒，100tick）
        lightning_range: 20.0         # 闪电攻击范围（20格）

        death_effect_range: 10.0      # 死亡技能影响范围（10格）
        death_speed_level: 1          # 死亡后给玩家的速度等级（2级，0-based）
        death_speed_duration: 600     # 死亡后速度效果持续时间（30秒，600tick）

        # 爆炸配置
        explosion_radius: 8           # 爆炸半径
        prevent_block_damage: true    # 防止破坏方块

    # IDC5 - 变异末影螨
    idc5:
      enabled: true
      health_override: 150.0
      damage_override: 6.0
      speed_multiplier: 6.0
      custom_name_override: "§5§l变异末影螨"
      entity_type_override: "ENDERMITE"

      # 药水效果配置
      potion_effects:
        speed:
          level: 4
          duration: -1
        regeneration:
          level: 1
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 电流攻击配置
        electric_attack_enabled: true # 启用电流攻击
        electric_damage: 4.0          # 电流伤害
        electric_range: 10            # 电流范围（10格）
        electric_interval: 60         # 电流间隔（3秒）

        # 传送技能配置
        teleport_enabled: true        # 启用传送技能
        teleport_interval: 8000       # 传送间隔（8秒）
        teleport_range: 20            # 传送搜索范围（20格）

        # 分裂技能配置
        clone_enabled: true           # 启用分裂技能
        clone_interval: 25000         # 分裂间隔（25秒）
        max_clones: 5                 # 最大仆从数量

        # 粒子效果配置
        particle_enabled: true        # 启用持续粒子效果

    # IDC6 - 变异蜘蛛
    idc6:
      enabled: true
      health_override: 300.0
      damage_override: 8.0
      custom_name_override: "§3§l变异蜘蛛"
      entity_type_override: "CAVE_SPIDER"

      # 药水效果配置
      potion_effects:
        # 永久速度4效果
        speed:
          level: 3
          duration: -1
        # 永久力量3效果
        strength:
          level: 2
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 蜘蛛网生成配置
        web_generation_enabled: true  # 启用蜘蛛网生成
        web_generation_interval: 10000 # 生成间隔（10秒）
        web_generation_count: 5       # 每次生成数量
        web_generation_duration: 600  # 蜘蛛网持续时间（30秒，600tick）

        # 毒液喷射配置
        poison_spray_enabled: true    # 启用毒液喷射
        poison_spray_interval: 15000  # 喷射间隔（15秒）
        poison_spray_range: 10        # 喷射范围（10格）
        poison_level: 4               # 中毒等级V（0-based，所以是4）
        poison_duration: 600          # 中毒持续时间（30秒）

        # 跳跃攻击配置
        jump_attack_enabled: true     # 启用跳跃攻击
        jump_attack_interval: 5000    # 跳跃间隔（5秒）
        jump_attack_range: 15         # 跳跃搜索范围（15格）

        # 蜘蛛网攻击配置
        web_attack_enabled: true      # 启用蜘蛛网攻击
        web_attack_interval: 4000     # 攻击间隔（4秒）
        web_attack_range: 15          # 攻击范围（15格）
        web_attack_duration: 100      # 蜘蛛网持续时间（5秒，100tick）

        # 粒子效果配置
        particle_enabled: true        # 启用持续粒子效果

    # IDC7 - 灾厄卫道士
    idc7:
      enabled: true
      health_override: 540.0
      damage_override: 15.0
      custom_name_override: "§4§l灾厄卫道士"
      entity_type_override: "PILLAGER"

      # 药水效果配置
      potion_effects:
        # 永久抗性提升II效果
        resistance:
          level: 1
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 脚底暴击圆圈配置
        circle_attack_enabled: true   # 启用脚底暴击圆圈
        circle_radius: 3.0            # 圆圈半径（3格）
        circle_damage: 2.0            # 圆圈伤害（2点）

        # 召唤卫道士配置
        summon_enabled: true          # 启用召唤卫道士
        summon_interval: 5000         # 召唤间隔（5秒）
        max_minions: 10               # 最大仆从数量

        # 发射伤害球体配置
        projectile_enabled: true      # 启用发射伤害球体
        projectile_interval: 4000     # 发射间隔（4秒）
        projectile_damage: 10.0       # 球体伤害（10点）

    # IDC8 - 灾厄唤魔者
    idc8:
      enabled: true
      health_override: 600.0
      damage_override: 12.0
      custom_name_override: "§5§l灾厄唤魔者"
      entity_type_override: "EVOKER"

      # 药水效果配置
      potion_effects:
        # 永久速度II效果
        speed:
          level: 1
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 脚底粒子圆圈配置
        circle_enabled: true          # 启用脚底粒子圆圈
        circle_radius: 2.0            # 圆圈半径（2格）

        # 召唤卫道士配置
        summon_enabled: true          # 启用召唤卫道士
        summon_interval: 10000        # 召唤间隔（10秒）
        max_minions: 10               # 最大仆从数量

        # DNA螺旋魔法攻击配置
        magic_attack_enabled: true
        magic_attack_interval: 2000   # 攻击间隔（2秒）

        # 圈式内收缩尖牙攻击配置
        fang_circle_enabled: true     # 启用圈式内收缩尖牙攻击
        fang_circle_interval: 8000    # 攻击间隔（8秒）

    # IDC9 - 灾厄劫掠兽
    idc9:
      enabled: true
      health_override: 1000.0
      damage_override: 20.0
      custom_name_override: "§4§l灾厄劫掠兽"
      entity_type_override: "RAVAGER"

      # 药水效果配置
      potion_effects:
        # 永久跳跃提升III效果
        jump_boost:
          level: 2
          duration: -1
        # 永久速度I效果
        speed:
          level: 0
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 脚底暴击圆圈配置
        circle_attack_enabled: true   # 启用脚底暴击圆圈
        circle_radius: 3.0            # 圆圈半径（3格）
        circle_damage: 2.0            # 圆圈伤害（2点）
        knockback_strength: 8.0       # 击退强度（8格）

    # IDC10 - 变异僵尸马
    idc10:
      enabled: true
      health_override: 300.0
      damage_override: 10.0
      custom_name_override: "§8§l变异僵尸马"
      entity_type_override: "ZOMBIE_HORSE" # 覆盖实体类型为僵尸马

      # 药水效果配置
      potion_effects:
        # 永久速度V效果
        speed:
          level: 4
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 撞击伤害配置
        collision_damage_enabled: true # 启用撞击伤害
        collision_damage: 8.0         # 撞击伤害（8点）
        knockback_strength: 1.5       # 击退强度（1.5格）

        # 召唤武装僵尸配置
        summon_zombies_enabled: true  # 启用召唤武装僵尸
        summon_interval: 10000        # 召唤间隔（10秒）
        max_zombies: 4                # 最大僵尸数量

        # 持续粒子效果配置
        particle_enabled: true        # 启用持续粒子效果

        # 智能追踪移动配置
        smart_movement_enabled: true  # 启用智能追踪移动
        tracking_range: 30.0          # 追踪范围（30格）

        # 飞行能力配置
        flying_enabled: true          # 启用飞行能力
        flying_range: 15.0            # 飞行范围（15格）

        # 视线跟踪配置
        look_at_player_enabled: true  # 启用视线跟踪（让马始终面向玩家）
        max_turn_speed: 30.0          # 最大转向速度（度/tick）

    # IDC11 - 变异岩浆怪
    idc11:
      enabled: true
      health_override: 100.0
      damage_override: 8.0
      custom_name_override: "§6§l变异岩浆怪"
      entity_type_override: "MAGMA_CUBE" # 覆盖实体类型为岩浆怪

      # 药水效果配置
      potion_effects:
        # 永久速度III效果
        speed:
          level: 2
          duration: -1

      # 特殊能力配置
      special_abilities:
        # 岩浆怪尺寸配置
        size: 3                       # 岩浆怪尺寸（3为大尺寸）

        # 火焰粒子环配置
        flame_ring_enabled: true      # 启用火焰粒子环
        ring_radius: 2.5              # 粒子环半径（2.5格）
        ring_damage: 2.0              # 粒子环伤害（2点）
        fire_duration: 60             # 着火持续时间（60tick=3秒）

    # IDC12 - 变异尸壳
    idc12:
      enabled: true
      health_override: 100.0
      damage_override: 6.0
      custom_name_override: "§7§l变异尸壳"
      entity_type_override: "HUSK"

      # 武器配置
      weapon_override: "IRON_SWORD"
      weapon_enchantments:
        sharpness: 1

      # 护甲配置
      armor_overrides:
        chestplate: "LEATHER_CHESTPLATE" # 粉色皮革胸甲
        chestplate_color: "FUCHSIA"   # 粉色
        chestplate_enchantments:
          protection: 10

      # 药水效果配置
      potion_effects:
        # 永久速度II效果
        speed:
          level: 1
          duration: -1
        # 永久跳跃提升II效果
        jump_boost:
          level: 1
          duration: -1

      # 技能冷却时间配置
      skill_cooldown_overrides:
        particle_interval: 10         # 药水粒子效果间隔（10tick=0.5秒）
        summon_interval: 160          # 召唤强化僵尸间隔（160tick=8秒）
        max_nearby_zombies: 20        # 最大周围僵尸数量限制
        summon_count: 6               # 每次召唤僵尸数量

      # 特殊能力配置
      special_abilities:
        # 药水粒子效果配置
        particle_enabled: true        # 启用药水粒子效果

        # 召唤强化僵尸配置
        summon_enabled: true          # 启用召唤强化僵尸

        # 混乱物品栏攻击配置
        shuffle_enabled: true         # 启用混乱物品栏攻击（攻击玩家时触发）

    # IDC13 - 变异僵尸3
    idc13:
      enabled: true
      health_override: 200.0
      damage_override: 8.0
      custom_name_override: "§3变异僵尸3"
      entity_type_override: "STRAY"

      # 武器配置
      weapon_override: "BOW"          # 覆盖武器为弓
      weapon_enchantments:
        sharpness: 3                  # 锋利III附魔
        punch: 1                      # 冲击I附魔

      # 护甲配置
      armor_overrides:
        boots: "LEATHER_BOOTS"        # 黑色皮革靴子
        boots_color: "BLACK"          # 黑色
        boots_enchantments:
          protection: 2               # 保护II附魔

      # 药水效果配置
      potion_effects:
        # 永久速度II效果
        speed:
          level: 1
          duration: -1

      # 技能冷却时间配置
      skill_cooldown_overrides:
        arrow_attack_interval: 100    # 三连射箭攻击间隔（100tick=5秒）
        frost_ring_interval: 160      # 冰霜粒子环间隔（160tick=8秒）
        arrow_count: 3                # 每次射箭数量
        attack_range: 20.0            # 攻击范围（20格）
        frost_ring_radius: 5.0        # 冰霜环半径（5格）
        arrow_damage: 6.0             # 箭矢伤害
        slowness_duration: 100        # 缓慢效果持续时间（100tick=5秒）

      # 特殊能力配置
      special_abilities:
        # 三连射箭攻击配置
        arrow_attack_enabled: true    # 启用三连射箭攻击

        # 冰霜粒子环配置
        frost_ring_enabled: true      # 启用冰霜粒子环攻击

    # IDC14 - 变异僵尸4
    idc14:
      enabled: true
      health_override: 150.0
      damage_override: 10.0
      custom_name_override: "§8变异僵尸4"
      entity_type_override: "WITHER_SKELETON"

      # 武器配置
      weapon_override: "NETHERITE_SWORD"
      weapon_enchantments:
        sharpness: 5

      # 护甲配置
      armor_overrides:
        helmet: "LEATHER_HELMET"      # 黑色皮革头盔
        helmet_color: "BLACK"         # 黑色
        helmet_enchantments:
          protection: 10

        chestplate: "LEATHER_CHESTPLATE"
        chestplate_color: "BLACK"     # 黑色
        chestplate_enchantments:
          protection: 10

        leggings: "LEATHER_LEGGINGS"
        leggings_color: "BLACK"
        leggings_enchantments:
          protection: 10

        boots: "LEATHER_BOOTS"        # 黑色皮革靴子
        boots_color: "BLACK"          # 黑色
        boots_enchantments:
          protection: 10              # 保护X附魔

      # 药水效果配置
      potion_effects:
        # 永久速度II效果
        speed:
          level: 1
          duration: -1

      # 技能冷却时间配置
      skill_cooldown_overrides:
        machine_gun_interval: 20      # 机枪射击间隔（20tick=1秒）
        particle_interval: 10         # 烟雾粒子间隔（10tick=0.5秒）
        dash_interval: 200            # 冲刺攻击间隔（200tick=10秒）
        machine_gun_range: 30         # 机枪射击范围（30格）
        dash_range: 15                # 冲刺攻击范围（15格）
        bullet_count: 8               # 每次射击子弹数量
        bullet_damage: 5              # 每颗子弹伤害
        dash_damage: 8                # 冲刺攻击伤害
        spread_angle: 5               # 子弹散射角度

      # 特殊能力配置
      special_abilities:
        # 机枪射击配置
        machine_gun_enabled: true     # 启用机枪射击

        # 烟雾粒子环配置
        smoke_particles_enabled: true # 启用烟雾粒子效果

        # 冲刺攻击配置
        dash_attack_enabled: true     # 启用冲刺攻击

    # IDC15 - 鲜血猪灵
    idc15:
      enabled: true
      health_override: 100.0
      damage_override: 8.0
      custom_name_override: "§c鲜血猪灵"
      entity_type_override: "PIGLIN"

      # 武器配置
      weapon_override: "NETHERITE_SWORD"
      weapon_enchantments:
        sharpness: 2
      weapon_display_name: "§c鲜血之剑"

      # 护甲配置
      armor_overrides:
        helmet: "LEATHER_HELMET"      # 红色皮革头盔
        helmet_color: "RED"           # 红色
        helmet_enchantments:
          protection: 20              # 保护XX附魔
        helmet_display_name: "§c鲜血头盔"

        chestplate: "LEATHER_CHESTPLATE"
        chestplate_color: "RED"
        chestplate_enchantments:
          protection: 20
        chestplate_display_name: "§c鲜血胸甲"

        leggings: "LEATHER_LEGGINGS"  # 红色皮革护腿
        leggings_color: "RED"         # 红色
        leggings_enchantments:
          protection: 20              # 保护XX附魔
        leggings_display_name: "§c鲜血护腿"

        boots: "LEATHER_BOOTS"        # 红色皮革靴子
        boots_color: "RED"            # 红色
        boots_enchantments:
          protection: 20              # 保护XX附魔
        boots_display_name: "§c鲜血靴子"

      # 技能冷却时间配置
      skill_cooldown_overrides:
        particle_ball_interval: 20    # 红色粒子球间隔（20tick=1秒）
        explosion_detection_range: 5  # 爆炸检测范围（5格）
        explosion_damage: 8           # 爆炸伤害
        explosion_range: 2            # 爆炸范围（2格）
        particle_ball_radius: 1.5     # 粒子球半径（1.5格）
        projectile_speed: 0.5         # 爆炸弹飞行速度
        projectile_max_steps: 20      # 爆炸弹最大飞行步数

      # 特殊能力配置
      special_abilities:
        # 红色粒子球配置
        particle_ball_enabled: true   # 启用红色粒子球效果

        # 爆炸攻击配置
        explosion_attack_enabled: true # 启用爆炸攻击

        # 攻击回血配置
        attack_heal_enabled: true     # 启用攻击回血
        attack_heal_percentage: 10    # 攻击回血百分比（10%最大生命值）

        # 猪灵特殊配置
        immune_to_zombification: true # 免疫僵尸化

    # IDC16 - 暗影潜影贝
    idc16:
      enabled: true
      health_override: 50.0
      damage_override: 6.0
      custom_name_override: "§8暗影潜影贝"
      entity_type_override: "SHULKER" # 覆盖实体类型为潜影贝

      # 潜影贝特殊配置
      shulker_color: "BLACK"          # 潜影贝颜色为黑色

      # 药水效果配置
      potion_effects:
        # 隐身效果（1分钟）
        invisibility:
          level: 0                    # 隐身等级I（0-based，所以是0）
          duration: 1200              # 持续时间（1200tick=1分钟）

      # 技能冷却时间配置
      skill_cooldown_overrides:
        bullet_attack_interval: 60    # 三连射子弹攻击间隔（60tick=3秒）
        damage_check_interval: 20     # 受伤检测间隔（20tick=1秒）
        bullet_count: 3               # 每次射击子弹数量
        attack_range: 10.0            # 攻击范围（10格）
        shuffle_range: 10.0           # 混乱物品栏范围（10格）
        shuffle_cooldown: 5000        # 混乱物品栏冷却时间（5000ms=5秒）
        health_threshold: 0.9         # 触发混乱的血量阈值（90%）
        invisibility_duration: 1200  # 重新隐身持续时间（1200tick=1分钟）

      # 特殊能力配置
      special_abilities:
        # 三连射子弹攻击配置
        bullet_attack_enabled: true   # 启用三连射子弹攻击

        # 受伤混乱物品栏配置
        damage_shuffle_enabled: true  # 启用受伤混乱物品栏

        # 消息发送配置
        shuffle_message_enabled: false # 禁用混乱物品栏消息（避免刷屏）

    # IDC17 - 变异雪傀儡
    idc17:
      enabled: true
      health_override: 200.0
      damage_override: 4.0
      custom_name_override: "§f变异雪傀儡"
      entity_type_override: "SNOW_GOLEM"   # 覆盖实体类型为雪傀儡

      # 药水效果配置
      potion_effects:
        # 永久速度V效果
        speed:
          level: 4
          duration: -1

      # 技能冷却时间配置
      skill_cooldown_overrides:
        tracking_interval: 10         # 追踪玩家间隔（10tick=0.5秒）
        snowball_attack_interval: 40  # 雪球攻击间隔（40tick=2秒）
        tracking_range: 20.0          # 追踪范围（20格）
        snowball_range: 15.0          # 雪球攻击范围（15格）
        melee_range: 3.0              # 近战攻击范围（3格）
        melee_damage: 4.0             # 近战攻击伤害
        snowball_count: 8             # 每次雪球数量
        snowball_interval: 3          # 雪球发射间隔（3tick=0.15秒）
        spread_factor: 0.1            # 雪球散射因子
        snowball_speed: 1.5           # 雪球飞行速度

      # 特殊能力配置
      special_abilities:
        # 主动追踪玩家配置
        player_tracking_enabled: true # 启用主动追踪玩家

        # 连发雪球攻击配置
        snowball_attack_enabled: true # 启用连发雪球攻击

        # 毒性雪球配置
        poison_snowball_enabled: true # 启用毒性雪球效果

    # IDC18 - 变异铁傀儡
    idc18:
      enabled: true
      health_override: 2000.0
      damage_override: 15.0
      custom_name_override: "§8变异铁傀儡"
      entity_type_override: "IRON_GOLEM"   # 覆盖实体类型为铁傀儡

      # 药水效果配置
      potion_effects:
        # 永久速度IV效果
        speed:
          level: 3                    # 速度等级IV（0-based，所以是3）
          duration: -1                # 持续时间（-1表示永久）
        # 永久跳跃提升IV效果
        jump_boost:
          level: 3                    # 跳跃提升等级IV（0-based，所以是3）
          duration: -1                # 持续时间（-1表示永久）

      # 技能冷却时间配置
      skill_cooldown_overrides:
        tracking_interval: 5          # 追踪玩家间隔（5tick=0.25秒）
        sonic_attack_interval: 80     # 声波攻击间隔（80tick=4秒）
        grass_attack_interval: 100    # 草方块攻击间隔（100tick=5秒）
        tracking_range: 30.0          # 追踪范围（30格）
        attack_range: 25.0            # 远程攻击范围（25格）
        melee_range: 4.0              # 近战攻击范围（4格）
        melee_damage: 15.0            # 近战攻击伤害
        knockback_strength: 2.0       # 击飞强度
        knockback_height: 0.8         # 击飞高度

      # 特殊能力配置
      special_abilities:
        # 主动追踪玩家配置
        player_tracking_enabled: true # 启用主动追踪玩家

        # 远程声波弹攻击配置
        sonic_attack_enabled: true    # 启用远程声波弹攻击

        # 草方块攻击配置
        grass_attack_enabled: true    # 启用草方块攻击

        # 敌对AI配置
        hostile_ai_enabled: true      # 启用敌对AI（只攻击玩家）

    # IDC19 - 变异僵尸Max
    idc19:
      enabled: true
      health_override: 2000.0
      damage_override: 10.0
      custom_name_override: "§b变异僵尸Max"
      entity_type_override: "DROWNED"

      # 装备配置
      equipment:
        main_hand:
          material: "TRIDENT"
          display_name: "§b雷霆三叉戟"
          enchantments:
            channeling: 20            # 引雷20级
            loyalty: 10               # 忠诚10级
            piercing: 4               # 穿透4级
          drop_chance: 0.0            # 不掉落

      # 药水效果配置
      potion_effects:
        # 永久速度IX效果
        speed:
          level: 8                    # 速度等级IX（0-based，所以是8）
          duration: -1                # 持续时间（-1表示永久）
        # 永久跳跃提升III效果
        jump_boost:
          level: 2                    # 跳跃提升等级III（0-based，所以是2）
          duration: -1                # 持续时间（-1表示永久）

      # 技能冷却时间配置
      skill_cooldown_overrides:
        weather_control_interval: 1200  # 天气控制间隔（1200tick=1分钟）
        particle_effect_interval: 10    # 粒子效果间隔（10tick=0.5秒）
        trident_attack_interval: 20     # 三叉戟攻击间隔（20tick=1秒）
        invisibility_interval: 100     # 隐身间隔（100tick=5秒）
        teleport_interval: 40          # 传送间隔（40tick=2秒）
        particle_range: 15.0           # 粒子攻击范围（15格）
        trident_range: 20.0            # 三叉戟攻击范围（20格）
        teleport_range: 20.0           # 传送检测范围（20格）
        teleport_distance_min: 3.0     # 传送最小距离（3格）
        teleport_distance_max: 5.0     # 传送最大距离（5格）
        particle_damage: 8.0           # 粒子攻击伤害
        trident_damage: 10.0           # 三叉戟攻击伤害
        invisibility_duration: 60     # 隐身持续时间（60tick=3秒）

      # 特殊能力配置
      special_abilities:
        # 天气控制配置
        weather_control_enabled: true # 启用天气控制

        # 蓝色粒子环绕+发射配置
        particle_attack_enabled: true # 启用粒子攻击

        # 三叉戟发射+闪电攻击配置
        trident_attack_enabled: true  # 启用三叉戟攻击
        lightning_attack_enabled: true # 启用闪电攻击

        # 隐身技能配置
        invisibility_enabled: true    # 启用隐身技能

        # 传送能力配置
        teleport_enabled: true        # 启用传送能力

    # IDC20 - 灵魂坚守者
    idc20:
      enabled: true
      health_override: 1000.0
      damage_override: 15.0
      custom_name_override: "§5灵魂坚守者"
      entity_type_override: "WARDEN"

      # 药水效果配置
      potion_effects:
        # 永久速度II效果
        speed:
          level: 1                    # 速度等级II（0-based，所以是1）
          duration: -1                # 持续时间（-1表示永久）
        # 永久额外生命30级效果
        health_boost:
          level: 29                   # 额外生命等级30（0-based，所以是29）
          duration: -1                # 持续时间（-1表示永久）
        # 永久伤害吸收30级效果
        absorption:
          level: 29                   # 伤害吸收等级30（0-based，所以是29）
          duration: -1                # 持续时间（-1表示永久）

      # 技能冷却时间配置
      skill_cooldown_overrides:
        tracking_interval: 5          # 追踪玩家间隔（5tick=0.25秒）
        sonic_attack_interval: 60     # 声波攻击间隔（60tick=3秒）
        obsidian_attack_interval: 40  # 黑曜石攻击间隔（40tick=2秒）
        knockup_attack_interval: 60   # 击飞攻击间隔（60tick=3秒）
        tracking_range: 30.0          # 追踪范围（30格）
        sonic_range: 30.0             # 声波攻击范围（30格）
        obsidian_range: 30.0          # 黑曜石攻击范围（30格）
        knockup_range: 15.0           # 击飞攻击范围（15格）
        melee_range: 4.0              # 近战攻击范围（4格）
        melee_damage: 15.0            # 近战攻击伤害
        sonic_damage: 20.0            # 声波攻击伤害
        obsidian_damage: 25.0         # 黑曜石攻击伤害
        knockup_strength: 5.0         # 击飞强度（5格高度）

      # 特殊能力配置
      special_abilities:
        # 主动追踪玩家配置
        player_tracking_enabled: true # 启用主动追踪玩家

        # 声波弹攻击配置
        sonic_attack_enabled: true    # 启用声波弹攻击

        # 黑曜石方块攻击配置
        obsidian_attack_enabled: true # 启用黑曜石方块攻击

        # 向上击飞+黑曜石柱配置
        knockup_attack_enabled: true  # 启用击飞攻击

        # 敌对AI配置
        hostile_ai_enabled: true      # 启用敌对AI（只攻击玩家）

    # IDC21 - 凋零领主
    idc21:
      enabled: true
      health_override: 6999.0
      damage_override: 10.0
      custom_name_override: "§5凋零领主"
      entity_type_override: "WITHER"

      # 技能冷却时间配置
      skill_cooldown_overrides:
        obsidian_attack_interval: 80  # 黑曜石攻击间隔（80tick=4秒）
        skull_attack_interval: 30    # 凋零头颅攻击间隔（30tick=1.5秒）
        fence_attack_interval: 100   # 下界栅栏攻击间隔（100tick=5秒）
        attack_range: 30.0            # 攻击范围（30格）
        obsidian_damage: 10.0         # 黑曜石攻击伤害
        fence_damage: 5.0             # 下界栅栏攻击伤害
        skull_speed_multiplier: 2.0   # 凋零头颅速度倍数
        fence_slowness_level: 2       # 下界栅栏减速等级（减速3）
        fence_slowness_duration: 60  # 下界栅栏减速持续时间（60tick=3秒）
        obsidian_max_distance: 25     # 黑曜石攻击最大距离
        fence_ray_length: 15          # 下界栅栏射线长度
        fence_restore_delay: 40       # 下界栅栏恢复延迟（40tick=2秒）

      # 特殊能力配置
      special_abilities:
        # 黑曜石方块攻击配置
        obsidian_attack_enabled: true # 启用黑曜石方块攻击

        # 高速凋零头颅攻击配置
        skull_attack_enabled: true    # 启用凋零头颅攻击

        # 下界栅栏攻击配置
        fence_attack_enabled: true    # 启用下界栅栏攻击

        # 敌对AI配置
        hostile_ai_enabled: true      # 启用敌对AI（只攻击玩家）

    # IDC22 - 异变之王
    idc22:
      enabled: true
      health_override: 10999.0
      damage_override: 20.0
      custom_name_override: "§4异变之王"
      entity_type_override: "ENDER_DRAGON"  # 覆盖实体类型为末影龙

      # 药水效果配置
      potion_effects:
        # 永久速度III效果
        speed:
          level: 2                    # 速度等级III（0-based，所以是2）
          duration: -1                # 持续时间（-1表示永久）

      # 技能冷却时间配置
      skill_cooldown_overrides:
        crystal_attack_cooldown: 15000  # 末影水晶攻击冷却（15秒）
        breath_attack_cooldown: 10000   # 龙息攻击冷却（10秒）
        ender_field_cooldown: 20000     # 末影粒子场冷却（20秒）
        summon_cooldown: 4000           # 召唤变异生物冷却（4秒）
        obsidian_attack_cooldown: 5000  # 黑曜石柱攻击冷却（5秒）
        nether_fence_cooldown: 6000     # 下界栅栏攻击冷却（6秒）
        movement_interval: 5            # 移动AI间隔（5tick=0.25秒）
        attack_interval: 10             # 近战攻击间隔（10tick=0.5秒）

        # 基础伤害配置
        melee_damage: 20.0              # 近战攻击伤害
        crystal_damage: 15.0            # 末影水晶子弹伤害
        breath_damage: 12.0             # 龙息伤害
        obsidian_pillar_damage: 18.0    # 黑曜石柱伤害
        obsidian_blocks_damage: 12.0    # 黑曜石快连续攻击伤害
        fence_damage: 10.0              # 下界栅栏伤害
        ender_field_radius: 10.0        # 末影粒子场半径
        ender_field_duration: 200       # 末影粒子场持续时间（200tick=10秒）

        # 黑曜石柱攻击配置
        obsidian_pillar_count: 3        # 黑曜石柱数量（默认3个）
        obsidian_pillar_growth_time: 160 # 黑曜石柱生长时间（160tick=8秒）
        obsidian_pillar_growth_interval: 2 # 黑曜石柱生长间隔（2tick=0.1秒）
        obsidian_pillar_random_range: 8  # 随机柱子位置范围（8格）

        # 黑曜石快连续攻击配置（真实发射方块）
        obsidian_blocks_cooldown: 8000      # 黑曜石块攻击冷却时间（8秒）
        obsidian_blocks_waves: 5            # 黑曜石快攻击波数（5波）
        obsidian_blocks_per_wave: 7         # 每波黑曜石方块数量（5-8个随机）
        obsidian_blocks_wave_interval: 40   # 波次间隔（40tick=2秒）
        obsidian_blocks_cleanup_delay: 0    # 黑曜石方块清理延迟（0=立即清理，与原版一致；>0=延迟清理）
        obsidian_blocks_instant_cleanup: true # 是否立即清理落地的黑曜石（true=原版行为，false=延迟清理）
        obsidian_blocks_trigger_chance: 0.33 # 黑曜石方块攻击触发几率（33%）
        obsidian_blocks_speed: 15           # 黑曜石方块发射速度（15=1.5倍速度）
        obsidian_blocks_spread: 6           # 黑曜石方块散布范围（6格）

        # 下界栅栏攻击配置
        fence_ray_count: 5              # 下界栅栏射线数量（5条）
        fence_ray_angles: [0, 36, -36, 72, -72] # 下界栅栏射线角度（度）
        fence_ray_max_distance: 25      # 下界栅栏射线最大距离（25格）
        fence_ray_start_delay: 5        # 下界栅栏射线启动延迟（5tick=0.25秒）
        fence_cleanup_delay: 60         # 下界栅栏清理延迟（60tick=3秒）
        fence_slowness_level: 2         # 下界栅栏减速等级（减速3）
        fence_slowness_duration: 60     # 下界栅栏减速持续时间（60tick=3秒）

        # 末影水晶攻击配置
        crystal_orbital_count: 3        # 环绕水晶数量（3个）
        crystal_orbital_radius: 3.0     # 环绕水晶半径（3格）
        crystal_orbital_lifetime: 200   # 环绕水晶生命周期（200tick=10秒）
        crystal_bullet_interval: 20     # 水晶子弹发射间隔（20tick=1秒）
        crystal_heal_amount: 50.0       # 水晶治疗量（50点生命值）
        crystal_heal_interval: 40       # 水晶治疗间隔（40tick=2秒）

      # 特殊能力配置
      special_abilities:
        # 智能移动AI配置
        movement_ai_enabled: true     # 启用智能移动AI

        # 近战攻击配置
        melee_attack_enabled: true    # 启用近战攻击

        # 末影水晶攻击配置
        crystal_attack_enabled: true  # 启用末影水晶攻击

        # 龙息攻击配置
        breath_attack_enabled: true   # 启用龙息攻击

        # 黑曜石攻击配置（包含两种攻击）
        obsidian_attack_enabled: true # 启用黑曜石攻击（柱攻击+快连续攻击）

        # 下界栅栏攻击配置
        fence_attack_enabled: true    # 启用下界栅栏攻击

        # 末影粒子减速场配置
        ender_field_enabled: true     # 启用末影粒子减速场

        # 召唤变异生物配置
        summon_enabled: true          # 启用召唤变异生物

        # Boss血条配置
        boss_bar_enabled: true        # 启用Boss血条

        # 敌对AI配置
        hostile_ai_enabled: true      # 启用敌对AI（只攻击玩家）

# ========================================
# 性能优化配置（Beta）
# ========================================
performance:
  #最大同时存在的用户自定义实体数量
  max_user_custom_entities: 50

  # 实体技能处理间隔（tick）
  skill_processing_interval: 20

  #实体生成间隔限制（毫秒）
  entity_spawn_cooldown: 100

  #是否启用配置缓存
  enable_config_cache: true

  #配置文件重载间隔（秒）
  config_reload_interval: 300

# ========================================
# 兼容性配置(beta)
# ========================================
compatibility:
  # 与原有IDC系统的兼容模式
  legacy_compatibility: true

  # 是否保持原有的元数据标记
  preserve_original_metadata: true

  # 是否保持原有的命名规则
  preserve_original_naming: true

  # 是否启用实体类型回退
  enable_entity_type_fallback: true

  # 是否启用版本兼容检查
  enable_version_check: true

  #支持的最低Bukkit版本
  min_bukkit_version: "1.21"

# ========================================
# 🐛 调试和日志配置(beta)
# ========================================
debug:
  #是否记录实体生成日志
  log_entity_spawn: true

  # 是否记录配置加载日志
  log_config_loading: true

  # 是否记录技能执行日志
  log_skill_execution: true

  # 是否记录错误详情
  log_error_details: true

  # 日志级别
  # "INFO": 记录所有信息（推荐用于调试）
  # "WARNING": 只记录警告和错误
  # "SEVERE": 只记录严重错误
  log_level: "SEVERE"

  # 是否启用性能监控
  enable_performance_monitoring: false

  # 性能监控间隔（秒）
  performance_monitor_interval: 60

