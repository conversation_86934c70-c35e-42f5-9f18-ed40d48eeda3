// 使用Vue 3的Composition API
const { createApp, ref, reactive, computed, onMounted } = Vue;

// 调试日志函数
function debugLog(message) {
    const debugInfo = document.getElementById('debug-info');
    if (debugInfo) {
        const logItem = document.createElement('div');
        logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        debugInfo.appendChild(logItem);
        debugInfo.scrollTop = debugInfo.scrollHeight;
    }
    console.log(`[DEBUG] ${message}`);
}

// 错误显示函数
function showError(title, error) {
    console.error(title, error);
    alert(`${title}: ${error.message || error}`);
}

// 预定义的怪物类型和描述
const MONSTER_PRESETS = {
    zombies: [
        { id: 'id1', name: '普通僵尸', description: '无特殊功能。20点血，手持木斧，无防具' },
        { id: 'id2', name: '小僵尸', description: '速度2buff。10点血，手持木棍，无防具' },
        { id: 'id3', name: '路障僵尸', description: '无特殊功能，30点血，手持木斧，带着保护附魔1的铁头盔' },
        { id: 'id4', name: '钻斧僵尸', description: '无特殊功能，20点血，手持钻石斧，带着保护附魔1的铁护腿' },
        { id: 'id5', name: '剧毒僵尸', description: '特殊功能:被剧毒僵尸攻击后获得剧毒1buff效果，持续三秒，10点血，穿全身皮革套' },
        { id: 'id6', name: '双生僵尸', description: '特殊功能:每5分钟生成一个普通僵尸，20点血，手持铁剑，穿着铁胸甲' },
        { id: 'id7', name: '骷髅僵尸', description: '特殊功能:可以向玩家发射箭矢，30点血，手持钻石剑，带着铁护腿，穿着铁盔甲' },
        { id: 'id8', name: '武装僵尸', description: '无特殊功能，100点血，手持钻石剑，带着铁套，自带速度1buff' },
        { id: 'id9', name: '肥胖僵尸', description: '无特殊功能，100点血，无武器，无装备，体型是普通僵尸的两倍' },
        { id: 'id10', name: '法师僵尸', description: '特殊功能:每10秒召唤两个双生僵尸，50点血，手持木斧，装备全套连锁套' },
        { id: 'id11', name: '自爆僵尸', description: '特殊功能:靠近玩家后发生自爆，30点血，无武器，带着保护附魔1的皮革头盔' },
        { id: 'id12', name: '毒箭僵尸', description: '特殊功能:每隔3秒射出剧毒箭矢，50点血，手持弓，带着铁护腿，穿着铁盔甲' },
        { id: 'id13', name: '电击僵尸', description: '特殊功能:每3秒对自己10*10的范围内释放电流，50点血，手持木棍，带着铁套' },
        { id: 'id14', name: '冰冻僵尸', description: '特殊功能:每3秒对自己10*10的范围内冻结，70点血，手持钻石，带着锁链套' },
        { id: 'id15', name: '暗影僵尸', description: '特殊功能：隐身效果和隐匿攻击，60点血，手持铁剑，佩戴暗影头盔' },
        { id: 'id16', name: '毁灭僵尸', description: '特殊功能：力量效果持续增加攻击力，150点血，手持钻石剑，全套铁护甲' },
        { id: 'id17', name: '雷霆僵尸', description: '特殊功能：雷电攻击，80点血，手持弓，全套链甲' },
        { id: 'id18', name: '变异科学家', description: '特殊功能：多种高级能力，600点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲' },
        { id: 'id19', name: '变异法师', description: '特殊功能：闪电攻击、失明减速效果和召唤能力，400点血，木棍，全套附魔保护三皮革甲' },
        { id: 'id20', name: '气球僵尸', description: '特殊功能：持续漂浮效果，25点血，手持气球棒' },
        { id: 'id21', name: '迷雾僵尸', description: '特殊功能：生成迷雾降低玩家视野和移动速度，40点血，手持迷雾生成器' },
        { id: 'id22', name: '变异雷霆僵尸', description: '特殊功能：多种强力雷电和召唤能力，800点血，手持弓，全套附魔保护2锁链套' },
        { id: 'id23', name: '终极毁灭僵尸', description: '特殊功能：多种强力能力，1000点血，全套附魔装备' },
        { id: 'id24', name: '变异暗影僵尸', description: '特殊功能：高级隐身和攻击能力，500点血，手持锋利5钻石剑' },
        { id: 'id25', name: '变异博士', description: '终极BOSS：多种强力特殊能力，2048点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲' }
    ],
    entities: [
        { id: 'idc1', name: '变异僵尸01', description: '僵尸猪人形态，每次攻击对玩家施加剧毒效果，100点血，手持附魔锋利1铁剑，全套皮革套' },
        { id: 'idc2', name: '变异僵尸02', description: '骷髅形态，100点血，手持附魔冲击2的弓，带着苦力怕头颅' },
        { id: 'idc3', name: '变异烈焰人', description: '烈焰人形态，特殊能力：发射烈焰粒子和烈焰弹，100点血' },
        { id: 'idc4', name: '变异爬行者', description: '闪电苦力怕形态，特殊能力：闪电伤害，50点血' },
        { id: 'idc5', name: '变异末影螨', description: '末影螨形态，特殊能力：释放电流，30点血' },
        { id: 'idc6', name: '变异蜘蛛', description: '蜘蛛形态，特殊能力：发射烈焰弹，50点血' },
        { id: 'idc7', name: '灾厄卫道士', description: '掠夺者形态，特殊能力：每5秒召唤两个仆从，发射粒子球，540点血，手持下界合金斧' },
        { id: 'idc8', name: '灾厄唤魔者', description: '唤魔者形态，特殊能力：每10秒召唤两个掠夺者，释放DNA螺旋法术，600点血，速度2效果' },
        { id: 'idc9', name: '灾厄劫掠兽', description: '劫掠兽形态，特殊能力：强力冲撞和跳跃，1000点血，跳跃提升3和速度1效果' },
        { id: 'idc10', name: '变异僵尸马', description: '僵尸马形态，特殊能力：每10秒召唤4个武装僵尸，冲撞玩家，300点血，速度5效果' },
        { id: 'idc11', name: '变异岩浆怪', description: '岩浆怪形态，特殊能力：生成火焰粒子环，造成火焰伤害，100点血，速度3效果' },
        { id: 'idc12', name: '变异骷髅', description: '凋零骷髅形态，特殊功能：发射凋零箭，200点血，手持附魔弓' },
        { id: 'idc13', name: '变异僵尸3', description: '溺尸形态，特殊功能：每20秒召唤4个变异岩浆怪，200点血，穿着黑色皮革鞋子' },
        { id: 'idc14', name: '变异僵尸04', description: '僵尸形态，特殊功能：每15秒召唤2个变异僵尸01，150点血，手持铁剑' },
        { id: 'idc15', name: '鲜血猪灵', description: '猪灵形态，特殊功能：每次攻击造成流血效果，250点血，手持金剑' },
        { id: 'idc16', name: '暗影潜影贝', description: '潜影贝形态，特殊功能：隐身效果和打乱玩家物品栏，200点血' },
        { id: 'idc17', name: '变异雪傀儡', description: '雪傀儡形态，特殊功能：发射冰霜弹，造成减速效果，300点血' },
        { id: 'idc18', name: '变异铁傀儡', description: '铁傀儡形态，特殊功能：远程声波攻击和高速方块投射，2000点血' },
        { id: 'idc19', name: '变异僵尸Max', description: '僵尸形态，特殊功能：附魔三叉戟，瞬移能力和隐身周期，2000点血' },
        { id: 'idc20', name: '灵魂坚守者', description: '监守者形态，特殊功能：声波攻击和高速冲撞，1000点血' },
        { id: 'idc21', name: '凋零领主', description: '凋零形态，特殊功能：每4秒随机召唤变异生物，高速发射凋零头颅，黑曜石方块攻击和下界栅栏攻击，6999点血' },
        { id: 'idc22', name: '异变之王', description: '末影龙形态，特殊功能：末影人粒子场，黑曜石方块攻击，下界栅栏攻击，随机召唤怪物，10999点血，速度3效果' }
    ],
    npcs: [
        { id: 'idn1', name: '感染者1史蒂夫', description: '会自动攻击玩家直到自己死亡' },
        { id: 'idn2', name: '感染者2艾利克斯', description: '会自动攻击玩家直到自己死亡' },
        { id: 'idn3', name: '感染者农民', description: '特殊能力：死亡时召唤3个路障僵尸，50点血，武器：木锄，防具：皮革套' },
        { id: 'idn4', name: '感染者居民', description: '特殊能力：死亡时召唤3个普通僵尸，70点血，武器：木剑，防具：皮革套装' },
        { id: 'idn5', name: '感染猪', description: '特殊能力：死亡时对10*10范围内玩家造成剧毒II效果持续30秒，200点血' }
    ],
    idz: [] // 将通过API动态加载
};

// 加载IDZ怪物列表
async function loadIDZMonsters() {
    try {
        console.log('开始加载IDZ怪物列表...');
        const response = await fetchWithAuth('/api/idz-monsters');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.monsters) {
            // 更新MONSTER_PRESETS中的IDZ怪物列表
            MONSTER_PRESETS.idz = data.monsters.map(monster => ({
                id: monster.id,
                name: monster.name,
                description: monster.description
            }));

            console.log(`成功加载 ${data.monsters.length} 个IDZ怪物`);
            return true;
        } else {
            console.warn('IDZ怪物列表加载失败:', data.message || '未知错误');
            return false;
        }

    } catch (error) {
        console.error('加载IDZ怪物列表时发生错误:', error);
        return false;
    }
}

// 添加键盘快捷键显示调试面板
document.addEventListener('keydown', function(event) {
    // Ctrl+Shift+D 显示调试面板
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }
    }
});

debugLog('开始初始化Vue应用...');

createApp({
    setup() {
        console.log('Vue setup函数开始执行...');

        // 检查身份验证状态
        try {
            debugLog('检查身份验证...');

            // 检查auth.js是否已加载
            if (typeof checkAuth !== 'function') {
                showError('身份验证模块未加载', { message: 'auth.js文件可能未正确加载' });
                return;
            }

            if (!checkAuth()) {
                debugLog('身份验证失败，重定向到登录页面');
                return;
            }
            debugLog('身份验证成功');
        } catch (error) {
            showError('身份验证检查出错', error);
            return;
        }

        // 基础状态
        const loading = ref(true);
        const loadingMessage = ref('正在加载...');
        const presets = ref([]);
        const showCreateModal = ref(false);
        const showDetailsModal = ref(false);
        const selectedPreset = ref(null);
        const isEditing = ref(false);
        const editingPresetIndex = ref(-1);

        // 新预设状态
        const newPreset = reactive({
            name: '',
            description: '',
            monsters: []
        });

        // 当前编辑的怪物
        const currentMonster = reactive({
            monsterType: 'zombie',
            monsterId: 'id1',
            count: '5'
        });

        // 计算属性：是否可以保存预设
        const canSavePreset = computed(() => {
            return newPreset.name.trim() !== '' && newPreset.monsters.length > 0;
        });

        // 加载所有预设
        const loadPresets = async () => {
            loading.value = true;
            loadingMessage.value = '正在加载预设...';

            try {
                debugLog('正在加载预设...');
                const response = await fetchWithAuth('/api/monster-presets');
                const data = await response.json();

                if (data && data.presets) {
                    presets.value = data.presets;
                    debugLog(`成功加载 ${presets.value.length} 个预设`);
                } else {
                    debugLog('没有找到预设或返回数据格式不正确');
                    presets.value = []; // 确保是空数组而不是undefined
                }
            } catch (error) {
                console.error('加载预设出错:', error);
                showError('加载预设失败', error);
                presets.value = []; // 出错时确保是空数组
            } finally {
                loading.value = false;
            }
        };

        // 打开创建预设模态框
        const openCreatePresetModal = () => {
            // 重置表单
            newPreset.name = '';
            newPreset.description = '';
            newPreset.monsters = [];
            currentMonster.monsterType = 'zombie';
            currentMonster.monsterId = 'id1';
            currentMonster.count = '5';
            isEditing.value = false;
            editingPresetIndex.value = -1;
            showCreateModal.value = true;
        };

        // 打开编辑预设模态框
        const editPreset = (preset, index) => {
            try {
                // 验证参数
                if (!preset) {
                    console.error('编辑预设失败: 预设对象为空');
                    showError('编辑预设失败', { message: '预设对象为空' });
                    return;
                }

                if (index === undefined || index < 0) {
                    console.error('编辑预设失败: 无效的索引值', index);
                    showError('编辑预设失败', { message: '无效的索引值: ' + index });
                    return;
                }

                console.log('开始编辑预设:', preset.name, '索引:', index);

                // 填充表单
                newPreset.name = preset.name;
                newPreset.description = preset.description || '';

                // 确保monsters属性存在
                if (!preset.monsters || !Array.isArray(preset.monsters)) {
                    console.warn('预设没有怪物列表或格式不正确:', preset);
                    newPreset.monsters = [];
                } else {
                    newPreset.monsters = JSON.parse(JSON.stringify(preset.monsters)); // 深拷贝
                }

                currentMonster.monsterType = 'zombie';
                currentMonster.monsterId = 'id1';
                currentMonster.count = '5';
                isEditing.value = true;
                editingPresetIndex.value = index;
                showCreateModal.value = true;
            } catch (error) {
                console.error('编辑预设时发生错误:', error);
                showError('编辑预设失败', error);
            }
        };

        // 关闭创建预设模态框
        const closeCreateModal = () => {
            showCreateModal.value = false;
            isEditing.value = false;
            editingPresetIndex.value = -1;
        };

        // 添加怪物到预设
        const addMonsterToPreset = () => {
            newPreset.monsters.push({
                monsterType: currentMonster.monsterType,
                monsterId: currentMonster.monsterId,
                count: currentMonster.count
            });

            // 重置当前怪物
            currentMonster.monsterType = 'zombie';
            currentMonster.monsterId = 'id1';
            currentMonster.count = '5';
        };

        // 从预设中移除怪物
        const removeMonsterFromPreset = (index) => {
            newPreset.monsters.splice(index, 1);
        };

        // 验证预设名称是否包含不安全字符
        const validatePresetName = (name) => {
            // 检查是否包含Windows文件系统不允许的字符
            const invalidChars = /[<>:"|?*\\/]/;
            if (invalidChars.test(name)) {
                return {
                    valid: false,
                    message: '预设名称不能包含以下字符: < > : " | ? * \\ /'
                };
            }

            // 检查是否为空或只包含空格
            if (!name || name.trim().length === 0) {
                return {
                    valid: false,
                    message: '预设名称不能为空'
                };
            }

            // 检查长度
            if (name.length > 100) {
                return {
                    valid: false,
                    message: '预设名称不能超过100个字符'
                };
            }

            return { valid: true };
        };

        // 保存预设
        const savePreset = async () => {
            if (!canSavePreset.value) {
                alert('请填写预设名称并添加至少一种怪物');
                return;
            }

            // 验证预设名称
            const nameValidation = validatePresetName(newPreset.name);
            if (!nameValidation.valid) {
                alert(nameValidation.message);
                return;
            }

            loading.value = true;
            loadingMessage.value = isEditing.value ? '正在更新预设...' : '正在保存预设...';

            try {
                // 准备保存的数据
                const saveData = {
                    name: newPreset.name.trim(), // 去除首尾空格
                    description: newPreset.description,
                    monsters: newPreset.monsters
                };

                console.log('准备保存的预设数据:', saveData);

                // 如果是编辑模式，添加原始名称
                if (isEditing.value) {
                    // 添加错误处理和日志
                    if (editingPresetIndex.value < 0 || editingPresetIndex.value >= presets.value.length) {
                        console.error('编辑索引无效:', editingPresetIndex.value, '预设列表长度:', presets.value.length);
                        throw new Error('编辑索引无效: ' + editingPresetIndex.value);
                    }

                    const originalPreset = presets.value[editingPresetIndex.value];
                    if (!originalPreset || !originalPreset.name) {
                        console.error('无法获取原始预设名称:', originalPreset);
                        throw new Error('无法获取原始预设名称');
                    }

                    saveData.originalName = originalPreset.name;
                    console.log('正在更新预设:', saveData.originalName, '->', saveData.name);
                }

                // 发送到服务器
                const response = await fetchWithAuth('/api/monster-preset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(saveData)
                });

                console.log('服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const data = await response.json();
                console.log('服务器响应数据:', data);

                if (data && data.success) {
                    alert(isEditing.value ? '预设更新成功！' : '预设保存成功！');
                    // 关闭创建模态框
                    closeCreateModal();
                    // 重新加载预设列表
                    await loadPresets();
                } else {
                    const errorMessage = data.message || '未知错误';
                    console.error('保存失败，服务器返回:', data);
                    alert('保存失败: ' + errorMessage);
                }
            } catch (error) {
                console.error('保存预设出错:', error);

                // 提供更友好的错误信息
                let errorMessage = '保存预设失败';
                if (error.message.includes('401')) {
                    errorMessage = '身份验证失败，请重新登录';
                } else if (error.message.includes('400')) {
                    errorMessage = '请求数据格式错误，请检查输入内容';
                } else if (error.message.includes('500')) {
                    errorMessage = '服务器内部错误，请稍后重试';
                } else if (error.message.includes('网络')) {
                    errorMessage = '网络连接错误，请检查网络连接';
                } else {
                    errorMessage = '保存失败: ' + error.message;
                }

                alert(errorMessage);
                showError(isEditing.value ? '更新预设失败' : '保存预设失败', error);
            } finally {
                loading.value = false;
            }
        };

        // 查看预设详情
        const viewPresetDetails = (preset) => {
            selectedPreset.value = preset;
            showDetailsModal.value = true;
        };

        // 关闭预设详情模态框
        const closeDetailsModal = () => {
            showDetailsModal.value = false;
            selectedPreset.value = null;
        };

        // 确认删除预设
        const confirmDeletePreset = async (preset, index) => {
            if (!confirm(`确定要删除预设"${preset.name}"吗？`)) {
                return;
            }

            loading.value = true;
            loadingMessage.value = '正在删除预设...';

            try {
                const response = await fetchWithAuth(`/api/monster-preset?name=${encodeURIComponent(preset.name)}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data && data.success) {
                    alert('预设删除成功！');
                    // 从本地列表中移除
                    presets.value.splice(index, 1);
                } else {
                    alert('删除失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('删除预设出错:', error);
                showError('删除预设失败', error);
            } finally {
                loading.value = false;
            }
        };

        // 获取怪物选项
        const getMonsterOptions = (type) => {
            if (type === 'zombie') {
                return MONSTER_PRESETS.zombies;
            } else if (type === 'entity') {
                return MONSTER_PRESETS.entities;
            } else if (type === 'npc') {
                return MONSTER_PRESETS.npcs;
            } else if (type === 'idz') {
                // 如果IDZ列表为空，返回一个提示项
                if (MONSTER_PRESETS.idz.length === 0) {
                    return [{
                        id: '',
                        name: '暂无IDZ怪物，请先使用 /czm make create 创建',
                        description: '使用 /czm make create <怪物名称> 创建IDZ怪物'
                    }];
                }
                return MONSTER_PRESETS.idz;
            }
            return [];
        };

        // 获取怪物名称
        const getMonsterName = (type, id) => {
            const options = getMonsterOptions(type);
            const monster = options.find(m => m.id === id);
            return monster ? monster.name : `${type}:${id}`;
        };

        // 返回主界面
        const backToMain = () => {
            window.location.href = 'index.html';
        };

        // 退出登录
        const logout = () => {
            localStorage.removeItem('auth_token');
            window.location.href = 'login.html';
        };

        // 初始化
        onMounted(() => {
            loadPresets();
            loadIDZMonsters(); // 加载IDZ怪物列表
        });

        return {
            loading,
            loadingMessage,
            presets,
            showCreateModal,
            showDetailsModal,
            selectedPreset,
            newPreset,
            currentMonster,
            canSavePreset,
            isEditing,
            loadPresets,
            openCreatePresetModal,
            closeCreateModal,
            addMonsterToPreset,
            removeMonsterFromPreset,
            savePreset,
            viewPresetDetails,
            closeDetailsModal,
            confirmDeletePreset,
            editPreset,
            getMonsterOptions,
            getMonsterName,
            backToMain,
            logout
        };
    }
}).mount('#app');
