package org.Ver_zhzh.deathZombieV4.web.api;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.MonsterPresetManager;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

/**
 * 处理怪物预设相关的API请求
 */
public class MonsterPresetApiHandler implements HttpHandler {

    private final DeathZombieV4 plugin;
    private final MonsterPresetManager presetManager;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public MonsterPresetApiHandler(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.presetManager = new MonsterPresetManager(plugin);
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        // 获取请求方法和路径
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();
        String query = exchange.getRequestURI().getQuery();

        // 添加CORS头
        handleCorsHeaders(exchange);

        // 记录请求信息
        plugin.getLogger().info("处理怪物预设API请求: " + path + ", 方法: " + method + ", 查询: " + query);

        // 根据请求方法处理不同的操作
        if (path.equals("/api/monster-presets") && method.equalsIgnoreCase("GET")) {
            // 获取所有预设
            handleGetAllPresets(exchange);
        } else if (path.equals("/api/monster-preset") && method.equalsIgnoreCase("POST")) {
            // 创建新预设
            handleCreatePreset(exchange);
        } else if (path.equals("/api/monster-preset") && method.equalsIgnoreCase("DELETE")) {
            // 删除预设
            handleDeletePreset(exchange, query);
        } else if (path.equals("/api/idz-monsters") && method.equalsIgnoreCase("GET")) {
            // 获取所有IDZ怪物
            handleGetIDZMonsters(exchange);
        } else {
            // 未知路径，返回404
            plugin.getLogger().warning("未知的怪物预设API请求路径: " + path);
            sendResponse(exchange, 404, createErrorResponse("未找到请求的资源"));
        }
    }

    /**
     * 处理获取所有预设的请求
     */
    private void handleGetAllPresets(HttpExchange exchange) throws IOException {
        // 获取所有预设
        JSONArray presets = presetManager.getAllPresetsAsJson();

        // 创建响应
        JSONObject response = new JSONObject();
        response.put("success", true);
        response.put("presets", presets);

        // 发送响应
        sendResponse(exchange, 200, response.toJSONString());
    }

    /**
     * 处理创建或更新预设的请求
     */
    private void handleCreatePreset(HttpExchange exchange) throws IOException {
        // 读取请求体
        String requestBody = new BufferedReader(new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));

        try {
            // 记录请求体内容用于调试
            plugin.getLogger().info("收到预设保存请求，请求体: " + requestBody);

            // 解析JSON
            JSONParser parser = new JSONParser();
            JSONObject requestJson = (JSONObject) parser.parse(requestBody);

            // 获取预设信息
            String name = (String) requestJson.get("name");
            String description = (String) requestJson.get("description");
            JSONArray monstersArray = (JSONArray) requestJson.get("monsters");
            String originalName = (String) requestJson.get("originalName"); // 获取原始名称（如果是编辑操作）

            // 记录解析的数据
            plugin.getLogger().info("解析的预设数据 - 名称: " + name + ", 描述: " + description +
                                  ", 怪物数量: " + (monstersArray != null ? monstersArray.size() : 0) +
                                  ", 原始名称: " + originalName);

            // 验证必要字段
            if (name == null || name.isEmpty()) {
                plugin.getLogger().warning("预设名称为空或null");
                sendResponse(exchange, 400, createErrorResponse("预设名称不能为空"));
                return;
            }

            if (monstersArray == null || monstersArray.isEmpty()) {
                plugin.getLogger().warning("怪物列表为空或null");
                sendResponse(exchange, 400, createErrorResponse("预设必须包含至少一个怪物"));
                return;
            }

            // 转换怪物列表
            List<Map<String, String>> monsters = new ArrayList<>();
            for (Object obj : monstersArray) {
                JSONObject monsterObj = (JSONObject) obj;
                Map<String, String> monster = new HashMap<>();
                monster.put("monsterType", (String) monsterObj.get("monsterType"));
                monster.put("monsterId", (String) monsterObj.get("monsterId"));
                monster.put("count", String.valueOf(monsterObj.get("count")));
                monsters.add(monster);
            }

            // 记录转换后的怪物列表
            plugin.getLogger().info("转换后的怪物列表: " + monsters.toString());

            // 保存预设
            plugin.getLogger().info("开始保存预设: " + name);
            boolean success = presetManager.savePreset(name, description, monsters, originalName);

            if (success) {
                // 创建成功响应
                JSONObject response = new JSONObject();
                response.put("success", true);
                if (originalName != null) {
                    response.put("message", "预设更新成功");
                    plugin.getLogger().info("预设更新成功: " + originalName + " -> " + name);
                } else {
                    response.put("message", "预设创建成功");
                    plugin.getLogger().info("预设创建成功: " + name);
                }
                sendResponse(exchange, 200, response.toJSONString());
            } else {
                // 创建失败响应
                String errorMsg;
                if (originalName != null) {
                    errorMsg = "更新预设失败: " + originalName + " -> " + name;
                    plugin.getLogger().severe(errorMsg);
                    sendResponse(exchange, 500, createErrorResponse("更新预设失败"));
                } else {
                    errorMsg = "保存预设失败: " + name;
                    plugin.getLogger().severe(errorMsg);
                    sendResponse(exchange, 500, createErrorResponse("保存预设失败"));
                }
            }

        } catch (ParseException e) {
            plugin.getLogger().warning("解析预设JSON失败: " + e.getMessage());
            sendResponse(exchange, 400, createErrorResponse("无效的JSON格式: " + e.getMessage()));
        } catch (Exception e) {
            plugin.getLogger().warning("处理创建预设请求失败: " + e.getMessage());
            sendResponse(exchange, 500, createErrorResponse("服务器错误: " + e.getMessage()));
        }
    }

    /**
     * 处理删除预设的请求
     */
    private void handleDeletePreset(HttpExchange exchange, String query) throws IOException {
        // 解析查询参数
        if (query == null || !query.startsWith("name=")) {
            sendResponse(exchange, 400, createErrorResponse("缺少预设名称参数"));
            return;
        }

        String presetName = query.substring(5);

        // 删除预设
        boolean success = presetManager.deletePreset(presetName);

        if (success) {
            // 创建成功响应
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "预设删除成功");
            sendResponse(exchange, 200, response.toJSONString());
        } else {
            // 创建失败响应
            sendResponse(exchange, 404, createErrorResponse("预设不存在或无法删除"));
        }
    }

    /**
     * 处理CORS头
     */
    private void handleCorsHeaders(HttpExchange exchange) {
        exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, POST, DELETE, OPTIONS");
        exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type, Authorization");
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().add("Content-Type", "application/json; charset=UTF-8");
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    /**
     * 处理获取IDZ怪物列表的请求
     */
    private void handleGetIDZMonsters(HttpExchange exchange) throws IOException {
        try {
            // 获取IDZ怪物管理器
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager = plugin.getIDZMonsterManager();

            if (idzManager == null) {
                plugin.getLogger().warning("IDZ怪物管理器未初始化");
                sendResponse(exchange, 500, createErrorResponse("IDZ怪物管理器未初始化"));
                return;
            }

            // 获取所有IDZ怪物ID
            java.util.Set<String> monsterIds = idzManager.getAllMonsterIds();

            // 创建怪物列表
            JSONArray monsters = new JSONArray();

            for (String monsterId : monsterIds) {
                org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
                if (config != null) {
                    JSONObject monster = new JSONObject();
                    monster.put("id", monsterId);
                    monster.put("name", config.getDisplayName() != null ? config.getDisplayName() : monsterId);
                    monster.put("description", config.getDescription() != null ? config.getDescription() : "自定义IDZ怪物");
                    monsters.add(monster);
                }
            }

            // 创建响应
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("monsters", monsters);

            // 发送响应
            sendResponse(exchange, 200, response.toJSONString());

            plugin.getLogger().info("成功返回 " + monsters.size() + " 个IDZ怪物信息");

        } catch (Exception e) {
            plugin.getLogger().severe("获取IDZ怪物列表时发生错误: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, createErrorResponse("获取IDZ怪物列表失败: " + e.getMessage()));
        }
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        return response.toJSONString();
    }
}
