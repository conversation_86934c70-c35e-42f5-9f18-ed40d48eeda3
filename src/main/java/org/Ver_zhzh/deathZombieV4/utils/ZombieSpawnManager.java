package org.Ver_zhzh.deathZombieV4.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Scoreboard;

/**
 * 管理僵尸生成的工具类
 */
public class ZombieSpawnManager {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final GameSessionManager gameSessionManager;
    private final ZombieHelper zombieHelper;
    private ScoreboardManager scoreboardManager;

    // 存储每个游戏的刷怪任务
    private final Map<String, BukkitTask> spawnTasks = new HashMap<>();

    // 存储每个游戏的当前回合
    private final Map<String, Integer> gameRounds = new HashMap<>();

    // 存储每个游戏的每个回合剩余需要生成的僵尸数量
    private final Map<String, Map<Integer, Integer>> remainingSpawns = new HashMap<>();

    // 存储每个游戏每个回合每个生成点的剩余生成计数器
    private final Map<String, Map<Integer, Map<String, Integer>>> spawnPointCounters = new HashMap<>();

    // 存储正在进行补救生成的游戏，防止多重生成机制冲突
    private final Set<String> remedialSpawningGames = new HashSet<>();

    // 随机数生成器
    private final Random random = new Random();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ZombieSpawnManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.gameSessionManager = plugin.getGameSessionManager();
        this.zombieHelper = plugin.getZombieHelper();

        // 启动监控任务，检查游戏状态并管理生成
        startMonitorTask();

        // 启动定时任务，隐藏所有idc类型实体的名称
        startHideEntityNamesTask();
    }

    /**
     * 设置ScoreboardManager引用
     *
     * @param scoreboardManager 计分板管理器实例
     */
    public void setScoreboardManager(ScoreboardManager scoreboardManager) {
        this.scoreboardManager = scoreboardManager;
    }

    /**
     * 启动定时任务，隐藏所有idc类型实体的名称
     */
    private void startHideEntityNamesTask() {
        // 每5秒执行一次，隐藏所有idc类型实体的名称
        plugin.getServer().getScheduler().runTaskTimer(plugin, () -> {
            // 遍历所有世界
            for (World world : plugin.getServer().getWorlds()) {
                hideAllIdcEntityNames(world);
            }
        }, 20L, 100L); // 初始延迟1秒，之后每5秒执行一次

        plugin.getLogger().info("已启动idc类型实体名称隐藏任务");
    }

    /**
     * 启动监控任务
     */
    private void startMonitorTask() {
        // 每5秒检查一次游戏状态
        new BukkitRunnable() {
            @Override
            public void run() {
                checkGames();
            }
        }.runTaskTimer(plugin, 20L, 100L);

        // 每分钟额外检查一次僵尸生成情况
        new BukkitRunnable() {
            @Override
            public void run() {
                checkZombieSpawns();
            }
        }.runTaskTimer(plugin, 1200L, 1200L); // 1200 ticks = 60秒

        // 🔥 方案A修改：移除30秒检查任务，不再需要补救生成
        // new BukkitRunnable() {
        //     @Override
        //     public void run() {
        //         checkAndFixZombieSpawns();
        //     }
        // }.runTaskTimer(plugin, 600L, 600L); // 已移除补救生成机制
    }

    // 🔥 方案A修改：移除自定义怪物单独计数方法
    // IDZ怪物现在计入正常的countGameMonsters()方法中
    // 不再需要单独的计数逻辑

    // 🔥 方案A修改：移除回合检查方法
    // 不再需要检查是否是自定义怪物专用回合

    /**
     * 计算指定游戏和回合的生成间隔时间（ticks）
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @return 生成间隔时间（ticks，20 ticks = 1秒）
     */
    private long calculateSpawnInterval(String gameName, int round) {
        // 获取基础生成间隔（秒）
        double baseInterval = plugin.getConfig().getDouble("game.zombie_spawn.base_spawn_interval", 10.0);

        // 检查是否启用速度递增
        boolean speedIncreaseEnabled = plugin.getConfig().getBoolean("game.zombie_spawn.speed_increase_enabled", true);

        if (!speedIncreaseEnabled) {
            // 不启用速度递增，返回固定间隔
            return (long) (baseInterval * 20); // 转换为ticks
        }

        // 获取开始加速的回合
        int startRound = plugin.getConfig().getInt("game.zombie_spawn.speed_increase_start_round", 3);

        if (round < startRound) {
            // 未到加速回合，使用基础间隔
            return (long) (baseInterval * 20);
        }

        // 计算加速后的间隔
        String increaseMode = plugin.getConfig().getString("game.zombie_spawn.speed_increase_mode", "multiply");
        double currentInterval = baseInterval;

        if ("multiply".equals(increaseMode)) {
            // 翻倍模式：每回合速度翻倍（间隔减半）
            int acceleratedRounds = round - startRound + 1;
            currentInterval = baseInterval / Math.pow(2, acceleratedRounds - 1);
        } else if ("linear".equals(increaseMode)) {
            // 线性模式：每回合减少固定时间
            double decreasePerRound = plugin.getConfig().getDouble("game.zombie_spawn.linear_decrease_per_round", 1.0);
            int acceleratedRounds = round - startRound;
            currentInterval = baseInterval - (decreasePerRound * acceleratedRounds);
        }

        // 确保不小于最小间隔
        double minInterval = plugin.getConfig().getDouble("game.zombie_spawn.min_spawn_interval", 2.0);
        currentInterval = Math.max(currentInterval, minInterval);

        // 转换为ticks并返回
        long intervalTicks = (long) (currentInterval * 20);

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("游戏 " + gameName + " 第 " + round + " 回合生成间隔: " +
                currentInterval + "秒 (" + intervalTicks + " ticks)");
        }

        return intervalTicks;
    }

    /**
     * 为新回合重新启动生成任务，使用新的生成间隔
     *
     * @param gameName 游戏名称
     * @param round 新回合数
     */
    private void restartSpawnTaskForNewRound(String gameName, int round) {
        // 取消现有的生成任务
        BukkitTask existingTask = spawnTasks.get(gameName);
        if (existingTask != null) {
            existingTask.cancel();
            spawnTasks.remove(gameName);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("已取消游戏 " + gameName + " 的旧生成任务");
            }
        }

        // 延迟启动新的生成任务，给玩家一些准备时间
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // 检查游戏是否仍在运行
            if (gameSessionManager.getGameState(gameName) == GameState.RUNNING) {
                startSpawnTask(gameName);
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("已为游戏 " + gameName + " 第 " + round + " 回合启动新的生成任务");
                }
            }
        }, 60L); // 延迟3秒启动新任务
    }

    /**
     * 检查是否是自定义怪物类型（IDZ、IDC、IDN等）
     */
    private boolean isCustomMonsterType(String monsterType, String monsterId) {
        // 检查怪物类型
        if (monsterType != null) {
            String type = monsterType.toLowerCase();
            if (type.equals("idz") || type.equals("idc") || type.equals("idn")) {
                return true;
            }
        }

        // 检查怪物ID
        if (monsterId != null) {
            String id = monsterId.toLowerCase();
            if (id.startsWith("idz") || id.startsWith("idc") || id.startsWith("idn")) {
                return true;
            }
        }

        return false;
    }

    // 🔥 方案A修改：移除30秒检查和补救生成方法
    // 不再需要补救生成机制，严格按配置文件生成



    /**
     * 额外检查僵尸生成情况，确保所有僵尸都能正确生成
     */
    private void checkZombieSpawns() {
        // 获取所有运行中的游戏
        Map<String, GameState> games = gameSessionManager.getRunningGamesWithStates();

        for (Map.Entry<String, GameState> entry : games.entrySet()) {
            String gameName = entry.getKey();
            GameState state = entry.getValue();

            // 只检查正在运行中的游戏
            if (state == GameState.RUNNING) {
                // 获取当前回合
                int currentRound = gameRounds.getOrDefault(gameName, 1);

                // 检查场上是否有自定义僵尸
                int existingMonsters = countGameMonsters(gameName);

                // 获取当前回合剩余生成数量
                int remaining = remainingSpawns.get(gameName).getOrDefault(currentRound, 0);

                // 60秒检查：只在没有补救生成进行时进行简单检查
                if (!remedialSpawningGames.contains(gameName)) {
                    if (existingMonsters <= 0 && remaining <= 0) {
                        // 如果没有怪物且没有剩余配额，检查是否需要进入下一回合
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("每分钟额外检查: 游戏 " + gameName + " 当前回合所有僵尸已生成完毕");
                        }
                        checkRoundCompletion(gameName);
                    } else if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("每分钟额外检查: 游戏 " + gameName + " 场上有 " + existingMonsters + " 个僵尸，剩余配额 " + remaining + " 个");
                    }
                } else {
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("每分钟额外检查: 游戏 " + gameName + " 正在进行补救生成，跳过检查");
                    }
                }
            }
        }
    }

    /**
     * 检查所有游戏状态并管理生成任务
     */
    private void checkGames() {
        // 获取所有运行中的游戏
        Map<String, GameState> games = gameSessionManager.getRunningGamesWithStates();

        for (Map.Entry<String, GameState> entry : games.entrySet()) {
            String gameName = entry.getKey();
            GameState state = entry.getValue();

            // 检查游戏是否在运行中
            if (state == GameState.RUNNING) {
                // 检查游戏中的玩家数量是否满足最小要求
                Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
                int minPlayers = gameManager.getMinPlayers(gameName);

                if (participants.size() < minPlayers) {
                    plugin.getLogger().warning("游戏 " + gameName + " 玩家数量不足（当前: " + participants.size()
                            + "，最小要求: " + minPlayers + "），结束游戏");

                    // 通知所有参与者
                    for (UUID uuid : participants) {
                        Player player = Bukkit.getPlayer(uuid);
                        if (player != null && player.isOnline()) {
                            player.sendMessage(ChatColor.RED + "游戏玩家数量不足，游戏结束！");
                        }
                    }

                    // 结束游戏并清理
                    endGameAndCleanup(gameName);
                    continue;
                }

                // 🔥 修复重复启动问题：更严格的检查条件
                if (!spawnTasks.containsKey(gameName) || spawnTasks.get(gameName).isCancelled()) {
                    // 额外检查：确保当前回合还有怪物需要生成
                    int currentRound = gameRounds.getOrDefault(gameName, 1);
                    int remaining = 0;
                    if (remainingSpawns.containsKey(gameName)) {
                        remaining = remainingSpawns.get(gameName).getOrDefault(currentRound, 0);
                    }

                    // 检查场上怪物数量是否已达上限
                    int existingMonsters = countGameMonsters(gameName);
                    int maxMonstersForRound = getZombieCountForRound(gameName, currentRound);

                    // 只有在确实需要生成时才启动任务
                    if (remaining > 0 && existingMonsters < maxMonstersForRound) {
                        plugin.getLogger().info("监控检查: 游戏 " + gameName + " 需要启动生成任务 (剩余配额:" + remaining +
                            ", 场上:" + existingMonsters + "/" + maxMonstersForRound + ")");
                        startSpawnTask(gameName);
                    } else {
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("监控检查: 游戏 " + gameName + " 无需启动生成任务 (剩余配额:" + remaining +
                                ", 场上:" + existingMonsters + "/" + maxMonstersForRound + ")");
                        }
                    }
                }
            } else {
                // 如果游戏不在运行中，但有生成任务，停止任务
                if (spawnTasks.containsKey(gameName) && !spawnTasks.get(gameName).isCancelled()) {
                    spawnTasks.get(gameName).cancel();
                    spawnTasks.remove(gameName);
                }
            }
        }

        // 清理不再运行的游戏的任务
        List<String> gamesToRemove = new ArrayList<>();
        for (String gameName : spawnTasks.keySet()) {
            if (!games.containsKey(gameName) || games.get(gameName) != GameState.RUNNING) {
                spawnTasks.get(gameName).cancel();
                gamesToRemove.add(gameName);
            }
        }

        for (String gameName : gamesToRemove) {
            spawnTasks.remove(gameName);
            gameRounds.remove(gameName);
            remainingSpawns.remove(gameName);
            spawnPointCounters.remove(gameName);
            remedialSpawningGames.remove(gameName);
        }
    }

    /**
     * 为指定游戏启动生成任务
     *
     * @param gameName 游戏名称
     */
    private void startSpawnTask(String gameName) {
        // 初始化该游戏的回合数
        if (!gameRounds.containsKey(gameName)) {
            gameRounds.put(gameName, 1); // 从第一回合开始
        }

        // 初始化该游戏的剩余生成数量
        if (!remainingSpawns.containsKey(gameName)) {
            remainingSpawns.put(gameName, new HashMap<>());
        }

        // 获取当前回合
        int currentRound = gameRounds.get(gameName);

        // 初始化当前回合的剩余生成数量
        if (!remainingSpawns.get(gameName).containsKey(currentRound)) {
            int zombieCount = getZombieCountForRound(gameName, currentRound);
            remainingSpawns.get(gameName).put(currentRound, zombieCount);
        }

        // 初始化当前回合的生成点计数器
        initializeSpawnPointCounters(gameName, currentRound);

        // 🔥 计算当前回合的生成间隔
        long spawnInterval = calculateSpawnInterval(gameName, currentRound);

        // 创建生成任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 如果游戏不再运行，取消任务
                if (gameSessionManager.getGameState(gameName) != GameState.RUNNING) {
                    this.cancel();
                    spawnTasks.remove(gameName);
                    return;
                }

                // 获取计分板中的当前回合
                if (scoreboardManager != null) {
                    // 使用计分板中的回合数据更新本地回合记录
                    Integer scoreboardRound = null;

                    // 尝试通过反射获取计分板中的当前回合信息
                    try {
                        java.lang.reflect.Field currentRoundsField = scoreboardManager.getClass().getDeclaredField("currentRounds");
                        currentRoundsField.setAccessible(true);
                        @SuppressWarnings("unchecked")
                        Map<String, Integer> currentRounds = (Map<String, Integer>) currentRoundsField.get(scoreboardManager);
                        scoreboardRound = currentRounds.get(gameName);
                    } catch (Exception e) {
                        plugin.getLogger().warning("无法获取计分板回合信息: " + e.getMessage());
                    }

                    if (scoreboardRound != null && scoreboardRound != gameRounds.get(gameName)) {
                        // 计分板回合已更新，同步回合数据
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("同步回合数据: 从计分板更新回合 " + gameRounds.get(gameName) + " -> " + scoreboardRound);
                        }
                        gameRounds.put(gameName, scoreboardRound);

                        // 初始化新回合的剩余生成数量
                        if (!remainingSpawns.get(gameName).containsKey(scoreboardRound)) {
                            int zombieCount = getZombieCountForRound(gameName, scoreboardRound);
                            remainingSpawns.get(gameName).put(scoreboardRound, zombieCount);
                        }
                    }
                }

                // 检查是否正在进行补救生成，如果是则跳过正常生成
                if (remedialSpawningGames.contains(gameName)) {
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("正常生成任务: 游戏 " + gameName + " 正在进行补救生成，跳过正常生成");
                    }
                    return;
                }

                // 获取当前回合的剩余生成数量
                int currentRound = gameRounds.get(gameName);
                int remaining = remainingSpawns.get(gameName).getOrDefault(currentRound, 0);

                // 🔥 方案A修改：移除自定义怪物专用回合检查
                // IDZ怪物现在计入正常生成流程，不需要特殊处理

                // 如果剩余数量为0，检查是否需要切换到下一回合
                if (remaining <= 0) {
                    // 检查场上是否仍有怪物，先尝试直接计算实际存在的怪物
                    int existingMonsters = countGameMonsters(gameName);

                    // 如果实际计算没有怪物，再检查计分板中的数据
                    if (existingMonsters <= 0 && scoreboardManager != null) {
                        // 使用计分板的剩余僵尸数量再次确认
                        existingMonsters = scoreboardManager.getRemainingZombiesInRound(gameName);
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("从计分板获取剩余僵尸数量: " + existingMonsters);
                        }
                    } else if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("实际计算游戏 " + gameName + " 剩余僵尸数量: " + existingMonsters);
                    }

                    // 如果没有怪物了，或者强制进入下一回合
                    if (existingMonsters <= 0) {
                        // 没有怪物了，尝试自动进入下一回合
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("游戏 " + gameName + " 当前回合 " + currentRound + " 所有怪物已清除");
                        }

                        // 调用回合完成检查方法，该方法会处理所有的回合切换和游戏结束逻辑
                        checkRoundCompletion(gameName);
                    }
                    return;
                }

                // 🔥 关键修复：检查场上实际怪物数量，防止超量生成
                int existingMonsters = countGameMonsters(gameName);
                int maxMonstersForRound = getZombieCountForRound(gameName, currentRound);

                // 如果场上怪物数量已经达到或超过回合上限，停止生成
                if (existingMonsters >= maxMonstersForRound) {
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("游戏 " + gameName + " 第 " + currentRound + " 回合场上怪物数量已达上限 (" +
                            existingMonsters + "/" + maxMonstersForRound + ")，停止生成");
                    }
                    return;
                }

                // 计算实际可以生成的数量（不能超过回合上限）
                int canSpawn = Math.min(remaining, maxMonstersForRound - existingMonsters);
                if (canSpawn <= 0) {
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("游戏 " + gameName + " 第 " + currentRound + " 回合无法生成更多怪物");
                    }
                    return;
                }

                // 确定本次要生成的数量（随机1-3个，但不超过可生成数量）
                int spawnCount = Math.min(canSpawn, 1 + random.nextInt(3));

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("游戏 " + gameName + " 第 " + currentRound + " 回合生成检查: 场上=" + existingMonsters +
                        ", 上限=" + maxMonstersForRound + ", 剩余配额=" + remaining + ", 本次生成=" + spawnCount);
                }

                // 生成怪物
                spawnMonstersForRound(gameName, currentRound, spawnCount);

                // 更新剩余生成数量
                remainingSpawns.get(gameName).put(currentRound, remaining - spawnCount);
            }
        }.runTaskTimer(plugin, 40L, spawnInterval); // 🔥 使用动态计算的生成间隔

        // 存储任务
        spawnTasks.put(gameName, task);
    }

    /**
     * 获取指定回合的总僵尸数量（包括已生成和未生成的）
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @return 总僵尸数量
     */
    private int getTotalZombiesForRound(String gameName, int round) {
        // 从游戏配置文件获取该回合的僵尸数量
        ConfigurationSection config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return 10; // 默认值
        }

        String path = "zombieCount.round" + round;
        return config.getInt(path, 10); // 如果未设置，默认为10个
    }

    /**
     * 获取指定回合的僵尸数量
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @return 僵尸数量
     */
    private int getZombieCountForRound(String gameName, int round) {
        // 从游戏配置文件获取该回合的僵尸数量
        ConfigurationSection config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return 10; // 默认值
        }

        String path = "zombieCount.round" + round;
        return config.getInt(path, 10); // 如果未设置，默认为10个
    }

    /**
     * 初始化指定游戏和回合的生成点计数器
     *
     * @param gameName 游戏名称
     * @param round 回合数
     */
    private void initializeSpawnPointCounters(String gameName, int round) {
        // 确保游戏的计数器映射存在
        if (!spawnPointCounters.containsKey(gameName)) {
            spawnPointCounters.put(gameName, new HashMap<>());
        }

        // 确保回合的计数器映射存在
        if (!spawnPointCounters.get(gameName).containsKey(round)) {
            spawnPointCounters.get(gameName).put(round, new HashMap<>());
        }

        // 获取该回合的生成配置
        Map<String, Map<String, Object>> roundModes = gameManager.getRoundModes(gameName, round);
        if (roundModes == null || roundModes.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 第 " + round + " 回合没有生成配置");
            return;
        }

        Map<String, Integer> roundCounters = spawnPointCounters.get(gameName).get(round);

        // 为每个生成点配置初始化计数器
        for (Map.Entry<String, Map<String, Object>> entry : roundModes.entrySet()) {
            String spawnConfigKey = entry.getKey();
            Map<String, Object> spawnConfig = entry.getValue();

            // 获取该生成点配置的生成数量
            int spawnCount = 1; // 默认每个配置生成1个
            if (spawnConfig.containsKey("count")) {
                Object countObj = spawnConfig.get("count");
                if (countObj instanceof Integer) {
                    spawnCount = (Integer) countObj;
                } else if (countObj instanceof String) {
                    try {
                        spawnCount = Integer.parseInt((String) countObj);
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning("无法解析生成点 " + spawnConfigKey + " 的count值: " + countObj + "，使用默认值1");
                        spawnCount = 1;
                    }
                } else {
                    plugin.getLogger().warning("生成点 " + spawnConfigKey + " 的count值类型不支持: " + (countObj != null ? countObj.getClass().getSimpleName() : "null") + "，使用默认值1");
                    spawnCount = 1;
                }
            }

            // 设置计数器
            roundCounters.put(spawnConfigKey, spawnCount);

            plugin.getLogger().info("初始化生成点计数器: " + spawnConfigKey + " = " + spawnCount);
        }

        plugin.getLogger().info("游戏 " + gameName + " 第 " + round + " 回合生成点计数器初始化完成，共 " + roundCounters.size() + " 个生成点");

        // 初始化完成后，立即检查并处理门锁状态的配额转移
        performInitialQuotaTransfer(gameName, round, roundCounters);
    }

    /**
     * 执行初始配额转移（在回合开始时）
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param roundCounters 回合计数器
     */
    private void performInitialQuotaTransfer(String gameName, int round, Map<String, Integer> roundCounters) {
        try {
            // 获取调整后的生成分配，检查门锁状态
            Map<String, Map<String, Object>> adjustedModes = plugin.getDoorManager().getAdjustedMonsterSpawnAllocation(gameName, round);
            if (adjustedModes == null || adjustedModes.isEmpty()) {
                return;
            }

            List<String> availableSpawnPoints = new ArrayList<>();
            List<String> unavailableSpawnPoints = new ArrayList<>();
            int totalTransferQuota = 0;

            // 分类生成点并计算需要转移的配额
            for (Map.Entry<String, Map<String, Object>> entry : adjustedModes.entrySet()) {
                String spawnConfigKey = entry.getKey();
                Map<String, Object> config = entry.getValue();
                int currentQuota = roundCounters.getOrDefault(spawnConfigKey, 0);

                if (Boolean.TRUE.equals(config.get("enabled"))) {
                    if (currentQuota > 0) {
                        availableSpawnPoints.add(spawnConfigKey);
                    }
                } else {
                    if (currentQuota > 0) {
                        unavailableSpawnPoints.add(spawnConfigKey);
                        totalTransferQuota += currentQuota;
                    }
                }
            }

            // 如果有需要转移的配额且有可用的生成点，执行转移
            if (totalTransferQuota > 0 && !availableSpawnPoints.isEmpty()) {
                plugin.getLogger().info("初始配额转移: 游戏 " + gameName + " 第 " + round + " 回合，发现 " + unavailableSpawnPoints.size() + " 个门未解锁的生成点");
                transferQuotaToAvailableSpawnPoints(gameName, round, unavailableSpawnPoints,
                                                  availableSpawnPoints, roundCounters, totalTransferQuota);
            } else if (totalTransferQuota > 0 && availableSpawnPoints.isEmpty()) {
                plugin.getLogger().warning("初始配额转移: 游戏 " + gameName + " 第 " + round + " 回合，所有门都未解锁，无法转移配额");
            }

        } catch (Exception e) {
            plugin.getLogger().warning("执行初始配额转移时出错: " + e.getMessage());
        }
    }

    // 🔥 方案A修改：移除补救生成方法
    // 不再需要补救生成机制



    /**
     * 将未解锁门的生成点配额转移到可用的生成点
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param unavailableSpawnPoints 不可用的生成点列表
     * @param availableSpawnPoints 可用的生成点列表
     * @param roundCounters 回合计数器
     * @param totalTransferQuota 总转移配额
     */
    private void transferQuotaToAvailableSpawnPoints(String gameName, int round,
                                                   List<String> unavailableSpawnPoints,
                                                   List<String> availableSpawnPoints,
                                                   Map<String, Integer> roundCounters,
                                                   int totalTransferQuota) {

        plugin.getLogger().info("开始配额转移: 游戏 " + gameName + " 第 " + round + " 回合，转移配额 " + totalTransferQuota + " 个");

        // 计算每个可用生成点应该分配到的额外配额
        int quotaPerSpawnPoint = totalTransferQuota / availableSpawnPoints.size();
        int remainingQuota = totalTransferQuota % availableSpawnPoints.size();

        // 分配配额到可用的生成点
        for (int i = 0; i < availableSpawnPoints.size(); i++) {
            String availableSpawnPoint = availableSpawnPoints.get(i);
            int currentQuota = roundCounters.get(availableSpawnPoint);

            // 基础分配配额
            int additionalQuota = quotaPerSpawnPoint;

            // 将余数分配给前几个生成点
            if (i < remainingQuota) {
                additionalQuota++;
            }

            // 更新配额
            int newQuota = currentQuota + additionalQuota;
            roundCounters.put(availableSpawnPoint, newQuota);

            plugin.getLogger().info("配额转移: 生成点 " + availableSpawnPoint + " 配额从 " + currentQuota + " 增加到 " + newQuota + " (+"+additionalQuota+")");
        }

        // 清空不可用生成点的配额
        for (String unavailableSpawnPoint : unavailableSpawnPoints) {
            int originalQuota = roundCounters.get(unavailableSpawnPoint);
            roundCounters.put(unavailableSpawnPoint, 0);
            plugin.getLogger().info("配额转移: 生成点 " + unavailableSpawnPoint + " 配额从 " + originalQuota + " 清零（门未解锁）");
        }

        plugin.getLogger().info("配额转移完成: 总计转移 " + totalTransferQuota + " 个配额到 " + availableSpawnPoints.size() + " 个可用生成点");
    }

    /**
     * 在特定生成点生成怪物
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param spawnConfigKey 生成点配置键
     * @param adjustedModes 调整后的生成模式
     * @return 是否成功生成
     */
    private boolean spawnMonsterAtSpecificSpawnPoint(String gameName, int round, String spawnConfigKey,
                                                   Map<String, Map<String, Object>> adjustedModes) {
        try {
            Map<String, Object> spawnConfig = adjustedModes.get(spawnConfigKey);
            if (spawnConfig == null) {
                plugin.getLogger().warning("找不到生成点配置: " + spawnConfigKey);
                return false;
            }

            // 提取实际生成点名称
            String actualSpawnName = spawnConfigKey;
            if (spawnConfigKey.contains("_")) {
                int lastUnderscoreIndex = spawnConfigKey.lastIndexOf("_刷怪逻辑");
                if (lastUnderscoreIndex > 0) {
                    actualSpawnName = spawnConfigKey.substring(0, lastUnderscoreIndex);
                } else {
                    String[] parts = spawnConfigKey.split("_");
                    if (parts.length > 1) {
                        String lastPart = parts[parts.length - 1];
                        if (lastPart.startsWith("刷怪逻辑")) {
                            actualSpawnName = String.join("_", Arrays.copyOf(parts, parts.length - 1));
                        }
                    }
                }
            }

            // 获取生成位置
            Location spawnLocation = gameManager.getZombieSpawnLocation(gameName, actualSpawnName);
            if (spawnLocation == null) {
                plugin.getLogger().warning("无法获取生成点 " + actualSpawnName + " 的位置");
                return false;
            }

            // 获取怪物类型和ID
            String monsterType = (String) spawnConfig.get("monsterType");
            String monsterId = (String) spawnConfig.get("monsterId");

            if (monsterType == null || monsterId == null) {
                plugin.getLogger().warning("生成点配置缺少怪物类型或ID: " + spawnConfigKey);
                return false;
            }

            // 🔥 方案A修改：统一怪物生成处理逻辑
            // 所有怪物（包括IDZ）现在都使用相同的生成和计数逻辑

            // 记录生成前的怪物数量
            int beforeCount = countGameMonsters(gameName);

            // 🔥 修复：传递游戏名称参数
            spawnMonster(spawnLocation, monsterType, monsterId, gameName);

            // 等待一小段时间让实体完全生成
            try {
                Thread.sleep(100);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }

            // 检查是否成功生成
            int afterCount = countGameMonsters(gameName);
            boolean success = afterCount > beforeCount;

            if (success) {
                plugin.getLogger().info("在生成点 " + spawnConfigKey + " 成功生成怪物: 类型=" + monsterType + ", ID=" + monsterId);
            } else {
                plugin.getLogger().warning("在生成点 " + spawnConfigKey + " 生成怪物失败");
            }

            return success;

        } catch (Exception e) {
            plugin.getLogger().warning("在生成点 " + spawnConfigKey + " 生成怪物时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 生成指定回合的怪物（使用基于计数器的新逻辑）
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param count 生成数量
     */
    private void spawnMonstersForRound(String gameName, int round, int count) {
        // 检查是否正在进行补救生成，如果是则跳过
        if (remedialSpawningGames.contains(gameName)) {
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("正常生成: 游戏 " + gameName + " 正在进行补救生成，跳过正常生成");
            }
            return;
        }

        // 确保生成点计数器已初始化
        if (!spawnPointCounters.containsKey(gameName) ||
            !spawnPointCounters.get(gameName).containsKey(round)) {
            initializeSpawnPointCounters(gameName, round);
        }

        // 获取调整后的生成分配
        Map<String, Map<String, Object>> adjustedModes = plugin.getDoorManager().getAdjustedMonsterSpawnAllocation(gameName, round);
        if (adjustedModes == null || adjustedModes.isEmpty()) {
            plugin.getLogger().warning("正常生成: 游戏 " + gameName + " 第 " + round + " 回合没有生成配置");
            return;
        }

        // 获取当前回合的生成点计数器
        Map<String, Integer> roundCounters = spawnPointCounters.get(gameName).get(round);

        // 收集可用的生成点
        List<String> availableSpawnPoints = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : adjustedModes.entrySet()) {
            String spawnConfigKey = entry.getKey();
            Map<String, Object> config = entry.getValue();

            // 检查生成点是否启用且计数器大于0
            if (Boolean.TRUE.equals(config.get("enabled")) &&
                roundCounters.getOrDefault(spawnConfigKey, 0) > 0) {
                availableSpawnPoints.add(spawnConfigKey);
            }
        }

        if (availableSpawnPoints.isEmpty()) {
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("正常生成: 游戏 " + gameName + " 没有可用的生成点");
            }
            return;
        }

        // 执行生成，每次最多生成指定数量
        int actualSpawned = 0;
        for (int i = 0; i < count && !availableSpawnPoints.isEmpty(); i++) {
            // 随机选择一个可用的生成点
            String selectedSpawnPoint = availableSpawnPoints.get(random.nextInt(availableSpawnPoints.size()));

            // 尝试在该生成点生成怪物
            if (spawnMonsterAtSpecificSpawnPoint(gameName, round, selectedSpawnPoint, adjustedModes)) {
                actualSpawned++;

                // 减少计数器
                int currentCount = roundCounters.get(selectedSpawnPoint);
                roundCounters.put(selectedSpawnPoint, currentCount - 1);

                // 如果计数器用完，从可用列表中移除
                if (currentCount - 1 <= 0) {
                    availableSpawnPoints.remove(selectedSpawnPoint);
                }

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("正常生成: 在生成点 " + selectedSpawnPoint + " 成功生成1个怪物，剩余计数: " + (currentCount - 1));
                }
            } else {
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().warning("正常生成: 在生成点 " + selectedSpawnPoint + " 生成失败");
                }
            }
        }

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("正常生成完成: 游戏 " + gameName + " 成功生成 " + actualSpawned + " 个僵尸");
        }
    }

    /**
     * 生成指定回合的怪物（旧版本逻辑，保留作为备用）
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param count 生成数量
     */
    private void spawnMonstersForRoundLegacy(String gameName, int round, int count) {
        // 获取游戏中的任意一个玩家
        Player player = null;
        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        if (!participants.isEmpty()) {
            for (UUID uuid : participants) {
                Player p = plugin.getServer().getPlayer(uuid);
                if (p != null && p.isOnline()) {
                    player = p;
                    break;
                }
            }
        }

        // 如果找不到玩家，使用随机位置生成
        if (player == null) {
            plugin.getLogger().warning("游戏 " + gameName + " 找不到在线玩家，使用随机位置生成怪物");
            spawnMonstersAtRandomLocation(gameName, count);
            return;
        }

        // 获取回合模式配置 - 使用DoorManager的调整后的生成分配
        Map<String, Map<String, Object>> roundModes;
        try {
            // 先尝试使用DoorManager的调整后的生成分配
            roundModes = plugin.getDoorManager().getAdjustedMonsterSpawnAllocation(gameName, round);
            plugin.getLogger().info("使用DoorManager调整后的生成分配，生成点数量: " + (roundModes != null ? roundModes.size() : 0));
        } catch (Exception e) {
            // 如果出错，回退到原始方法
            plugin.getLogger().warning("使用DoorManager调整生成分配时出错: " + e.getMessage());
            roundModes = gameManager.getRoundModes(gameName, round);
        }

        if (roundModes == null || roundModes.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 第 " + round + " 回合没有配置，使用随机位置");
            spawnMonstersAtRandomLocation(gameName, count);
            return;
        }

        // 收集可用的生成点和刷怪逻辑
        List<String> availableSpawns = new ArrayList<>();
        Map<String, List<Map<String, Object>>> spawnPointLogics = new HashMap<>();

        // 遍历所有生成点配置
        for (String spawnName : roundModes.keySet()) {
            Map<String, Object> spawnConfig = roundModes.get(spawnName);

            // 获取生成点的启用状态
            boolean enabled = true;  // 默认启用
            if (spawnConfig.containsKey("enabled")) {
                enabled = Boolean.TRUE.equals(spawnConfig.get("enabled"));
            }

            if (enabled) {
                // 检查出生点是否可用 - 如果在游戏配置中定义则确认存在
                // 处理可能的组合键（生成点_刷怪逻辑）
                String actualSpawnName = spawnName;
                if (spawnName.contains("_")) {
                    // 修复：正确提取生成点名称
                    // 格式应该是 "生成点名称_刷怪逻辑X"，其中生成点名称可能包含下划线
                    // 例如：HOME_AN_刷怪逻辑1 -> HOME_AN，HOME_KM_刷怪逻辑1 -> HOME_KM
                    int lastUnderscoreIndex = spawnName.lastIndexOf("_刷怪逻辑");
                    if (lastUnderscoreIndex > 0) {
                        actualSpawnName = spawnName.substring(0, lastUnderscoreIndex);
                        plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                    } else {
                        // 如果没有找到"_刷怪逻辑"模式，使用原来的逻辑作为备用
                        String[] parts = spawnName.split("_");
                        if (parts.length > 1) {
                            // 检查最后一部分是否是刷怪逻辑
                            String lastPart = parts[parts.length - 1];
                            if (lastPart.startsWith("刷怪逻辑")) {
                                // 重新组合除最后一部分外的所有部分
                                actualSpawnName = String.join("_", Arrays.copyOf(parts, parts.length - 1));
                                plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                            } else {
                                plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                            }
                        } else {
                            plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                        }
                    }
                }

                // 不再在这里检查门限制，因为getAdjustedMonsterSpawnAllocation已经处理了enabled状态
                // 直接使用enabled状态，该状态已经考虑了门锁情况

                Location location = gameManager.getZombieSpawnLocation(gameName, actualSpawnName);
                if (location == null) {
                    plugin.getLogger().warning("无法获取游戏 " + gameName + " 的生成点 " + actualSpawnName + " 位置，将跳过此生成点");
                    continue;
                }

                availableSpawns.add(spawnName);

                // 处理刷怪逻辑
                List<Map<String, Object>> logics = new ArrayList<>();

                // 遍历生成点的所有子配置，寻找刷怪逻辑
                if (spawnConfig instanceof Map) {
                    Map<String, Object> spawnConfigMap = (Map<String, Object>) spawnConfig;

                    // 检查是否有嵌套的刷怪逻辑
                    boolean hasNestedLogic = false;
                    for (String key : spawnConfigMap.keySet()) {
                        if (key.startsWith("刷怪逻辑") && spawnConfigMap.get(key) instanceof Map) {
                            hasNestedLogic = true;
                            @SuppressWarnings("unchecked")
                            Map<String, Object> logicData = (Map<String, Object>) spawnConfigMap.get(key);

                            // 检查是否包含必要的刷怪信息
                            if (logicData.containsKey("monsterType") && logicData.containsKey("monsterId")) {
                                // 添加生成点位置信息到逻辑数据中
                                logicData = new HashMap<>(logicData); // 创建副本以避免修改原始数据
                                logicData.put("spawnLocation", location);
                                logics.add(logicData);
                                plugin.getLogger().info("找到刷怪逻辑: " + key + " 在生成点: " + spawnName);
                            }
                        }
                    }

                    // 如果没有找到嵌套的刷怪逻辑，检查直接配置
                    if (!hasNestedLogic && spawnConfigMap.containsKey("monsterType") && spawnConfigMap.containsKey("monsterId")) {
                        Map<String, Object> directLogic = new HashMap<>(spawnConfigMap);
                        directLogic.put("spawnLocation", location);
                        logics.add(directLogic);
                        plugin.getLogger().info("使用直接配置作为刷怪逻辑，生成点: " + spawnName);
                    }
                }

                if (!logics.isEmpty()) {
                    spawnPointLogics.put(spawnName, logics);
                }
            }
        }

        // 检查有效的生成点和刷怪逻辑
        if (availableSpawns.isEmpty() || spawnPointLogics.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 第 " + round + " 回合没有可用的生成点或刷怪逻辑");

            // 获取所有被锁定门限制的生成点
            List<String> restrictedSpawns = new ArrayList<>();
            Map<String, Map<String, Object>> allRoundModes = gameManager.getRoundModes(gameName, round);

            if (allRoundModes != null && !allRoundModes.isEmpty()) {
                for (String spawnName : allRoundModes.keySet()) {
                    // 提取实际生成点名称（如果是组合键）
                    String actualSpawnName = spawnName;
                    if (spawnName.contains("_")) {
                        // 修复：正确提取生成点名称
                        // 格式应该是 "生成点名称_刷怪逻辑X"，其中生成点名称可能包含下划线
                        // 例如：HOME_AN_刷怪逻辑1 -> HOME_AN，HOME_KM_刷怪逻辑1 -> HOME_KM
                        int lastUnderscoreIndex = spawnName.lastIndexOf("_刷怪逻辑");
                        if (lastUnderscoreIndex > 0) {
                            actualSpawnName = spawnName.substring(0, lastUnderscoreIndex);
                            plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                        } else {
                            // 如果没有找到"_刷怪逻辑"模式，使用原来的逻辑作为备用
                            String[] parts = spawnName.split("_");
                            if (parts.length > 1) {
                                // 检查最后一部分是否是刷怪逻辑
                                String lastPart = parts[parts.length - 1];
                                if (lastPart.startsWith("刷怪逻辑")) {
                                    // 重新组合除最后一部分外的所有部分
                                    actualSpawnName = String.join("_", Arrays.copyOf(parts, parts.length - 1));
                                    plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                                } else {
                                    plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                                }
                            } else {
                                plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                            }
                        }
                    }

                    // 检查是否被锁定门限制
                    if (plugin.getDoorManager().isSpawnRestrictedByLockedDoor(gameName, actualSpawnName)) {
                        restrictedSpawns.add(spawnName);
                        plugin.getLogger().info("生成点 " + actualSpawnName + " 被锁定的门限制");
                    }
                }
            }

            if (!restrictedSpawns.isEmpty()) {
                plugin.getLogger().warning("有 " + restrictedSpawns.size() + " 个生成点被锁定的门限制，玩家需要解锁门才能继续生成怪物");
                // 不再使用随机位置生成怪物，而是直接返回
                // 这样可以确保在没有可用生成点时不会生成怪物
                plugin.getLogger().warning("玩家需要解锁门才能继续生成怪物");
                return;
            } else {
                // 如果没有被锁定门限制的生成点，使用随机位置生成怪物
                plugin.getLogger().info("没有找到被锁定门限制的生成点，将使用随机位置生成怪物");
                spawnMonstersAtRandomLocation(gameName, count);
                return;
            }
        }

        // 随机选择生成点和生成逻辑
        for (int i = 0; i < count; i++) {
            // 随机选择一个可用生成点
            String spawnName = availableSpawns.get(random.nextInt(availableSpawns.size()));

            // 获取该生成点的刷怪逻辑列表
            List<Map<String, Object>> logics = spawnPointLogics.get(spawnName);
            if (logics == null || logics.isEmpty()) {
                plugin.getLogger().warning("生成点没有有效的刷怪逻辑: " + spawnName);
                continue;
            }

            // 随机选择一个刷怪逻辑
            Map<String, Object> logicConfig = logics.get(random.nextInt(logics.size()));

            // 获取怪物类型和ID
            String monsterType = (String) logicConfig.get("monsterType");
            String monsterId = (String) logicConfig.get("monsterId");
            Location spawnLocation = (Location) logicConfig.get("spawnLocation");

            if (spawnLocation == null) {
                plugin.getLogger().warning("生成点位置无效: " + spawnName);
                continue;
            }

            // 生成怪物
            plugin.getLogger().info("在生成点 " + spawnName + " 生成怪物: 类型=" + monsterType + ", ID=" + monsterId);

            // 根据怪物类型和ID生成怪物
            plugin.getLogger().info("尝试生成怪物: 类型=" + monsterType + ", ID=" + monsterId);

            // 处理不同类型的ID
            if (monsterId.startsWith("idc")) {
                // 使用spawnOtherEntity方法处理idc类型的实体
                plugin.getLogger().info("检测到idc类型的实体ID: " + monsterId + "，使用spawnOtherEntity方法");
                boolean success = zombieHelper.spawnOtherEntity(spawnLocation, monsterId);
                if (!success) {
                    plugin.getLogger().warning("使用spawnOtherEntity生成" + monsterId + "失败，尝试使用默认ID=1");
                    zombieHelper.spawnZombie(spawnLocation, 1);
                }
            } else if (monsterId.startsWith("idn")) {
                // 使用createNPC方法处理idn类型的实体
                plugin.getLogger().info("检测到idn类型的实体ID: " + monsterId + "，使用createNPC方法");
                boolean success = zombieHelper.createNPC(spawnLocation, monsterId);
                if (!success) {
                    // 如果失败，尝试使用spawnNPC方法
                    plugin.getLogger().info("使用createNPC失败，尝试使用spawnNPC方法");
                    try {
                        int npcId = Integer.parseInt(monsterId.substring(3));
                        Entity npcEntity = zombieHelper.spawnNPC(spawnLocation, npcId);
                        if (npcEntity == null) {
                            plugin.getLogger().warning("使用spawnNPC生成" + monsterId + "失败，尝试使用默认ID=1");
                            zombieHelper.spawnZombie(spawnLocation, 1);
                        }
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning("无法解析NPC ID: " + monsterId + "，使用默认ID=1");
                        zombieHelper.spawnZombie(spawnLocation, 1);
                    }
                }
            } else if (monsterId.startsWith("idz")) {
                // 处理IDZ类型的怪物
                plugin.getLogger().info("检测到idz类型的怪物ID: " + monsterId + "，使用IDZMonsterManager生成");

                // 获取IDZ怪物管理器
                org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager = plugin.getIDZMonsterManager();

                if (idzManager != null) {
                    // 生成IDZ怪物
                    org.bukkit.entity.LivingEntity idzEntity = idzManager.spawnMonster(spawnLocation, monsterId);

                    if (idzEntity == null) {
                        plugin.getLogger().warning("IDZ怪物生成失败: " + monsterId + "，使用默认ID=1");
                        zombieHelper.spawnZombie(spawnLocation, 1);
                    } else {
                        plugin.getLogger().info("成功生成IDZ怪物: " + monsterId);

                        // 🔥 方案A修改：IDZ怪物现在计入游戏僵尸数量
                        idzEntity.setMetadata("idz_special_spawn", new FixedMetadataValue(plugin, true));
                        idzEntity.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));
                        plugin.getLogger().info("IDZ怪物已标记，现在计入游戏僵尸数量");
                        // 不再直接返回，继续执行后续的游戏标识设置
                    }
                } else {
                    plugin.getLogger().warning("IDZ怪物管理器不可用，无法生成IDZ怪物: " + monsterId + "，使用默认ID=1");
                    zombieHelper.spawnZombie(spawnLocation, 1);
                }
            } else if (monsterId.startsWith("id")) {
                // 处理普通id类型的僵尸
                plugin.getLogger().info("检测到id类型的实体ID: " + monsterId + "，使用spawnZombie方法");
                try {
                    int zombieId = Integer.parseInt(monsterId.substring(2));
                    zombieHelper.spawnZombie(spawnLocation, zombieId);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析怪物ID: " + monsterId + "，使用默认ID=1");
                    zombieHelper.spawnZombie(spawnLocation, 1);
                }
            } else {
                // 尝试直接解析为数字ID
                plugin.getLogger().info("尝试将" + monsterId + "直接解析为数字ID");
                try {
                    int zombieId = Integer.parseInt(monsterId);
                    zombieHelper.spawnZombie(spawnLocation, zombieId);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析怪物ID: " + monsterId + "，使用默认ID=1");
                    zombieHelper.spawnZombie(spawnLocation, 1);
                }
            }
        }
    }

    /**
     * 在随机位置生成怪物
     *
     * @param gameName 游戏名称
     * @param count 生成数量
     */
    private void spawnMonstersAtRandomLocation(String gameName, int count) {
        // 获取游戏出生点作为参考位置
        Location spawnLocation = getGameSpawnLocation(gameName);
        if (spawnLocation == null) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有设置出生点，无法生成随机位置");
            return;
        }

        // 获取玩家位置，在玩家附近生成
        Set<UUID> players = gameSessionManager.getGameParticipants(gameName);
        if (players.isEmpty()) {
            return; // 没有玩家，不生成
        }

        // 随机选择一个玩家
        List<Player> onlinePlayers = new ArrayList<>();
        for (UUID uuid : players) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                onlinePlayers.add(player);
            }
        }

        if (onlinePlayers.isEmpty()) {
            return; // 没有在线玩家，不生成
        }

        Player randomPlayer = onlinePlayers.get(random.nextInt(onlinePlayers.size()));
        Location playerLocation = randomPlayer.getLocation();

        // 在玩家附近随机位置生成怪物
        for (int i = 0; i < count; i++) {
            // 随机偏移量，范围15-30格
            double offsetX = (random.nextDouble() * 15 + 15) * (random.nextBoolean() ? 1 : -1);
            double offsetZ = (random.nextDouble() * 15 + 15) * (random.nextBoolean() ? 1 : -1);

            Location spawnLoc = playerLocation.clone().add(offsetX, 0, offsetZ);

            // 确保生成位置是安全的
            spawnLoc.setY(spawnLoc.getWorld().getHighestBlockYAt(spawnLoc) + 1);

            // 随机选择怪物类型和ID
            String monsterType;
            String monsterId;

            // 根据回合数增加特殊怪物的概率
            int currentRound = gameRounds.get(gameName);
            double specialChance = Math.min(0.1 + (currentRound * 0.05), 0.5); // 从10%开始，每回合增加5%，最大50%

            if (random.nextDouble() < specialChance) {
                // 生成特殊怪物
                monsterType = "zombie";
                // 随机选择特殊僵尸ID (id8-id17)
                int idNum = 8 + random.nextInt(10);
                monsterId = "id" + idNum;
            } else {
                // 生成普通僵尸
                monsterType = "zombie";
                monsterId = "id1";
            }

            // 🔥 修复：传递游戏名称参数
            spawnMonster(spawnLoc, monsterType, monsterId, gameName);
        }
    }

    /**
     * 检查回合是否完成，如果完成则进入下一回合
     *
     * @param gameName 游戏名称
     */
    public void checkRoundCompletion(String gameName) {
        // 获取当前回合
        int currentRound = gameRounds.getOrDefault(gameName, 1);

        // 优先使用计分板中的剩余僵尸数量
        int existingMonsters = 0;
        if (scoreboardManager != null) {
            existingMonsters = scoreboardManager.getRemainingZombiesInRound(gameName);
            plugin.getLogger().info("从计分板获取游戏 " + gameName + " 剩余僵尸数量: " + existingMonsters);
        } else {
            // 如果计分板管理器不可用，才使用实际计算
            existingMonsters = countGameMonsters(gameName);
            plugin.getLogger().info("实际计算游戏 " + gameName + " 剩余僵尸数量: " + existingMonsters);
        }

        // 始终输出调试日志，不受debug配置限制
        plugin.getLogger().info("游戏 " + gameName + " 当前回合 " + currentRound + ", 剩余怪物数量: " + existingMonsters);

        // 如果没有怪物了，检查是否需要进入下一回合或结束游戏
        if (existingMonsters <= 0) {
            // 获取游戏最大回合数
            int maxRound = getMaxRounds(gameName);

            // 检查是否是最后一回合且僵尸数为0
            if (currentRound >= maxRound) {
                // 如果已经是最后一回合且僵尸数为0，结束游戏
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("游戏 " + gameName + " 最后一回合(第" + currentRound + "回合)僵尸数为0，游戏胜利结束");
                }

                // 立即停止计分板计时器，防止计时器干扰游戏结束
                if (scoreboardManager != null) {
                    scoreboardManager.stopGameTimers(gameName);
                    plugin.getLogger().info("已停止游戏 " + gameName + " 的计分板计时器");
                }

                // 使用新的游戏胜利处理逻辑
                handleGameVictory(gameName, maxRound);
                return;
            }

            // 进入下一回合
            int nextRound = currentRound + 1;

            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("游戏 " + gameName + " 即将进入下一回合: " + nextRound);
            }

            // 检查计分板管理器是否已标记游戏为结束状态
            if (scoreboardManager != null && scoreboardManager.isGameEnded(gameName)) {
                plugin.getLogger().info("游戏 " + gameName + " 已标记为结束，跳过下一回合逻辑");
                return;
            }

            // 清除场上所有僵尸
            clearAllZombies(gameName);

            // 更新游戏回合，这个方法会同时更新计分板和僵尸数量
            updateGameRound(gameName, nextRound);

            // 重新设置购买点NPC
            Bukkit.getScheduler().runTask(plugin, () -> {
                try {
                    // 通过反射调用GameSessionManager的setupBuyPoints方法
                    java.lang.reflect.Method setupBuyPointsMethod = gameSessionManager.getClass().getDeclaredMethod("setupBuyPoints", String.class);
                    setupBuyPointsMethod.setAccessible(true);
                    setupBuyPointsMethod.invoke(gameSessionManager, gameName);
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("成功重新设置游戏 " + gameName + " 的购买点NPC");
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("重新设置购买点NPC失败: " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        e.printStackTrace();
                    }
                }
            });

            // 向所有玩家发送回合开始的Title提示
            for (UUID uuid : gameSessionManager.getGameParticipants(gameName)) {
                Player player = Bukkit.getPlayer(uuid);
                if (player != null && player.isOnline()) {
                    // 发送消息通知
                    player.sendMessage(ChatColor.GREEN + "第 " + currentRound + " 回合结束! 第 " + nextRound + " 回合开始!");

                    // 使用MessageManager发送Title提示
                    try {
                        plugin.getMessageManager().sendRoundTitleMessage(player, nextRound, gameName);
                    } catch (Exception titleEx) {
                        // 如果MessageManager发送失败，使用备用的硬编码Title
                        try {
                            player.sendTitle(
                                    ChatColor.GOLD + "第 " + nextRound + " 回合",
                                    ChatColor.YELLOW + "准备迎战新的挑战！",
                                    10, 70, 20
                            );
                        } catch (Exception backupTitleEx) {
                            plugin.getLogger().warning("备用Title发送也失败: " + backupTitleEx.getMessage());
                            // 发送普通消息代替
                            player.sendMessage(ChatColor.GOLD + "第 " + nextRound + " 回合");
                            player.sendMessage(ChatColor.YELLOW + "准备迎战新的挑战！");
                        }
                    }

                    // 播放回合切换音效 - 使用僵尸主题+教堂风格的音效组合
                    try {
                        // 从配置文件获取回合切换音效
                        String configSound = plugin.getConfig().getString("sounds.round_change", "block.bell.resonate");

                        // 使用安全播放方法
                        playSoundSafely(player, configSound, "block.bell.resonate", 1.0f, 0.5f);

                        // 延迟5tick后播放第二个音效 - 钟声敲击（教堂风格）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "block.bell.use", "block.bell.use", 1.0f, 0.6f);
                        }, 5L);

                        // 延迟10tick后播放第三个音效 - 僵尸村民转换（僵尸主题）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "entity.zombie_villager.converted", "entity.zombie_villager.converted", 0.8f, 0.7f);
                        }, 10L);

                        // 延迟15tick后播放第四个音效 - 僵尸嘶吼（僵尸主题）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "entity.zombie.ambient", "entity.zombie.ambient", 1.0f, 0.5f);
                        }, 15L);

                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("成功播放回合切换音效组合");
                        }
                    } catch (Exception soundEx) {
                        plugin.getLogger().warning("无法播放回合切换音效: " + soundEx.getMessage());
                        // 尝试使用默认音效
                        playSoundSafely(player, "block.bell.resonate", "entity.experience_orb.pickup", 1.0f, 0.5f);
                    }
                }
            }

            // 重新启动生成任务
            startSpawnTask(gameName);
        }
    }

    /**
     * 生成一个怪物
     *
     * @param location 位置
     * @param monsterType 怪物类型
     * @param monsterId 怪物ID
     * @param gameName 游戏名称
     */
    private void spawnMonster(Location location, String monsterType, String monsterId, String gameName) {
        // 🔥 修复：直接使用传入的游戏名称，不再尝试自动获取
        if (gameName == null || gameName.isEmpty()) {
            plugin.getLogger().warning("游戏名称为空，怪物生成可能不会被正确计数");
            gameName = ""; // 设置为空字符串以避免null
        }

        // 使用ZombieHelper生成怪物
        Entity entity = null;

        // 处理不同的怪物类型
        if (monsterType.equalsIgnoreCase("zombie")) {
            // 检查ID格式
            if (monsterId.startsWith("id")) {
                try {
                    plugin.getLogger().info("尝试生成自定义僵尸: " + monsterId + " 在位置: " + location);

                    // 直接使用完整的ID字符串，不再尝试提取数字部分
                    entity = zombieHelper.spawnCustomZombie(location, monsterId);

                    if (entity != null) {
                        plugin.getLogger().info("成功使用spawnCustomZombie生成僵尸: " + monsterId + ", 实体类型: " + entity.getType());
                    } else {
                        plugin.getLogger().warning("spawnCustomZombie返回null，尝试使用传统方法生成僵尸: " + monsterId);

                        // 如果自定义僵尸生成失败，尝试传统方法
                        int id = Integer.parseInt(monsterId.substring(2));
                        entity = zombieHelper.spawnZombie(location, id);

                        if (entity != null) {
                            plugin.getLogger().warning("使用传统方法生成僵尸成功: " + monsterId + ", 实体类型: " + entity.getType() + " (这可能不是自定义僵尸)");
                        } else {
                            plugin.getLogger().warning("传统方法也无法生成僵尸: " + monsterId);
                        }
                    }
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无效的僵尸ID格式: " + monsterId + "，将使用默认ID");
                    entity = zombieHelper.spawnZombie(location, 1); // 默认ID 1
                }
            } else {
                plugin.getLogger().info("僵尸ID不以'id'开头，使用默认ID 1: " + monsterId);
                entity = zombieHelper.spawnZombie(location, 1); // 默认ID 1
            }
        } else if (monsterType.equalsIgnoreCase("skeleton")) {
            entity = location.getWorld().spawnEntity(location, EntityType.SKELETON);
        } else if (monsterType.equalsIgnoreCase("spider")) {
            entity = location.getWorld().spawnEntity(location, EntityType.SPIDER);
        } else if (monsterType.equalsIgnoreCase("idz") || monsterId.startsWith("idz")) {
            plugin.getLogger().info("尝试生成IDZ怪物: 类型=" + monsterType + ", ID=" + monsterId);

            // 获取IDZ怪物管理器
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager = plugin.getIDZMonsterManager();

            if (idzManager != null) {
                // 生成IDZ怪物
                org.bukkit.entity.LivingEntity idzEntity = idzManager.spawnMonster(location, monsterId);

                if (idzEntity != null) {
                    entity = idzEntity;
                    plugin.getLogger().info("成功生成IDZ怪物: " + monsterId + " 在位置: " +
                        location.getX() + "," + location.getY() + "," + location.getZ());

                    // 为IDZ怪物添加特殊标记，表示它不应该计入游戏僵尸数量
                    idzEntity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
                    idzEntity.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));
                    idzEntity.setMetadata("idz_special_spawn", new FixedMetadataValue(plugin, true)); // 特殊标记

                    // 不设置游戏标识名称，保持IDZ怪物的原始显示名称
                    plugin.getLogger().info("IDZ怪物保持原始显示名称: " + idzEntity.getCustomName());
                    return; // 直接返回，不执行后续的名称设置逻辑
                } else {
                    plugin.getLogger().warning("IDZ怪物生成失败: " + monsterId);
                }
            } else {
                plugin.getLogger().warning("IDZ怪物管理器不可用，无法生成IDZ怪物: " + monsterId);
            }
        } else if (monsterType.equalsIgnoreCase("entity") || monsterType.equalsIgnoreCase("special") || monsterId.startsWith("idc")) {
            plugin.getLogger().info("尝试生成特殊实体: 类型=" + monsterType + ", ID=" + monsterId);
            // 特殊实体生成逻辑
            if (monsterId.equalsIgnoreCase("idc1")) {
                // 尝试直接使用spawnOtherEntity方法
                boolean success = zombieHelper.spawnOtherEntity(location, monsterId);
                if (success) {
                    plugin.getLogger().info("成功生成idc1类型的实体");
                    // 返回一个临时实体对象，仅用于标记成功
                    entity = location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                    entity.remove(); // 立即移除，因为真正的实体已由spawnOtherEntity生成
                } else {
                    // 如果失败，尝试旧方法
                    entity = zombieHelper.spawnSpecialEntity(location, "mutant01");
                }
            } else if (monsterId.equalsIgnoreCase("idc2")) {
                // 尝试直接使用spawnOtherEntity方法
                boolean success = zombieHelper.spawnOtherEntity(location, monsterId);
                if (success) {
                    plugin.getLogger().info("成功生成idc2类型的实体");
                    // 返回一个临时实体对象，仅用于标记成功
                    entity = location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                    entity.remove(); // 立即移除，因为真正的实体已由spawnOtherEntity生成
                } else {
                    // 如果失败，尝试旧方法
                    entity = zombieHelper.spawnSpecialEntity(location, "mutant02");
                }
            } else if (monsterId.equalsIgnoreCase("idc3")) {
                // 尝试直接使用spawnOtherEntity方法
                boolean success = zombieHelper.spawnOtherEntity(location, monsterId);
                if (success) {
                    plugin.getLogger().info("成功生成idc3类型的实体");
                    // 返回一个临时实体对象，仅用于标记成功
                    entity = location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                    entity.remove(); // 立即移除，因为真正的实体已由spawnOtherEntity生成
                } else {
                    // 如果失败，尝试旧方法
                    entity = zombieHelper.spawnSpecialEntity(location, "mutant_blaze");
                }
            } else if (monsterId.equalsIgnoreCase("idc4")) {
                // idc4是变异爬行者，直接使用spawnOtherEntity方法
                boolean success = zombieHelper.spawnOtherEntity(location, monsterId);
                if (success) {
                    plugin.getLogger().info("成功生成idc4变异爬行者");
                    // 返回一个临时实体对象，仅用于标记成功
                    entity = location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                    entity.remove(); // 立即移除，因为真正的实体已由spawnOtherEntity生成
                } else {
                    plugin.getLogger().warning("idc4变异爬行者生成失败");
                }
            } else if (monsterId.equalsIgnoreCase("idn1") || monsterId.equalsIgnoreCase("idn2")) {
                entity = zombieHelper.spawnNPC(location, monsterId.equals("idn1") ? 1 : 2);
            } else if (monsterId.startsWith("idc")) {
                // 处理其他idc类型的实体
                boolean success = zombieHelper.spawnOtherEntity(location, monsterId);
                if (success) {
                    plugin.getLogger().info("成功生成" + monsterId + "类型的实体");
                    // 返回一个临时实体对象，仅用于标记成功
                    entity = location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                    entity.remove(); // 立即移除，因为真正的实体已由spawnOtherEntity生成
                }
            }
        }

        // 为生成的实体添加游戏标记
        if (entity != null) {
            // 添加游戏实体标记
            entity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 添加自定义僵尸类型标记
            entity.setMetadata("customZombieType", new FixedMetadataValue(plugin, monsterType + "_" + monsterId));

            // 添加当前游戏标记
            entity.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));

            // 设置自定义名称，便于计分板识别
            if (entity instanceof LivingEntity) {
                LivingEntity livingEntity = (LivingEntity) entity;

                // 检查是否已经有自定义名称（来自CustomZombie）
                String existingName = livingEntity.getCustomName();
                if (existingName != null && (existingName.contains("普通僵尸") || existingName.contains("小僵尸") ||
                    existingName.contains("路障僵尸") || existingName.contains("双生僵尸") || existingName.contains("剧毒僵尸") ||
                    existingName.contains("钻斧僵尸") || existingName.contains("骷髅僵尸") || existingName.contains("武装僵尸") ||
                    existingName.contains("肥胖僵尸") || existingName.contains("法师僵尸") || existingName.contains("自爆僵尸") ||
                    existingName.contains("毒箭僵尸") || existingName.contains("电击僵尸") || existingName.contains("冰冻僵尸") ||
                    existingName.contains("暗影僵尸") || existingName.contains("毁灭僵尸") || existingName.contains("雷霆僵尸") ||
                    existingName.contains("变异") || existingName.contains("终极") || existingName.contains("气球") ||
                    existingName.contains("迷雾") || existingName.contains("博士"))) {

                    plugin.getLogger().info("保留CustomZombie设置的自定义名称: " + existingName);
                    // 保持CustomZombie设置的名称可见性
                    // livingEntity.setCustomNameVisible(true); // 已在CustomZombie中设置
                } else {
                    // 如果没有自定义名称，设置游戏标识名称
                    livingEntity.setCustomName("游戏_" + gameName + "_" + monsterType + "_" + monsterId);
                    livingEntity.setCustomNameVisible(false); // 不显示名称
                }
            }

            plugin.getLogger().info("成功生成怪物: " + monsterType + "_" + monsterId + " 在游戏 " + gameName);
        } else {
            plugin.getLogger().warning("无法生成怪物 (类型=" + monsterType + ", ID=" + monsterId + ")");

            // 对于idc类型的实体，可能entity为null但实际上已经生成
            // 因为spawnOtherEntity方法可能返回null但实体已经生成
            // 所以我们需要查找并处理刚刚生成的实体
            if (monsterId.startsWith("idc")) {
                plugin.getLogger().info("尝试查找并处理刚刚生成的idc类型实体: " + monsterId);

                // 在生成位置附近查找实体
                for (Entity nearbyEntity : location.getWorld().getNearbyEntities(location, 5, 5, 5)) {
                    if (nearbyEntity instanceof LivingEntity) {
                        LivingEntity livingEntity = (LivingEntity) nearbyEntity;

                        // 检查是否是刚刚生成的实体
                        if (livingEntity.getCustomName() != null
                                && (livingEntity.getCustomName().contains(monsterId)
                                || livingEntity.hasMetadata("customZombieType"))) {

                            plugin.getLogger().info("找到刚刚生成的实体: " + livingEntity.getType() + ", 名称: " + livingEntity.getCustomName());

                            // 添加游戏实体标记
                            livingEntity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

                            // 添加自定义僵尸类型标记（如果还没有）
                            if (!livingEntity.hasMetadata("customZombieType")) {
                                livingEntity.setMetadata("customZombieType", new FixedMetadataValue(plugin, monsterType + "_" + monsterId));
                            }

                            // 添加当前游戏标记
                            livingEntity.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));

                            // 检查是否已经有自定义名称（来自CustomZombie）
                            String existingName = livingEntity.getCustomName();
                            if (existingName != null && (existingName.contains("变异") || existingName.contains("灾厄") ||
                                existingName.contains("凋零") || existingName.contains("感染") || existingName.contains("领主"))) {

                                plugin.getLogger().info("保留CustomZombie设置的idc实体自定义名称: " + existingName);
                                // 保持CustomZombie设置的名称可见性
                            } else {
                                // 设置自定义名称，便于计分板识别，并设置为不可见
                                livingEntity.setCustomName("游戏_" + gameName + "_" + monsterType + "_" + monsterId);
                                livingEntity.setCustomNameVisible(false);
                            }

                            // 添加特殊标记，表示这是一个idc类型的实体，应该被当做僵尸处理
                            livingEntity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));

                            plugin.getLogger().info("成功处理刚刚生成的实体: " + monsterType + "_" + monsterId + " 在游戏 " + gameName);
                        }
                    }
                }
            }
        }
    }

    /**
     * 通知附近的玩家有怪物生成
     *
     * @param location 生成位置
     * @param monsterType 怪物类型
     * @param monsterId 怪物ID
     */
    private void notifyNearbyPlayers(Location location, String monsterType, String monsterId) {
        // 在30格范围内的玩家会收到通知
        double notifyRange = 30.0;
        World world = location.getWorld();

        for (Player player : world.getPlayers()) {
            if (player.getLocation().distance(location) <= notifyRange) {
                String message;

                if (monsterType.equalsIgnoreCase("zombie")) {
                    if (monsterId.equals("id1")) {
                        message = ChatColor.YELLOW + "附近出现了普通僵尸！";
                    } else {
                        message = ChatColor.RED + "附近出现了特殊僵尸！小心！";
                    }
                } else if (monsterType.equalsIgnoreCase("special")) {
                    message = ChatColor.DARK_RED + "警告！附近出现了危险的变异生物！";
                } else {
                    message = ChatColor.YELLOW + "附近出现了 " + monsterType + "！";
                }

                player.sendMessage(message);
            }
        }
    }

    /**
     * 获取游戏的最大回合数
     *
     * @param gameName 游戏名称
     * @return 最大回合数
     */
    private int getMaxRounds(String gameName) {
        ConfigurationSection config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return 5; // 默认5回合
        }

        return config.getInt("rounds", 5);
    }

    /**
     * 获取游戏当前回合数
     *
     * @param gameName 游戏名称
     * @return 当前回合数
     */
    public int getCurrentRound(String gameName) {
        return gameRounds.getOrDefault(gameName, 1);
    }

    /**
     * 获取游戏耗时字符串
     *
     * @param gameName 游戏名称
     * @return 格式化的游戏耗时字符串 (MM:SS)
     */
    public String getGameDurationString(String gameName) {
        if (scoreboardManager != null) {
            return scoreboardManager.getGameDuration(gameName);
        }
        return "00:00";
    }

    /**
     * 获取游戏出生点位置
     *
     * @param gameName 游戏名称
     * @return 出生点位置
     */
    private Location getGameSpawnLocation(String gameName) {
        ConfigurationSection config = gameManager.getGameConfig(gameName);
        if (config == null || !config.contains("spawn")) {
            return null;
        }

        ConfigurationSection spawnSection = config.getConfigurationSection("spawn");
        if (spawnSection == null) {
            return null;
        }

        String worldName = spawnSection.getString("world");
        if (worldName == null) {
            return null;
        }

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            return null;
        }

        double x = spawnSection.getDouble("x");
        double y = spawnSection.getDouble("y");
        double z = spawnSection.getDouble("z");

        return new Location(world, x, y, z);
    }

    /**
     * 计算游戏中当前存在的怪物数量，使用与ScoreboardManager一致的逻辑
     *
     * @param gameName 游戏名称
     * @return 怪物数量
     */
    private int countGameMonsters(String gameName) {
        int count = 0;

        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                // 使用与ScoreboardManager一致的检测逻辑
                if (isGameEntity(entity)) {
                    count++;
                }
            }
        }

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("游戏 " + gameName + " 当前怪物数量: " + count);
        }
        return count;
    }

    /**
     * 检查实体是否是游戏实体，与ScoreboardManager逻辑保持一致
     *
     * @param entity 实体
     * @return 是否是游戏实体
     */
    private boolean isGameEntity(Entity entity) {
        // 跳过玩家
        if (entity instanceof Player) {
            return false;
        }

        // 检查是否是生物实体
        if (!(entity instanceof LivingEntity)) {
            return false;
        }

        // 🔥 方案A修改：IDZ怪物现在计入游戏僵尸数量，参与游戏进度
        if (entity.hasMetadata("idz_special_spawn")) {
            return true; // 改为计入游戏实体
        }

        // 检查是否有IDZ怪物标识，如果是则计入游戏僵尸数量
        if (entity.hasMetadata("idz_monster")) {
            return true; // 改为计入游戏实体
        }

        // 检查是否有idcZombieEntity标记，如果有则视为游戏实体
        if (entity.hasMetadata("idcZombieEntity")) {
            return true;
        }

        // 检查是否有not_round_zombie标记，如果有则不计入回合生成的僵尸
        if (entity.hasMetadata("not_round_zombie")) {
            return false;
        }

        // 检查是否有游戏标记元数据
        if (entity.hasMetadata("gameEntity")) {
            return true;
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            return true;
        }

        // 检查是否是自定义僵尸类型
        if (entity.hasMetadata("customZombieType")) {
            for (MetadataValue meta : entity.getMetadata("customZombieType")) {
                if (meta.getOwningPlugin().equals(plugin)) {
                    String value = meta.asString();
                    // 检查是否是idc类型的实体
                    if (value.contains("idc")) {
                        // 添加idcZombieEntity标记，确保后续处理中被识别为僵尸
                        entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                        return true;
                    }
                }
            }
            return true;
        }

        // 额外检查：实体名称是否有特定前缀或包含特定字符串
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            // 检查是否是游戏实体的命名格式
            if (customName.startsWith("游戏_")) {
                return true;
            }

            // 检查是否包含id或idc标识
            if (customName.contains("id") || customName.contains("idc")) {
                // 如果是idc类型，添加idcZombieEntity标记
                if (customName.contains("idc")) {
                    entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                }
                return true;
            }

            // 检查是否包含idn标识
            if (customName.contains("idn")) {
                return true;
            }

            // 检查是否包含zombie或僵尸标识
            if (customName.contains("zombie") || customName.contains("僵尸")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清除游戏中的所有僵尸
     *
     * @param gameName 游戏名称
     */
    public void clearAllZombies(String gameName) {
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("清除游戏 " + gameName + " 中的所有僵尸");
        }
        int count = 0;

        // 取消该游戏的生成任务
        if (spawnTasks.containsKey(gameName)) {
            BukkitTask task = spawnTasks.get(gameName);
            if (task != null && !task.isCancelled()) {
                task.cancel();
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("已取消游戏 " + gameName + " 的僵尸生成任务");
                }
            }
            spawnTasks.remove(gameName);
        }

        // 清理所有IDZ怪物
        try {
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager = plugin.getIDZMonsterManager();
            if (idzManager != null) {
                idzManager.cleanupAllSpawnedMonsters();
                plugin.getLogger().info("已清理游戏 " + gameName + " 中的所有IDZ怪物");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("清理IDZ怪物时发生错误: " + e.getMessage());
        }

        // 遍历所有世界的实体
        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                boolean shouldRemove = false;

                // 检查是否是普通僵尸
                if (entity instanceof Zombie && !(entity instanceof Player) &&
                    !entity.hasMetadata("customNPC") && !entity.hasMetadata("soulNPC")) {
                    shouldRemove = true;
                }

                // 检查是否是游戏实体
                if (isGameEntity(entity) && !(entity instanceof Player)) {
                    shouldRemove = true;
                }

                // 特别检查IDZ怪物 - 即使它们被标记为不计入游戏数量，回合切换时也要清理
                if (entity.hasMetadata("idz_monster") || entity.hasMetadata("idz_special_spawn")) {
                    shouldRemove = true;
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("清理IDZ怪物: " + entity.getType() + " 名称: " + entity.getCustomName());
                    }
                }

                if (shouldRemove) {
                    entity.remove();
                    count++;

                    // 输出调试信息
                    if (count % 10 == 0) {
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("已清理 " + count + " 个实体");
                        }
                    }
                }
            }
        }

        // 只清除剩余生成数量，不清除回合数据
        if (remainingSpawns.containsKey(gameName)) {
            remainingSpawns.get(gameName).clear();
        }

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("共清除了 " + count + " 个实体");
        }
    }

    /**
     * 安全播放音效
     *
     * @param player 玩家
     * @param soundName 音效名称
     * @param defaultSound 默认音效
     * @param volume 音量
     * @param pitch 音调
     */
    private void playSoundSafely(Player player, String soundName, String defaultSound, float volume, float pitch) {
        try {
            // 如果音效名称为空，使用默认音效
            if (soundName == null || soundName.isEmpty()) {
                soundName = defaultSound;
            }

            // 转换音效名称格式
            String formattedSound = formatSoundName(soundName);

            // 尝试播放音效（使用字符串格式，避免Sound.valueOf）
            player.playSound(player.getLocation(), formattedSound, volume, pitch);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("成功播放音效: " + formattedSound);
            }
        } catch (Exception e) {
            // 如果播放失败，尝试使用默认音效
            plugin.getLogger().warning("无法播放音效: " + soundName + "，错误: " + e.getMessage() + "，尝试使用默认音效");
            try {
                // 转换默认音效名称格式
                String formattedDefaultSound = formatSoundName(defaultSound);

                player.playSound(player.getLocation(), formattedDefaultSound, volume, pitch);
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("成功播放默认音效: " + formattedDefaultSound);
                }
            } catch (Exception ex) {
                // 如果默认音效也失败，使用通用音效
                plugin.getLogger().warning("无法播放默认音效，错误: " + ex.getMessage() + "，使用通用音效");
                try {
                    player.playSound(player.getLocation(), "minecraft:entity.experience_orb.pickup", volume, pitch);
                } catch (Exception exc) {
                    // 如果所有尝试都失败，记录错误但不中断游戏
                    plugin.getLogger().warning("无法播放任何音效给玩家 " + player.getName());
                }
            }
        }
    }

    /**
     * 隐藏所有idc类型实体的名称
     *
     * @param world 世界
     */
    public void hideAllIdcEntityNames(World world) {
        if (world == null) {
            return;
        }

        // 只在调试模式下输出扫描日志，避免日志文件被大量重复信息填满
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("正在隐藏世界 " + world.getName() + " 中所有idc类型实体的名称");
        }

        int processedCount = 0; // 记录处理的实体数量

        // 遍历世界中的所有实体
        for (Entity entity : world.getEntities()) {
            if (entity instanceof LivingEntity) {
                LivingEntity livingEntity = (LivingEntity) entity;

                // 检查是否是idc类型实体
                boolean isIdcEntity = false;

                // 检查自定义名称
                if (livingEntity.getCustomName() != null && livingEntity.getCustomName().contains("idc")) {
                    isIdcEntity = true;
                }

                // 检查customZombieType元数据
                if (livingEntity.hasMetadata("customZombieType")) {
                    for (MetadataValue meta : livingEntity.getMetadata("customZombieType")) {
                        if (meta.getOwningPlugin().equals(plugin) && meta.asString().contains("idc")) {
                            isIdcEntity = true;
                            break;
                        }
                    }
                }

                // 如果是idc类型实体，设置名称不可见并添加特殊标记
                if (isIdcEntity) {
                    livingEntity.setCustomNameVisible(false);

                    // 添加特殊标记，表示这是一个idc类型的实体，应该被当做僵尸处理
                    if (!livingEntity.hasMetadata("idcZombieEntity")) {
                        livingEntity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                    }

                    processedCount++;

                    // 只在调试模式下输出详细的处理日志
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("已隐藏实体名称: " + livingEntity.getType() + ", 名称: " + livingEntity.getCustomName());
                    }
                }
            }
        }

        // 只有在实际处理了实体且开启调试模式时才输出汇总信息
        if (processedCount > 0 && plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("世界 " + world.getName() + " 中共处理了 " + processedCount + " 个idc类型实体");
        }
    }

    /**
     * 格式化音效名称为Minecraft 1.21格式
     *
     * @param soundName 原始音效名称
     * @return 格式化后的音效名称
     */
    private String formatSoundName(String soundName) {
        // 移除可能存在的minecraft:前缀
        if (soundName.startsWith("minecraft:")) {
            soundName = soundName.substring(10);
        }

        // 转换为小写
        soundName = soundName.toLowerCase();

        // 将下划线替换为点
        soundName = soundName.replace("_", ".");

        // 添加minecraft:前缀
        if (!soundName.contains(":")) {
            soundName = "minecraft:" + soundName;
        }

        return soundName;
    }

    /**
     * 更新游戏回合
     *
     * @param gameName 游戏名称
     * @param round 新的回合数
     */
    public void updateGameRound(String gameName, int round) {
        // 检查游戏是否已结束，如果是则不执行回合更新
        if (scoreboardManager != null && scoreboardManager.isGameEnded(gameName)) {
            plugin.getLogger().info("游戏 " + gameName + " 已标记为结束，跳过回合更新逻辑");
            return;
        }

        // 先更新本类中的回合数
        gameRounds.put(gameName, round);

        // 初始化新回合的剩余生成数量
        if (!remainingSpawns.containsKey(gameName)) {
            remainingSpawns.put(gameName, new HashMap<>());
        }

        // 每次都重新计算新回合的僵尸数量，避免使用缓存
        int zombieCount = getZombieCountForRound(gameName, round);
        remainingSpawns.get(gameName).put(round, zombieCount);

        // 初始化新回合的生成点计数器
        initializeSpawnPointCounters(gameName, round);

        // 🔥 重新启动生成任务以使用新回合的生成间隔
        restartSpawnTaskForNewRound(gameName, round);

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("ZombieSpawnManager: 游戏 " + gameName + " 回合已更新为 " + round);
        }

        // 更新计分板中的回合信息
        if (scoreboardManager != null) {
            try {
                // 计算该回合的总僵尸数量
                int totalZombies = scoreboardManager.getTotalZombiesForRound(gameName, round);
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("计算得到游戏 " + gameName + " 第 " + round + " 回合的总僵尸数量: " + totalZombies);
                }

                // 先更新计分板管理器中的回合信息
                scoreboardManager.updateRoundInfo(gameName, round);

                // 等待一小段时间，确保回合信息已更新
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    try {
                        // 设置剩余僵尸数量，这将触发计分板更新
                        scoreboardManager.setRemainingZombiesInRound(gameName, totalZombies);

                        // 强制更新所有玩家的计分板显示
                        scoreboardManager.updateZombieCountDisplay(gameName);
                    } catch (Exception e) {
                        plugin.getLogger().warning("延迟更新计分板时发生错误: " + e.getMessage());
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            e.printStackTrace();
                        }
                    }
                }, 10L); // 延迟10tick后执行，确保回合信息已更新

            } catch (Exception e) {
                plugin.getLogger().warning("更新计分板信息时发生错误: " + e.getMessage());
                if (plugin.getConfig().getBoolean("debug", false)) {
                    e.printStackTrace();
                }
            }
        } else {
            plugin.getLogger().warning("ScoreboardManager未初始化，无法更新计分板回合信息");
        }

        // 重新设置购买点NPC
        Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                // 通过反射调用GameSessionManager的setupBuyPoints方法
                java.lang.reflect.Method setupBuyPointsMethod = gameSessionManager.getClass().getDeclaredMethod("setupBuyPoints", String.class);
                setupBuyPointsMethod.setAccessible(true);
                setupBuyPointsMethod.invoke(gameSessionManager, gameName);
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("成功重新设置游戏 " + gameName + " 的购买点NPC");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("重新设置购买点NPC失败: " + e.getMessage());
                if (plugin.getConfig().getBoolean("debug", false)) {
                    e.printStackTrace();
                }
            }
        });

        // 向所有玩家发送回合开始的Title提示
        for (UUID uuid : gameSessionManager.getGameParticipants(gameName)) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                // 发送消息通知
                player.sendMessage(ChatColor.GREEN + "第 " + round + " 回合开始！");

                // 使用MessageManager发送Title提示
                try {
                    plugin.getMessageManager().sendRoundTitleMessage(player, round, gameName);
                } catch (Exception titleEx) {
                    // 如果MessageManager发送失败，使用备用的硬编码Title
                    try {
                        player.sendTitle(
                                ChatColor.GOLD + "第 " + round + " 回合",
                                ChatColor.YELLOW + "准备迎战新的挑战！",
                                10, 70, 20
                        );
                    } catch (Exception backupTitleEx) {
                        plugin.getLogger().warning("备用Title发送也失败: " + backupTitleEx.getMessage());
                        // 发送普通消息代替
                        player.sendMessage(ChatColor.GOLD + "第 " + round + " 回合");
                        player.sendMessage(ChatColor.YELLOW + "准备迎战新的挑战！");
                    }
                }

                // 播放回合切换音效 - 使用僵尸主题+教堂风格的音效组合
                try {
                        // 从配置文件获取回合切换音效
                        String configSound = plugin.getConfig().getString("sounds.round_change", "BLOCK_BELL_RESONATE");

                        // 尝试播放配置的音效
                        try {
                            // 将配置的音效名称转换为大写并替换点为下划线
                            String soundName = configSound.toUpperCase().replace(".", "_");

                            // 输出调试信息，无论debug模式是否开启
                            plugin.getLogger().info("尝试播放音效: " + soundName);

                            // 使用安全播放方法
                            playSoundSafely(player, formatSoundName(soundName), "entity.experience_orb.pickup", 1.0f, 0.5f);
                            plugin.getLogger().info("成功播放音效: " + soundName);
                        } catch (Exception soundException) {
                            // 如果配置的音效无效，使用默认音效组合
                            plugin.getLogger().warning("配置的回合切换音效无效: " + configSound + "，使用默认音效组合，错误: " + soundException.getMessage());

                            // 播放主音效 - 钟声回响（教堂风格）
                            playSoundSafely(player, "block.bell.resonate", "entity.experience_orb.pickup", 1.0f, 0.5f);
                            plugin.getLogger().info("成功播放默认音效: BLOCK_BELL_RESONATE");
                        }

                        // 延迟5tick后播放第二个音效 - 钟声敲击（教堂风格）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "block.bell.use", "block.bell.use", 1.0f, 0.6f);
                        }, 5L);

                        // 延迟10tick后播放第三个音效 - 僵尸村民转换（僵尸主题）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "entity.zombie_villager.converted", "entity.zombie_villager.converted", 0.8f, 0.7f);
                        }, 10L);

                        // 延迟15tick后播放第四个音效 - 僵尸嘶吼（僵尸主题）
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            playSoundSafely(player, "entity.zombie.ambient", "entity.zombie.ambient", 1.0f, 0.5f);
                        }, 15L);

                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("成功播放回合切换音效组合");
                        }
                    } catch (Exception soundEx) {
                        plugin.getLogger().warning("无法播放回合切换音效: " + soundEx.getMessage());
                        // 尝试使用默认音效
                        playSoundSafely(player, "block.bell.resonate", "entity.experience_orb.pickup", 1.0f, 0.5f);
                    }
            }
        }

        // 激活幸运箱
        if (plugin.getLuckyBoxManager() != null) {
            plugin.getLuckyBoxManager().activateLuckyBoxes(gameName, round);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("已激活游戏 " + gameName + " 第 " + round + " 回合的幸运箱");
            }
        }

        // 执行回合复活逻辑（仅在非第一回合时执行）
        if (round > 1) {
            performRoundRevival(gameName);
        }
    }

    /**
     * 结束游戏并执行清理工作
     *
     * @param gameName 游戏名称
     */
    public void endGameAndCleanup(String gameName) {
        // 获取游戏参与者
        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);

        // 在清理游戏开始时间之前，先获取游戏耗时用于Title显示
        String gameDuration = getGameDurationString(gameName);
        int survivedRounds = getCurrentRound(gameName);
        plugin.getLogger().info("游戏结束时获取到的耗时: " + gameDuration + ", 回合数: " + survivedRounds);

        // 记录游戏结束时的统计数据（在清理游戏开始时间之前）
        recordGameEndStatistics(gameName, participants);

        // 统计数据保存完成后，清理游戏开始时间
        if (scoreboardManager != null) {
            scoreboardManager.clearGameStartTime(gameName);
            plugin.getLogger().info("已清理游戏 " + gameName + " 的开始时间记录");
        }

        // 获取游戏是否仍然启用（在结束游戏前检查）
        boolean isGameEnabled = gameManager.isGameEnabled(gameName);

        // 结束游戏
        gameSessionManager.endGame(gameName);

        // 清理所有生成的实体
        cleanupGameEntities(gameName);

        // 标记游戏正在显示结束计分板，防止定时更新覆盖
        if (scoreboardManager != null) {
            scoreboardManager.markGameShowingEndScoreboard(gameName);
        }

        // 重置玩家状态
        for (UUID uuid : participants) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                // 清理玩家的游戏统计数据
                plugin.getPlayerInteractionManager().clearPlayerGameStatistics(player);

                resetPlayerState(player);

                // 设置游戏结束计分板
                updatePlayerGameEndScoreboard(player, gameName);

                // 发送游戏结束的Title通知（使用之前获取的游戏耗时）
                try {
                    // 使用MessageManager发送游戏失败Title
                    plugin.getMessageManager().sendGameEndTitleMessage(player, "defeat", gameName, gameDuration, survivedRounds);

                    // 播放游戏结束音效
                    try {
                        String soundName = plugin.getConfig().getString("sounds.game_end", "entity.ender_dragon.death");
                        // 使用安全播放方法
                        playSoundSafely(player, soundName, "entity.ender_dragon.death", 1.0f, 1.0f);
                        plugin.getLogger().info("向玩家 " + player.getName() + " 播放了游戏结束音效: " + soundName);
                    } catch (Exception soundEx) {
                        plugin.getLogger().warning("播放游戏结束音效失败: " + soundEx.getMessage());
                    }

                    // 尝试传送玩家到大厅位置
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        try {
                            // 获取全局大厅位置
                            Location lobbyLocation = plugin.getLobbyManager().getLobbyLocation();
                            if (lobbyLocation != null) {
                                // 传送玩家到大厅
                                player.teleport(lobbyLocation);
                                player.sendMessage(ChatColor.GREEN + "游戏结束，你已被传送到大厅");
                                plugin.getLogger().info("玩家 " + player.getName() + " 已被传送到全局大厅");
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("传送玩家 " + player.getName() + " 到大厅失败: " + e.getMessage());
                        }
                    }, 20L); // 延迟1秒后传送，确保其他清理工作已完成

                } catch (Exception titleEx) {
                    plugin.getLogger().warning("向玩家发送游戏结束Title失败");
                    // 不再尝试使用TitleAPI
                    plugin.getLogger().warning("无法发送游戏结束Title");
                    // 发送普通消息代替
                    player.sendMessage("§c游戏结束");
                    player.sendMessage("§6感谢参与！");

                    // 播放游戏结束音效
                    try {
                        String soundName = plugin.getConfig().getString("sounds.game_end", "entity.ender_dragon.death");
                        // 使用安全播放方法
                        playSoundSafely(player, soundName, "entity.ender_dragon.death", 1.0f, 1.0f);
                        plugin.getLogger().info("向玩家 " + player.getName() + " 播放了游戏结束音效: " + soundName);
                    } catch (Exception soundEx) {
                        plugin.getLogger().warning("播放游戏结束音效失败: " + soundEx.getMessage());
                    }

                    // 尝试传送玩家到大厅位置
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        try {
                            // 获取全局大厅位置
                            Location lobbyLocation = plugin.getLobbyManager().getLobbyLocation();
                            if (lobbyLocation != null) {
                                // 传送玩家到大厅
                                player.teleport(lobbyLocation);
                                player.sendMessage(ChatColor.GREEN + "游戏结束，你已被传送到大厅");
                                plugin.getLogger().info("玩家 " + player.getName() + " 已被传送到全局大厅");
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("传送玩家 " + player.getName() + " 到大厅失败: " + e.getMessage());
                        }
                    }, 20L); // 延迟1秒后传送，确保其他清理工作已完成
                }
            }
        }

        // 取消所有相关任务
        if (spawnTasks.containsKey(gameName) && !spawnTasks.get(gameName).isCancelled()) {
            spawnTasks.get(gameName).cancel();
            spawnTasks.remove(gameName);
        }

        // 清理游戏相关数据
        gameRounds.remove(gameName);
        remainingSpawns.remove(gameName);

        // 清理幸运箱
        if (plugin.getLuckyBoxManager() != null) {
            plugin.getLuckyBoxManager().clearLuckyBoxes(gameName);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("已清理游戏 " + gameName + " 的幸运箱");
            }
        }

        // 重置计分板中的其他数据（游戏开始时间已在统计数据保存后清理）
        if (scoreboardManager != null) {
            plugin.getLogger().info("游戏结束时重置计分板中的计时器和僵尸数量数据");
            // 重置计时器和僵尸数量，但不清理游戏开始时间（已在统计数据保存后清理）
            scoreboardManager.resetGameTimers(gameName);
        }

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("游戏 " + gameName + " 已结束并清理");
        }

        // 如果游戏仍然启用，延迟一个tick后重置游戏状态
        if (isGameEnabled) {
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // 使用新的resetGame方法重置游戏状态
                boolean success = gameSessionManager.resetGame(gameName);
                if (success) {
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("游戏 " + gameName + " 已重置为等待状态，现在可以再次加入");
                    }
                } else {
                    plugin.getLogger().warning("重置游戏 " + gameName + " 状态失败");
                }
            }, 1L);
        }
    }

    /**
     * 清理游戏中生成的所有实体
     *
     * @param gameName 游戏名称
     */
    private void cleanupGameEntities(String gameName) {
        // 清理所有带有游戏标记的实体
        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                if (entity.hasMetadata("gameEntity")) {
                    entity.remove();
                }
            }
        }

        // 清理NPC
        if (zombieHelper != null) {
            zombieHelper.cleanupAllNPCs();
        }
    }

    /**
     * 重置玩家状态
     *
     * @param player 玩家
     */
    private void resetPlayerState(Player player) {
        // 清除玩家背包和装备
        player.getInventory().clear();
        player.getEquipment().clear();

        // 重置玩家游戏模式
        player.setGameMode(GameMode.ADVENTURE);

        // 清除玩家游戏中金钱（通过ShootPlugin）
        plugin.getShootPluginHelper().setPlayerMoney(player, 0);

        // 重置玩家生命值和饥饿值
        player.setHealth(player.getMaxHealth());
        player.setFoodLevel(20);

        // 清除所有药水效果
        for (PotionEffect effect : player.getActivePotionEffects()) {
            player.removePotionEffect(effect.getType());
        }

        // 清除玩家的临时数据，包括倒下和死亡状态
        plugin.getPlayerInteractionManager().clearPlayerData(player, null);

        player.sendMessage("§e你的状态已重置");
    }

    /**
     * 清理所有任务
     */
    public void shutdown() {
        for (BukkitTask task : spawnTasks.values()) {
            if (!task.isCancelled()) {
                task.cancel();
            }
        }

        spawnTasks.clear();
        gameRounds.clear();
        remainingSpawns.clear();
    }

    /**
     * 获取实体的元数据字符串值
     *
     * @param entity 实体
     * @param key 元数据键
     * @return 元数据值，如果不存在则返回空字符串
     */
    private String getMetadataString(Entity entity, String key) {
        if (entity.hasMetadata(key)) {
            for (MetadataValue value : entity.getMetadata(key)) {
                if (value.getOwningPlugin() == plugin) {
                    return value.asString();
                }
            }
        }
        return "";
    }

    /**
     * 处理游戏胜利逻辑，包括统计数据显示和等待时间
     *
     * @param gameName 游戏名称
     * @param maxRound 最大回合数
     */
    private void handleGameVictory(String gameName, int maxRound) {
        // 第一步清理：立即停止所有可能导致下一回合的逻辑
        performFirstStepCleanup(gameName);

        // 清理游戏相关数据，防止重新启动
        gameRounds.remove(gameName);
        remainingSpawns.remove(gameName);

        // 检查是否启用统计数据显示
        boolean statsEnabled = plugin.getConfig().getBoolean("game.post_game_statistics.enabled", true);
        int waitTime = plugin.getConfig().getInt("game.post_game_statistics.wait_time_seconds", 10);

        // 在游戏胜利处理开始时就获取游戏耗时，避免后续清理导致数据丢失
        String gameDuration = getGameDurationString(gameName);
        plugin.getLogger().info("游戏胜利时获取到的耗时: " + gameDuration);

        // 向所有玩家发送游戏胜利消息
        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        for (UUID uuid : participants) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                // 发送胜利消息
                player.sendMessage(ChatColor.GOLD + "恭喜！你已完成所有 " + maxRound + " 回合！游戏胜利！");

                // 使用MessageManager发送游戏胜利Title（使用之前获取的游戏耗时）
                plugin.getMessageManager().sendGameEndTitleMessage(player, "victory", gameName, gameDuration, maxRound);

                // 播放游戏胜利音效
                try {
                    String soundName = plugin.getConfig().getString("sounds.game_win", "ui.toast.challenge_complete");
                    // 使用安全播放方法
                    playSoundSafely(player, soundName, "ui.toast.challenge_complete", 1.0f, 1.0f);
                } catch (Exception soundEx) {
                    plugin.getLogger().warning("无法播放游戏胜利音效: " + soundEx.getMessage());
                    // 尝试使用默认音效
                    try {
                        playSoundSafely(player, "ui.toast.challenge_complete", "entity.experience_orb.pickup", 1.0f, 1.0f);
                    } catch (Exception ex) {
                        plugin.getLogger().warning("播放默认游戏胜利音效也失败: " + ex.getMessage());
                    }
                }

                // 如果启用统计数据显示，显示统计信息
                if (statsEnabled) {
                    displayPlayerStatistics(player, gameName);
                    // 在显示统计数据的同时，更新玩家的游戏结束计分板
                    updatePlayerGameEndScoreboard(player, gameName);
                }
            }
        }

        // 如果启用统计数据显示且等待时间大于0，则等待指定时间后结束游戏
        if (statsEnabled && waitTime > 0) {
            // 启动倒计时任务
            startPostGameCountdown(gameName, waitTime);
        } else {
            // 立即结束游戏并清理
            plugin.getLogger().info("游戏 " + gameName + " 胜利，立即结束游戏并清理");
            endGameAndCleanup(gameName);
        }
    }

    /**
     * 第一步清理：立即停止所有可能导致下一回合的逻辑
     * 包括停止生成任务、计分板计时器，并切换到游戏结束计分板
     *
     * @param gameName 游戏名称
     */
    private void performFirstStepCleanup(String gameName) {
        plugin.getLogger().info("开始执行游戏 " + gameName + " 的第一步清理");

        // 1. 立即停止生成任务，防止游戏胜利后继续生成僵尸
        if (spawnTasks.containsKey(gameName) && !spawnTasks.get(gameName).isCancelled()) {
            spawnTasks.get(gameName).cancel();
            spawnTasks.remove(gameName);
            plugin.getLogger().info("已停止游戏 " + gameName + " 的僵尸生成任务");
        }

        // 2. 立即停止计分板计时器，防止计时器干扰游戏结束
        if (scoreboardManager != null) {
            scoreboardManager.stopGameTimers(gameName);
            plugin.getLogger().info("已停止游戏 " + gameName + " 的计分板计时器");
        }

        // 3. 切换到游戏结束计分板
        switchToGameEndScoreboard(gameName);

        plugin.getLogger().info("游戏 " + gameName + " 第一步清理完成");
    }

    /**
     * 切换到游戏结束计分板
     *
     * @param gameName 游戏名称
     */
    private void switchToGameEndScoreboard(String gameName) {
        // 标记游戏正在显示结束计分板，防止定时更新覆盖
        if (scoreboardManager != null) {
            scoreboardManager.markGameShowingEndScoreboard(gameName);
        }

        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        for (UUID uuid : participants) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                updatePlayerGameEndScoreboard(player, gameName);
            }
        }
    }

    /**
     * 更新玩家的游戏结束计分板
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void updatePlayerGameEndScoreboard(Player player, String gameName) {
        try {
            Scoreboard scoreboard = Bukkit.getScoreboardManager().getNewScoreboard();
            Objective objective = scoreboard.registerNewObjective("gameEnd", "dummy",
                ChatColor.translateAlternateColorCodes('&',
                    plugin.getConfig().getString("game.post_game_scoreboard.title", "&6&l游戏结束")));
            objective.setDisplaySlot(DisplaySlot.SIDEBAR);

            // 获取配置的游戏结束计分板内容
            List<String> lines = plugin.getConfig().getStringList("game.post_game_scoreboard.lines");
            if (lines.isEmpty()) {
                // 默认内容
                lines = Arrays.asList(
                    "&7==================",
                    "&a&l游戏胜利！",
                    "&7",
                    "&e正在统计数据...",
                    "&7",
                    "&c即将返回大厅",
                    "&7=================="
                );
            }

            // 设置计分板内容
            int score = lines.size();
            for (String line : lines) {
                String formattedLine = ChatColor.translateAlternateColorCodes('&', line);
                objective.getScore(formattedLine).setScore(score--);
            }

            player.setScoreboard(scoreboard);
            plugin.getLogger().info("已为玩家 " + player.getName() + " 设置游戏结束计分板");
        } catch (Exception e) {
            plugin.getLogger().warning("为玩家 " + player.getName() + " 设置游戏结束计分板时出错: " + e.getMessage());
        }
    }

    /**
     * 显示玩家的游戏统计数据
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void displayPlayerStatistics(Player player, String gameName) {
        // 获取玩家统计数据
        PlayerInteractionManager.PlayerGameStatistics stats =
            plugin.getPlayerInteractionManager().getPlayerGameStatistics(player, gameName);

        // 获取消息模板
        String header = plugin.getConfig().getString("game.post_game_statistics.messages.header",
            "&6&l========== &e游戏统计 &6&l==========");
        String zombieKillsMsg = plugin.getConfig().getString("game.post_game_statistics.messages.zombie_kills",
            "&a击杀僵尸: &f{zombie_kills} 只");
        String moneyEarnedMsg = plugin.getConfig().getString("game.post_game_statistics.messages.money_earned",
            "&a获得金钱: &f{money_earned} 金币");
        String doorsOpenedMsg = plugin.getConfig().getString("game.post_game_statistics.messages.doors_opened",
            "&a开启门数: &f{doors_opened} 扇");
        String moneySpentMsg = plugin.getConfig().getString("game.post_game_statistics.messages.money_spent",
            "&a花费金钱: &f{money_spent} 金币");
        String windowsRepairedMsg = plugin.getConfig().getString("game.post_game_statistics.messages.windows_repaired",
            "&a修复窗户: &f{windows_repaired} 扇");
        String footer = plugin.getConfig().getString("game.post_game_statistics.messages.footer",
            "&6&l==========================");

        // 替换变量并发送消息
        player.sendMessage(ChatColor.translateAlternateColorCodes('&', header));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&',
            zombieKillsMsg.replace("{zombie_kills}", String.valueOf(stats.getZombieKills()))));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&',
            moneyEarnedMsg.replace("{money_earned}", String.format("%.1f", stats.getMoneyEarned()))));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&',
            doorsOpenedMsg.replace("{doors_opened}", String.valueOf(stats.getDoorsOpened()))));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&',
            moneySpentMsg.replace("{money_spent}", String.format("%.1f", stats.getMoneySpent()))));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&',
            windowsRepairedMsg.replace("{windows_repaired}", String.valueOf(stats.getWindowsRepaired()))));
        player.sendMessage(ChatColor.translateAlternateColorCodes('&', footer));
    }

    /**
     * 启动游戏结束后的倒计时任务
     *
     * @param gameName 游戏名称
     * @param waitTime 等待时间（秒）
     */
    private void startPostGameCountdown(String gameName, int waitTime) {
        new BukkitRunnable() {
            int remainingTime = waitTime;

            @Override
            public void run() {
                Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);

                // 如果没有玩家了，直接结束
                if (participants.isEmpty()) {
                    this.cancel();
                    endGameAndCleanup(gameName);
                    return;
                }

                // 向所有玩家发送倒计时消息
                if (remainingTime > 0) {
                    String countdownMsg = plugin.getConfig().getString("game.post_game_statistics.messages.teleport_countdown",
                        "&e{seconds} 秒后传送到大厅...");
                    String message = ChatColor.translateAlternateColorCodes('&',
                        countdownMsg.replace("{seconds}", String.valueOf(remainingTime)));

                    for (UUID uuid : participants) {
                        Player player = Bukkit.getPlayer(uuid);
                        if (player != null && player.isOnline()) {
                            player.sendMessage(message);
                        }
                    }
                    remainingTime--;
                } else {
                    // 倒计时结束，结束游戏
                    this.cancel();
                    endGameAndCleanup(gameName);
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
    }

    /**
     * 执行回合复活逻辑
     * 复活所有已死亡和已倒下的队友
     *
     * @param gameName 游戏名称
     */
    private void performRoundRevival(String gameName) {
        // 检查是否启用回合复活功能
        if (!plugin.getConfig().getBoolean("game.round_revival.enabled", true)) {
            return;
        }

        plugin.getLogger().info("开始执行游戏 " + gameName + " 的回合复活逻辑");

        // 获取游戏参与者
        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        if (participants.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有参与者，跳过回合复活");
            return;
        }

        // 获取游戏出生点位置
        Location spawnLocation = getGameSpawnLocation(gameName);
        boolean respawnAtSpawn = plugin.getConfig().getBoolean("game.round_revival.respawn_at_spawn", true);

        // 获取复活配置
        double healthPercentage = plugin.getConfig().getDouble("game.round_revival.health_percentage", 1.0);
        String revivalSound = plugin.getConfig().getString("game.round_revival.revival_sound", "entity.player.levelup");
        String revivalParticle = plugin.getConfig().getString("game.round_revival.revival_particle", "HEART");
        String revivalMessage = plugin.getConfig().getString("game.round_revival.revival_message", "&a你已在新回合中复活！");

        int revivedCount = 0;

        for (UUID playerId : participants) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                continue;
            }

            // 检查玩家状态（检查两个地方的状态数据以确保兼容性）
            String playerStatus = plugin.getPlayerInteractionManager().getPlayerData(player, "status");
            String deathReviveStatus = null;

            // 从PlayerDeathReviveListener获取状态
            if (plugin.getPlayerDeathReviveListener() != null) {
                // 通过反射或公共方法获取状态（这里我们检查玩家的游戏模式作为替代）
                if (player.getGameMode() == GameMode.SPECTATOR) {
                    deathReviveStatus = "downed"; // 旁观者模式通常表示倒下状态
                }
            }

            boolean needsRevival = false;

            // 检查PlayerInteractionManager中的状态
            if (playerStatus != null && (playerStatus.equals("downed") || playerStatus.equals("dead"))) {
                needsRevival = true;
                plugin.getLogger().info("玩家 " + player.getName() + " 在PlayerInteractionManager中的状态为: " + playerStatus + "，需要复活");
            }

            // 检查玩家游戏模式（旁观者模式通常表示死亡/倒下状态）
            if (player.getGameMode() == GameMode.SPECTATOR) {
                needsRevival = true;
                plugin.getLogger().info("玩家 " + player.getName() + " 处于旁观者模式，需要复活");
            }

            if (needsRevival) {
                // 复活玩家
                revivePlayer(player, spawnLocation, respawnAtSpawn, healthPercentage, revivalSound, revivalParticle, revivalMessage);
                revivedCount++;
            }
        }

        if (revivedCount > 0) {
            plugin.getLogger().info("回合复活完成，共复活了 " + revivedCount + " 名玩家");

            // 向所有玩家广播复活消息
            for (UUID playerId : participants) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    player.sendMessage(ChatColor.GREEN + "新回合开始！已复活 " + revivedCount + " 名队友！");
                }
            }
        } else {
            plugin.getLogger().info("回合复活检查完成，没有需要复活的玩家");
        }
    }

    /**
     * 复活单个玩家
     *
     * @param player 玩家
     * @param spawnLocation 出生点位置
     * @param respawnAtSpawn 是否传送到出生点
     * @param healthPercentage 复活后的生命值百分比
     * @param revivalSound 复活音效
     * @param revivalParticle 复活粒子效果
     * @param revivalMessage 复活消息
     */
    private void revivePlayer(Player player, Location spawnLocation, boolean respawnAtSpawn,
                             double healthPercentage, String revivalSound, String revivalParticle, String revivalMessage) {
        try {
            // 清除玩家的倒下/死亡状态
            plugin.getPlayerInteractionManager().removePlayerData(player, "status");

            // 清理玩家的灵魂NPC和相关数据（修复尸体不清理的bug）
            if (plugin.getPlayerDeathReviveListener() != null) {
                plugin.getPlayerDeathReviveListener().cleanupPlayerSoulNPC(player);
                plugin.getLogger().info("已清理玩家 " + player.getName() + " 的灵魂NPC（回合复活）");
            }

            // 设置玩家为冒险模式
            player.setGameMode(GameMode.ADVENTURE);

            // 恢复玩家生命值
            double maxHealth = player.getMaxHealth();
            double newHealth = maxHealth * healthPercentage;
            player.setHealth(Math.min(newHealth, maxHealth));

            // 恢复饥饿值
            player.setFoodLevel(20);

            // 如果需要传送到出生点且出生点存在
            if (respawnAtSpawn && spawnLocation != null) {
                player.teleport(spawnLocation);
            }

            // 添加发光效果
            player.setGlowing(true);

            // 发送复活消息
            String message = ChatColor.translateAlternateColorCodes('&', revivalMessage);
            player.sendMessage(message);

            // 发送Title
            try {
                player.sendTitle(
                        ChatColor.GREEN + "已复活",
                        ChatColor.YELLOW + "新回合开始！",
                        10, 70, 20
                );
            } catch (Exception e) {
                plugin.getLogger().warning("发送复活Title失败: " + e.getMessage());
            }

            // 播放复活音效
            try {
                playSoundSafely(player, revivalSound, "entity.player.levelup", 1.0f, 1.0f);
            } catch (Exception e) {
                plugin.getLogger().warning("播放复活音效失败: " + e.getMessage());
            }

            // 生成粒子效果
            try {
                Location playerLoc = player.getLocation();
                // 在玩家周围生成心形粒子
                for (int i = 0; i < 10; i++) {
                    double offsetX = (Math.random() - 0.5) * 2;
                    double offsetY = Math.random() * 2;
                    double offsetZ = (Math.random() - 0.5) * 2;
                    Location particleLoc = playerLoc.clone().add(offsetX, offsetY, offsetZ);

                    // 使用字符串形式的粒子名称，避免版本兼容问题
                    player.getWorld().spawnParticle(org.bukkit.Particle.HEART, particleLoc, 1);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("生成复活粒子效果失败: " + e.getMessage());
            }

            plugin.getLogger().info("成功复活玩家: " + player.getName());

        } catch (Exception e) {
            plugin.getLogger().severe("复活玩家 " + player.getName() + " 时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 记录游戏结束时的统计数据
     *
     * @param gameName 游戏名称
     * @param participants 参与者列表
     */
    private void recordGameEndStatistics(String gameName, Set<UUID> participants) {
        try {
            // 获取当前回合数
            int currentRound = getCurrentRound(gameName);

            // 获取游戏耗时（从计分板管理器获取）
            long gameDuration = 0;
            if (scoreboardManager != null) {
                String durationString = scoreboardManager.getGameDuration(gameName);
                plugin.getLogger().info("获取到游戏 " + gameName + " 的耗时字符串: " + durationString);

                // 解析时间字符串 (MM:SS) 转换为毫秒
                try {
                    if (durationString != null && !durationString.isEmpty() && !durationString.equals("00:00")) {
                        String[] parts = durationString.split(":");
                        if (parts.length == 2) {
                            int minutes = Integer.parseInt(parts[0].trim());
                            int seconds = Integer.parseInt(parts[1].trim());
                            gameDuration = (minutes * 60 + seconds) * 1000L;
                            plugin.getLogger().info("成功解析游戏耗时: " + minutes + "分" + seconds + "秒 = " + gameDuration + "毫秒");
                        } else {
                            plugin.getLogger().warning("游戏耗时格式不正确，期望 MM:SS 格式，实际: " + durationString);
                        }
                    } else {
                        plugin.getLogger().warning("游戏耗时为空或为默认值 00:00，可能游戏开始时间未正确记录");
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("解析游戏耗时失败: " + durationString + ", 错误: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                plugin.getLogger().warning("ScoreboardManager为null，无法获取游戏耗时");
            }

            // 检查是否为胜利（达到最大回合数）
            boolean isVictory = false;
            if (gameManager.gameExists(gameName)) {
                int maxRounds = getMaxRounds(gameName);
                isVictory = currentRound >= maxRounds;
            }

            plugin.getLogger().info("记录游戏 " + gameName + " 结束统计 - 回合: " + currentRound +
                                  ", 耗时: " + gameDuration + "ms, 胜利: " + isVictory);

            // 为所有参与者记录统计数据
            for (UUID playerId : participants) {
                Player player = plugin.getServer().getPlayer(playerId);
                if (player != null) {
                    // 记录生存回合数
                    plugin.getPlayerStatisticsManager().addRoundsSurvived(player, currentRound);

                    // 如果胜利，记录地图完成时间
                    if (isVictory && gameDuration > 0) {
                        plugin.getPlayerStatisticsManager().updateMapRecord(player, gameName, currentRound, gameDuration);
                        plugin.getLogger().info("为玩家 " + player.getName() + " 记录地图胜利记录");
                    } else {
                        // 即使失败也要更新最大生存回合数
                        plugin.getPlayerStatisticsManager().updateMapRecord(player, gameName, currentRound, 0);
                    }

                    plugin.getLogger().info("已为玩家 " + player.getName() + " 记录游戏结束统计");
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("记录游戏结束统计数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
