package org.Ver_zhzh.deathZombieV4.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import org.bukkit.ChatColor;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * 怪物ID映射器 - 处理DeathZombie的ID映射 提供各种工具方法用于ID格式转换与标准化
 * 直接为CustomZombie.spawnCustomZombieDirect方法准备ID
 */
public class MonsterIdMapper {

    private final JavaPlugin plugin;
    private final Logger logger;

    // 缓存已知的怪物名称映射
    private final Map<String, String> monsterNameCache = new HashMap<>();

    public MonsterIdMapper(JavaPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        initializeMonsterTypes();
    }

    /**
     * 初始化怪物类型映射
     */
    private void initializeMonsterTypes() {
        // 预设一些常用的怪物名称映射
        monsterNameCache.put("id1", "普通僵尸");
        monsterNameCache.put("id2", "小僵尸");
        monsterNameCache.put("id3", "路障僵尸");
        monsterNameCache.put("id4", "钻斧僵尸");
        monsterNameCache.put("id5", "剧毒僵尸");
        monsterNameCache.put("id10", "法师僵尸");
        monsterNameCache.put("id11", "爆炸僵尸");
        monsterNameCache.put("id13", "毁灭僵尸");
        monsterNameCache.put("id15", "暗影僵尸");
        monsterNameCache.put("id16", "雷霆僵尸");

        monsterNameCache.put("idc1", "变异僵尸01");
        monsterNameCache.put("idc2", "变异僵尸02");
        monsterNameCache.put("idc3", "变异烈焰人");
        monsterNameCache.put("idc4", "变异爬行者");
        monsterNameCache.put("idc5", "变异末影螨");
        monsterNameCache.put("idc6", "变异蜘蛛");
        monsterNameCache.put("idc13", "变异僵尸3");
        monsterNameCache.put("idc16", "暗影潜影贝");
        monsterNameCache.put("idc21", "凋零领主");
        monsterNameCache.put("idc22", "异变之王");

        monsterNameCache.put("idn1", "感染者1史蒂夫");
        monsterNameCache.put("idn2", "感染者2艾利克斯");
        monsterNameCache.put("idn3", "感染者农民");
        monsterNameCache.put("idn4", "感染者居民");
        monsterNameCache.put("idn5", "感染猪");
    }

    /**
     * 获取怪物的友好名称
     *
     * @param monsterId 怪物ID (如id1, idc2, idn3)
     * @return 怪物的友好名称
     */
    public String getMonsterName(String monsterId) {
        if (monsterId == null || monsterId.isEmpty()) {
            return "未知怪物";
        }

        // 去除可能存在的前缀
        String normalizedId = stripPrefixesFromId(monsterId);

        // 检查缓存中是否有该怪物的名称
        if (monsterNameCache.containsKey(normalizedId)) {
            return monsterNameCache.get(normalizedId);
        }

        // 根据ID格式构造默认名称
        if (normalizedId.startsWith("id")) {
            return "僵尸 #" + normalizedId.substring(2);
        } else if (normalizedId.startsWith("idc")) {
            return "自定义实体 #" + normalizedId.substring(3);
        } else if (normalizedId.startsWith("idn")) {
            return "NPC #" + normalizedId.substring(3);
        }

        return "怪物 " + normalizedId;
    }

    /**
     * 将配置中的ID转换为CustomZombie可识别的格式
     * 直接为CustomZombie.spawnCustomZombieDirect方法准备参数
     *
     * @param monsterId 原始怪物ID (可能带有前缀如zombie:id1)
     * @param monsterType 怪物类型 (id/idc/idn)
     * @return 标准化后的ID (id1/idc1/idn1格式)
     */
    public String getStandardId(String monsterId, String monsterType) {
        // 处理null和空参数
        if (monsterId == null || monsterId.isEmpty()) {
            logger.warning("怪物ID为空，返回默认ID");
            return "id1"; // 默认ID
        }

        if (monsterType == null || monsterType.isEmpty()) {
            logger.warning("怪物类型为空，使用默认类型'id'");
            monsterType = "id"; // 默认类型
        }

        // 去除可能存在的前缀
        String normalizedId = stripPrefixesFromId(monsterId);
        logger.fine("ID标准化: " + monsterId + " -> " + normalizedId);

        // 针对纯数字ID处理
        if (normalizedId.matches("\\d+")) {
            switch (monsterType.toLowerCase()) {
                case "zombie":
                case "id":
                    normalizedId = "id" + normalizedId;
                    logger.fine("纯数字ID转换为僵尸ID: " + normalizedId);
                    break;
                case "entity":
                case "custom":
                case "idc":
                    normalizedId = "idc" + normalizedId;
                    logger.fine("纯数字ID转换为自定义实体ID: " + normalizedId);
                    break;
                case "npc":
                case "idn":
                    normalizedId = "idn" + normalizedId;
                    logger.fine("纯数字ID转换为NPC ID: " + normalizedId);
                    break;
                case "idz":
                case "custom_idz":
                    // IDZ怪物ID通常不是纯数字，但如果是，则添加idz前缀
                    normalizedId = "idz" + normalizedId;
                    logger.fine("纯数字ID转换为IDZ怪物ID: " + normalizedId);
                    break;
                default:
                    normalizedId = "id" + normalizedId; // 对于未知类型，默认使用僵尸ID
                    logger.warning("未知的怪物类型 '" + monsterType + "'，默认使用僵尸ID: " + normalizedId);
                    break;
            }
        }

        // 确保ID符合预期格式
        if (!normalizedId.startsWith("id") && !normalizedId.startsWith("idc") && !normalizedId.startsWith("idn")) {
            // 使用类型前缀
            switch (monsterType.toLowerCase()) {
                case "zombie":
                case "id":
                    normalizedId = "id" + normalizedId;
                    break;
                case "entity":
                case "custom":
                case "idc":
                    normalizedId = "idc" + normalizedId;
                    break;
                case "npc":
                case "idn":
                    normalizedId = "idn" + normalizedId;
                    break;
                default:
                    normalizedId = "id" + normalizedId; // 对于未知类型，默认使用僵尸ID
                    logger.warning("未知的怪物类型 '" + monsterType + "'，默认使用僵尸ID前缀");
                    break;
            }
        }

        logger.fine("最终标准化ID: " + normalizedId);
        return normalizedId;
    }

    /**
     * 为保持兼容性，别名为getApiId
     *
     * @deprecated 使用getStandardId代替
     */
    @Deprecated
    public String getApiId(String monsterId, String monsterType) {
        logger.warning("使用已废弃的getApiId方法，请改用getStandardId");
        return getStandardId(monsterId, monsterType);
    }

    /**
     * 从ID中去除所有前缀
     *
     * @param id 原始ID (如zombie:id1)
     * @return 去除前缀后的ID (如id1)
     */
    public String stripPrefixesFromId(String id) {
        if (id == null || id.isEmpty()) {
            logger.warning("尝试处理空ID");
            return "1"; // 返回默认值，后续会被转换为id1
        }

        // 处理冒号分隔的前缀
        if (id.contains(":")) {
            String[] parts = id.split(":", 2);
            logger.fine("拆分ID: " + id + " -> [" + parts[0] + ", " + parts[1] + "]");
            id = parts[1].trim();
        }

        return id;
    }

    /**
     * 获取怪物的显示名称，用于实体头顶显示
     *
     * @param monsterId 怪物ID
     * @param monsterType 怪物类型
     * @return 格式化的显示名称
     */
    public String getDisplayName(String monsterId, String monsterType) {
        String standardId = getStandardId(monsterId, monsterType);
        return ChatColor.RED + standardId;
    }

    /**
     * 从类型和ID中获取完整的怪物标识符
     *
     * @param monsterType 怪物类型
     * @param monsterId 怪物ID
     * @return 完整标识符 (如zombie:id1)
     */
    public String getFullIdentifier(String monsterType, String monsterId) {
        return monsterType + ":" + stripPrefixesFromId(monsterId);
    }

    /**
     * 从完整标识符中获取标准ID
     *
     * @param fullIdentifier 完整标识符 (如zombie:id1)
     * @return 标准ID (如id1)
     */
    public String getStandardIdFromFullIdentifier(String fullIdentifier) {
        if (!fullIdentifier.contains(":")) {
            return fullIdentifier;
        }

        String[] parts = fullIdentifier.split(":", 2);
        String type = parts[0];
        String id = parts[1];

        return getStandardId(id, type);
    }

    /**
     * 为保持兼容性，别名为getApiIdFromFullIdentifier
     *
     * @deprecated 使用getStandardIdFromFullIdentifier代替
     */
    @Deprecated
    public String getApiIdFromFullIdentifier(String fullIdentifier) {
        logger.warning("使用已废弃的getApiIdFromFullIdentifier方法，请改用getStandardIdFromFullIdentifier");
        return getStandardIdFromFullIdentifier(fullIdentifier);
    }

    /**
     * 判断ID是否为特定类型
     *
     * @param id 怪物ID
     * @param type 类型前缀 (id/idc/idn)
     * @return 是否匹配
     */
    public boolean isIdOfType(String id, String type) {
        String normalizedId = stripPrefixesFromId(id);
        return normalizedId.startsWith(type);
    }
}
