package org.Ver_zhzh.deathZombieV4.commands;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.util.StringUtil;

/**
 * 提供DeathZombieV4插件命令的Tab补全功能
 */
public class CommandTabCompleter implements TabCompleter {

    private final DeathZombieV4 plugin;
    private final List<String> COMMANDS = Arrays.asList(
            "create", "setspawn", "setmaxplayers", "edit", "setround",
            "setdoor", "web", "help",
            "setzombiespawn", "buy", "power", "enable", "join", "jump", "stop", "test",
            "setminplayer", "setwindows", "kit", "kitgui", "reload", "game", "remove", "setcount", "check",
            "setwaittime", "leave", "buff", "lobby", "lucky", "displaySettings", "cleangame",
            "cleanholochest", "testmoney", "rejoin", "rej", "top", "topHolo"
    );

    private final List<String> ZOMBIE_TYPES = Arrays.asList("id", "idn", "idc");
    private final List<String> DOOR_STATES = Arrays.asList("lock", "unlock");
    private final List<String> ITEM_TYPES = Arrays.asList("ar", "wp", "it", "sp");

    // 怪物中文名称到ID的映射
    private final Map<String, String> monsterNameToId = new HashMap<>();
    private final Map<String, String> monsterIdToName = new HashMap<>();

    public CommandTabCompleter(DeathZombieV4 plugin) {
        this.plugin = plugin;
        initializeMonsterMappings();
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数是子命令
            StringUtil.copyPartialMatches(args[0], COMMANDS, completions);
        } else if (args.length >= 2) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "create":
                    // 创建游戏不需要补全
                    break;
                case "setspawn":
                case "setmaxplayers":
                case "edit":
                case "setround":
                case "enable":
                case "jump":
                case "setminplayer":
                case "setwindows":
                case "kit":
                case "kitgui":
                case "setwaittime":
                case "buff":
                case "power":
                case "stop":
                case "test":
                case "setcount":
                case "check":
                case "cleanholochest":
                case "testmoney":
                case "rejoin":
                case "rej":
                case "top":
                case "topHolo":
                    // 这些命令的第二个参数是游戏名称
                    if (args.length == 2) {
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    }

                    // 处理setwaittime命令的特殊补全
                    if (subCommand.equalsIgnoreCase("setwaittime")) {
                        if (args.length == 3) {
                            // 提供一些常用的等待时间选项
                            List<String> waitTimeOptions = Arrays.asList("10", "15", "20", "30", "60");
                            StringUtil.copyPartialMatches(args[2], waitTimeOptions, completions);
                        }
                    } // 处理buff命令的特殊补全
                    else if (subCommand.equalsIgnoreCase("buff")) {
                        if (args.length == 3) {
                            // 提供操作类型选项
                            List<String> operationOptions = Arrays.asList("add", "remove");
                            StringUtil.copyPartialMatches(args[2], operationOptions, completions);
                        } else if (args.length == 4) {
                            // 提供buff类型选项
                            List<String> buffOptions = Arrays.asList("speed1", "speed2", "KeepFood", "health1", "health2", "health3", "health+", "health++", "health+++", "jump1");
                            StringUtil.copyPartialMatches(args[3], buffOptions, completions);
                        }
                    } // 处理kit命令的特殊补全
                    else if (subCommand.equals("kit")) {
                        if (args.length == 3) {
                            // 补全槽位位置
                            List<String> slotOptions = new ArrayList<>();

                            // 添加主要槽位
                            slotOptions.add("1"); // 铁剑槽位
                            slotOptions.add("2"); // 枪支槽位
                            slotOptions.add("7"); // 物品槽位1
                            slotOptions.add("8"); // 物品槽位2
                            slotOptions.add("9"); // 物品槽位3

                            // 添加其他常用槽位
                            for (int i = 3; i <= 6; i++) {
                                slotOptions.add(String.valueOf(i));
                            }

                            // 添加扩展槽位
                            for (int i = 10; i <= 36; i++) {
                                slotOptions.add(String.valueOf(i));
                            }

                            StringUtil.copyPartialMatches(args[2], slotOptions, completions);
                        } else if (args.length == 4) {
                            // 补全操作类型
                            List<String> actionOptions = new ArrayList<>();
                            actionOptions.add("set");
                            actionOptions.add("remove");
                            actionOptions.add("change");

                            StringUtil.copyPartialMatches(args[3], actionOptions, completions);
                        } else if (args.length == 5) {
                            // 根据操作类型提供不同的补全
                            String action = args[3].toLowerCase();

                            if (action.equals("set")) {
                                // 补全物品ID
                                List<String> itemIds = new ArrayList<>();

                                // 添加武器ID
                                for (int i = 1; i <= 18; i++) {
                                    if (i == 12) {
                                        continue; // 跳过id12，因为没有这个ID
                                    }
                                    itemIds.add("id" + i);
                                }

                                StringUtil.copyPartialMatches(args[4], itemIds, completions);
                            } else if (action.equals("change")) {
                                // 补全槽位类型
                                List<String> slotTypes = new ArrayList<>();
                                slotTypes.add("武器");
                                slotTypes.add("道具");
                                slotTypes.add("weapon");
                                slotTypes.add("item");

                                StringUtil.copyPartialMatches(args[4], slotTypes, completions);
                            }
                        }
                    }
                    break;
                case "join":
                    // join命令的第二个参数是可选的游戏名称
                    if (args.length == 2) {
                        List<String> enabledGames = getEnabledGames();
                        StringUtil.copyPartialMatches(args[1], enabledGames, completions);
                    }
                    break;
                case "web":
                    // web命令的第二个参数是端口号，不需要补全
                    break;
                case "setdoor":
                    // /dzs setdoor <游戏名> <门名称> <lock|unlock> [解锁价格]
                    if (args.length == 2) {
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    } else if (args.length == 4) {
                        StringUtil.copyPartialMatches(args[3], DOOR_STATES, completions);
                    }
                    break;
                case "setzombiespawn":
                    // /dzs setzombiespawn <出生点名称> <僵尸类型(id|idn|idc)> <游戏名>
                    if (args.length == 3) {
                        StringUtil.copyPartialMatches(args[2], ZOMBIE_TYPES, completions);
                    } else if (args.length == 4) {
                        StringUtil.copyPartialMatches(args[3], getGameNames(), completions);
                    }
                    break;


                case "buy":
                    // 支持新格式: /dzs buy <游戏名称> <物品类型> <物品名称> [解锁金额]
                    // 兼容旧格式: /dzs buy <游戏名称> <all|ar_id|it_id|wp_id|sp_id> [解锁金额]
                    if (args.length == 2) {
                        // 补全游戏名称
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    } else if (args.length == 3) {
                        // 补全物品类型（中文和英文）
                        List<String> typeCompletion = new ArrayList<>();

                        // 添加all选项（全局电源按钮）
                        typeCompletion.add("all");

                        // 添加中文物品类型
                        typeCompletion.add("护甲");
                        typeCompletion.add("武器");
                        typeCompletion.add("道具");
                        typeCompletion.add("特殊功能");

                        // 添加英文物品类型（兼容旧格式）
                        typeCompletion.add("ar");
                        typeCompletion.add("wp");
                        typeCompletion.add("it");
                        typeCompletion.add("sp");

                        // 兼容旧格式的完整类型+ID
                        for (String type : ITEM_TYPES) {
                            if (type.equals("sp")) {
                                List<String> spIds = getItemIds(type);
                                for (String id : spIds) {
                                    typeCompletion.add(type + "_" + id);
                                }
                            } else {
                                typeCompletion.add(type + "_id");
                            }
                        }

                        StringUtil.copyPartialMatches(args[2], typeCompletion, completions);

                        // 如果输入已经包含 "_"，则尝试补全完整的类型+ID（兼容旧格式）
                        if (args[2].contains("_")) {
                            String[] parts = args[2].split("_", 2);
                            if (ITEM_TYPES.contains(parts[0]) && !parts[0].equals("sp")) {
                                List<String> itemIds = getItemIds(parts[0]);
                                List<String> fullOptions = new ArrayList<>();
                                for (String id : itemIds) {
                                    fullOptions.add(parts[0] + "_" + id);
                                }
                                StringUtil.copyPartialMatches(args[2], fullOptions, completions);
                            }
                        }
                    } else if (args.length == 4) {
                        // 补全物品名称（仅当第三个参数是中文物品类型时）
                        String itemType = args[2];
                        String englishType = convertChineseTypeToEnglish(itemType);

                        if (englishType != null) {
                            // 获取该类型的所有物品名称
                            List<String> itemNames = getItemNamesByType(englishType);
                            StringUtil.copyPartialMatches(args[3], itemNames, completions);
                        }
                    }
                    break;
                case "lucky":
                    // /dzs lucky <游戏名称> <Change|Add|Set|open> <箱子名称> [参数A] [参数B]
                    if (args.length == 2) {
                        // 补全游戏名称
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    } else if (args.length == 3 && plugin.getGameManager().gameExists(args[1])) {
                        // 补全操作类型
                        List<String> actionTypes = Arrays.asList("Change", "Add", "Set", "open");
                        StringUtil.copyPartialMatches(args[2], actionTypes, completions);
                    } else if (args.length == 4 && plugin.getGameManager().gameExists(args[1])) {
                        String action = args[2].toLowerCase();
                        if (action.equals("add")) {
                            // Add命令需要武器ID
                            List<String> weaponIds = new ArrayList<>();
                            for (int i = 1; i <= 24; i++) {
                                weaponIds.add("id" + i);
                            }
                            StringUtil.copyPartialMatches(args[3], weaponIds, completions);
                        } else if (action.equals("set")) {
                            // Set命令可以提供自定义箱子名称
                            List<String> boxNames = new ArrayList<>();
                            boxNames.add("box1");
                            boxNames.add("box2");
                            boxNames.add("box3");
                            boxNames.add("chest1");
                            boxNames.add("chest2");
                            boxNames.add("chest3");
                            boxNames.add("lucky1");
                            boxNames.add("lucky2");
                            boxNames.add("lucky3");
                            StringUtil.copyPartialMatches(args[3], boxNames, completions);
                        } else if (action.equals("change") || action.equals("open")) {
                            // Change或Open命令需要补全现有的箱子名称
                            StringUtil.copyPartialMatches(args[3], getLuckyBoxNames(args[1]), completions);
                        }
                    } else if (args.length == 5 && plugin.getGameManager().gameExists(args[1])) {
                        String action = args[2].toLowerCase();
                        if (action.equals("set")) {
                            // Set命令的第5个参数可以是Local或Random
                            List<String> boxTypes = Arrays.asList("Local", "Random");
                            StringUtil.copyPartialMatches(args[4], boxTypes, completions);
                        } else if (action.equals("change")) {
                            // Change命令的第5个参数可以是Local、Random、openRound或openCost
                            List<String> changeParams = Arrays.asList("Local", "Random", "openRound", "openCost");
                            StringUtil.copyPartialMatches(args[4], changeParams, completions);
                        } else if (action.equals("open")) {
                            // Open命令的第5个参数可以是openRound或openCost
                            List<String> openParams = Arrays.asList("openRound", "openCost");
                            StringUtil.copyPartialMatches(args[4], openParams, completions);
                        } else if (action.equals("add")) {
                            // Add命令的第5个参数是概率值，提供一些常用概率
                            List<String> probabilities = Arrays.asList("10", "20", "30", "40", "50", "60", "70", "80", "90", "100");
                            StringUtil.copyPartialMatches(args[4], probabilities, completions);
                        }
                    } else if (args.length == 6 && plugin.getGameManager().gameExists(args[1])) {
                        String action = args[2].toLowerCase();
                        String paramType = args[4].toLowerCase();
                        if ((action.equals("change") || action.equals("open"))
                                && (paramType.equals("openround") || paramType.equals("opencost"))) {
                            // 提供一些常用的数值
                            List<String> values = new ArrayList<>();
                            if (paramType.equals("openround")) {
                                // 常用的回合数值
                                for (int i = 1; i <= 20; i++) {
                                    values.add(String.valueOf(i));
                                }
                            } else if (paramType.equals("opencost")) {
                                // 常用的金钱数值
                                values.add("500");
                                values.add("1000");
                                values.add("1500");
                                values.add("2000");
                                values.add("2500");
                                values.add("3000");
                                values.add("4000");
                                values.add("5000");
                            }
                            StringUtil.copyPartialMatches(args[5], values, completions);
                        }
                    }
                    break;
                case "displaysettings":
                    // /dzs displaySettings <kill|Shoot|hit> <on|off>
                    if (args.length == 2) {
                        // 补全显示类型
                        List<String> displayTypes = Arrays.asList("kill", "Shoot", "hit");
                        StringUtil.copyPartialMatches(args[1], displayTypes, completions);
                    } else if (args.length == 3) {
                        // 补全操作类型
                        List<String> actions = Arrays.asList("on", "off");
                        StringUtil.copyPartialMatches(args[2], actions, completions);
                    }
                    break;
                case "game":
                    // /dzs game <游戏名称> <spawn|with|setcount> <回合数/门名称> [其他参数]
                    // spawn: /dzs game <游戏名称> spawn <回合数> <怪物出生点> <生成物具体名称> <生成数量|random>
                    if (args.length == 2) {
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    } else if (args.length == 3 && plugin.getGameManager().gameExists(args[1])) {
                        // 补全子命令
                        List<String> subCommands = Arrays.asList("spawn", "with", "setcount");
                        StringUtil.copyPartialMatches(args[2], subCommands, completions);
                    } else if (args.length >= 4 && plugin.getGameManager().gameExists(args[1])) {
                        String gameSubCommand = args[2].toLowerCase();
                        if (gameSubCommand.equals("spawn")) {
                            // /dzs game <游戏名称> spawn <回合数> <怪物出生点名称> <生成物的具体名称> <生成数量|random>
                            if (args.length == 4) {
                                // 获取游戏的回合数并提供回合数字列表
                                FileConfiguration config = plugin.getGameManager().getGameConfig(args[1]);
                                if (config != null && config.contains("rounds")) {
                                    int rounds = config.getInt("rounds");
                                    List<String> roundNumbers = new ArrayList<>();
                                    for (int i = 1; i <= rounds; i++) {
                                        roundNumbers.add(String.valueOf(i));
                                    }
                                    StringUtil.copyPartialMatches(args[3], roundNumbers, completions);
                                }
                            } else if (args.length == 5) {
                                // 提供该游戏的生成点列表
                                StringUtil.copyPartialMatches(args[4], getZombieSpawnNames(args[1]), completions);
                            } else if (args.length == 6) {
                                // 提供生成物的中文名称
                                List<String> monsterNames = getAllMonsterNames();
                                StringUtil.copyPartialMatches(args[5], monsterNames, completions);
                            } else if (args.length == 7) {
                                // 提供数量选项
                                List<String> countOptions = new ArrayList<>();
                                countOptions.add("random");
                                for (int i = 1; i <= 10; i++) {
                                    countOptions.add(String.valueOf(i));
                                }
                                StringUtil.copyPartialMatches(args[6], countOptions, completions);
                            }
                        } else if (gameSubCommand.equals("with")) {
                            // /dzs game <游戏名称> with <门名称> <怪物出生点名称>
                            if (args.length == 4) {
                                StringUtil.copyPartialMatches(args[3], getDoorNames(args[1]), completions);
                            } else if (args.length == 5) {
                                StringUtil.copyPartialMatches(args[4], getZombieSpawnNames(args[1]), completions);
                            }
                        } else if (gameSubCommand.equals("setcount")) {
                            // /dzs game <游戏名称> setcount <回合数>
                            if (args.length == 4) {
                                // 提供一些常用的回合数
                                List<String> roundOptions = Arrays.asList("5", "10", "15", "20", "25", "30");
                                StringUtil.copyPartialMatches(args[3], roundOptions, completions);
                            }
                        }
                    }
                    break;
                case "clean":
                    // /dzs clean [游戏名称]
                    if (args.length == 2) {
                        // 补全游戏名称（可选参数）
                        StringUtil.copyPartialMatches(args[1], getGameNames(), completions);
                    }
                    break;
            }
        }

        Collections.sort(completions); // 排序补全结果
        return completions;
    }

    /**
     * 初始化怪物名称映射
     */
    private void initializeMonsterMappings() {
        // 僵尸类型 (id系列)
        addMonsterMapping("id1", "普通僵尸");
        addMonsterMapping("id2", "小僵尸");
        addMonsterMapping("id3", "路障僵尸");
        addMonsterMapping("id4", "钻斧僵尸");
        addMonsterMapping("id5", "剧毒僵尸");
        addMonsterMapping("id6", "双生僵尸");
        addMonsterMapping("id7", "骷髅僵尸");
        addMonsterMapping("id8", "武装僵尸");
        addMonsterMapping("id9", "肥胖僵尸");
        addMonsterMapping("id10", "法师僵尸");
        addMonsterMapping("id11", "自爆僵尸");
        addMonsterMapping("id12", "毒箭僵尸");
        addMonsterMapping("id13", "电击僵尸");
        addMonsterMapping("id14", "冰冻僵尸");
        addMonsterMapping("id15", "暗影僵尸");
        addMonsterMapping("id16", "毁灭僵尸");
        addMonsterMapping("id17", "雷霆僵尸");
        addMonsterMapping("id18", "变异科学家");
        addMonsterMapping("id19", "变异法师");
        addMonsterMapping("id20", "气球僵尸");
        addMonsterMapping("id21", "迷雾僵尸");
        addMonsterMapping("id22", "变异雷霆僵尸");
        addMonsterMapping("id23", "终极毁灭僵尸");
        addMonsterMapping("id24", "变异暗影僵尸");
        addMonsterMapping("id25", "变异博士");
        addMonsterMapping("id26", "尖叫僵尸");

        // 实体类型 (idc系列)
        addMonsterMapping("idc1", "变异僵尸01");
        addMonsterMapping("idc2", "变异僵尸02");
        addMonsterMapping("idc3", "变异烈焰人");
        addMonsterMapping("idc4", "变异爬行者");
        addMonsterMapping("idc5", "变异末影螨");
        addMonsterMapping("idc6", "变异蜘蛛");
        addMonsterMapping("idc7", "灾厄卫道士");
        addMonsterMapping("idc8", "灾厄唤魔者");
        addMonsterMapping("idc9", "灾厄劫掠兽");
        addMonsterMapping("idc10", "变异僵尸马");
        addMonsterMapping("idc11", "变异岩浆怪");
        addMonsterMapping("idc12", "变异尸壳");  // 修复：应该是变异尸壳，不是变异骷髅
        addMonsterMapping("idc13", "变异僵尸3");
        addMonsterMapping("idc14", "变异僵尸04");
        addMonsterMapping("idc15", "鲜血猪灵");  // 修复：idc15对应鲜血猪灵
        addMonsterMapping("idc16", "暗影潜影贝");  // 修复：idc16对应暗影潜影贝
        addMonsterMapping("idc17", "变异雪傀儡");  // 修复：去掉"2"
        addMonsterMapping("idc18", "变异铁傀儡");
        addMonsterMapping("idc19", "变异僵尸Max");
        addMonsterMapping("idc20", "灵魂坚守者");
        addMonsterMapping("idc21", "凋零领主");
        addMonsterMapping("idc22", "异变之王");

        // NPC类型 (idn系列)
        addMonsterMapping("idn1", "感染者史蒂夫");
        addMonsterMapping("idn2", "感染者艾利克斯");
        addMonsterMapping("idn3", "感染者农民");
        addMonsterMapping("idn4", "感染者居民");
        addMonsterMapping("idn5", "感染猪");
    }

    /**
     * 添加怪物映射
     */
    private void addMonsterMapping(String id, String name) {
        monsterNameToId.put(name, id);
        monsterIdToName.put(id, name);
    }

    /**
     * 根据中文名称获取怪物ID
     */
    public String getMonsterIdByName(String name) {
        return monsterNameToId.get(name);
    }

    /**
     * 根据ID获取怪物中文名称
     */
    public String getMonsterNameById(String id) {
        return monsterIdToName.get(id);
    }

    /**
     * 获取所有怪物的中文名称列表
     */
    private List<String> getAllMonsterNames() {
        return new ArrayList<>(monsterNameToId.keySet());
    }

    /**
     * 获取所有游戏名称
     */
    private List<String> getGameNames() {
        return plugin.getGameManager().getGameNames();
    }

    /**
     * 获取所有已启用的游戏名称
     */
    private List<String> getEnabledGames() {
        return plugin.getGameManager().getEnabledGames();
    }

    /**
     * 获取指定游戏中的所有门名称
     */
    private List<String> getDoorNames(String gameName) {
        FileConfiguration config = plugin.getGameManager().getGameConfig(gameName);
        List<String> doorNames = new ArrayList<>();

        if (config != null && config.contains("doors")) {
            ConfigurationSection doorsSection = config.getConfigurationSection("doors");
            if (doorsSection != null) {
                doorNames.addAll(doorsSection.getKeys(false));
            }
        }

        return doorNames;
    }

    /**
     * 获取指定游戏中的所有僵尸生成点名称
     */
    private List<String> getZombieSpawnNames(String gameName) {
        FileConfiguration config = plugin.getGameManager().getGameConfig(gameName);
        List<String> spawnNames = new ArrayList<>();

        if (config != null && config.contains("zombieSpawns")) {
            ConfigurationSection spawnsSection = config.getConfigurationSection("zombieSpawns");
            if (spawnsSection != null) {
                spawnNames.addAll(spawnsSection.getKeys(false));
            }
        }

        return spawnNames;
    }

    /**
     * 获取指定类型的物品ID列表
     */
    private List<String> getItemIds(String type) {
        List<String> ids = new ArrayList<>();
        // 添加通用的ID
        for (int i = 1; i <= 12; i++) {
            ids.add("id" + i);
        }
        return ids;
    }

    /**
     * 转换中文物品类型到英文缩写
     *
     * @param chineseType 中文物品类型
     * @return 英文缩写，如果无效则返回null
     */
    private String convertChineseTypeToEnglish(String chineseType) {
        if (chineseType == null) return null;

        switch (chineseType) {
            case "护甲":
                return "ar";
            case "武器":
                return "wp";
            case "道具":
                return "it";
            case "特殊功能":
                return "sp";
            default:
                // 检查是否已经是英文缩写
                if (chineseType.equals("ar") || chineseType.equals("wp") ||
                    chineseType.equals("it") || chineseType.equals("sp")) {
                    return chineseType;
                }
                return null;
        }
    }

    /**
     * 获取指定类型的所有物品名称列表
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @return 物品名称列表
     */
    private List<String> getItemNamesByType(String type) {
        try {
            // 获取ShootPluginHelper实例
            org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
            if (shootHelper != null) {
                return shootHelper.getItemNamesByType(type);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取物品名称列表时出错: " + e.getMessage());
        }

        // 如果获取失败，返回空列表
        return new ArrayList<>();
    }

    /**
     * 获取指定游戏中的所有幸运箱名称
     */
    private List<String> getLuckyBoxNames(String gameName) {
        FileConfiguration config = plugin.getGameManager().getGameConfig(gameName);
        List<String> boxNames = new ArrayList<>();

        if (config != null && config.contains("luckyBoxes")) {
            ConfigurationSection boxesSection = config.getConfigurationSection("luckyBoxes");
            if (boxesSection != null) {
                boxNames.addAll(boxesSection.getKeys(false));
            }
        }

        return boxNames;
    }
}
