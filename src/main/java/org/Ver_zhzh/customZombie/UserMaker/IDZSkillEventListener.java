package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;

import java.util.logging.Logger;

/**
 * IDZ技能事件监听器
 * 处理命中、受伤、死亡等事件触发的技能
 */
public class IDZSkillEventListener implements Listener {

    private final Plugin plugin;
    private final Logger logger;
    private final IDZSkillExecutorExtensions extensions;

    public IDZSkillEventListener(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.extensions = new IDZSkillExecutorExtensions(plugin, logger);
    }

    /**
     * 处理实体攻击事件（命中触发技能）
     */
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof LivingEntity)) return;
        if (!(event.getEntity() instanceof Player)) return;

        LivingEntity attacker = (LivingEntity) event.getDamager();
        Player victim = (Player) event.getEntity();

        // 检查是否是IDZ怪物
        if (!attacker.hasMetadata("idz_monster_id")) return;

        // 处理所有命中触发技能
        handleOnHitSkills(attacker, victim, event);

        // 处理攻击回血技能
        handleLifestealSkill(attacker, victim, event);

        // 额外：即使未配置命中技能，只要存在隐身攻击的加成也生效
        try {
            if (attacker.hasMetadata("idz_stealth_bonus")) {
                double bonus = attacker.getMetadata("idz_stealth_bonus").get(0).asDouble();
                event.setDamage(event.getDamage() + Math.max(0.0, bonus));
                attacker.removeMetadata("idz_stealth_bonus", plugin);
            }
        } catch (Exception ignored) {}
    }

    /**
     * 处理投射物造成的伤害（箭矢/雪球等）
     * 用于给毒箭施加具体的药水参数，以及对雪球等自定义伤害生效
     */
    @EventHandler
    public void onProjectileDamage(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof Player)) return;
        Entity damager = event.getDamager();

        // 毒箭/冰冻箭命中：根据箭矢上的元数据施加药水或冰冻效果
        if (damager instanceof org.bukkit.entity.Arrow) {
            org.bukkit.entity.Arrow arrow = (org.bukkit.entity.Arrow) damager;
            if (arrow.hasMetadata("idz_projectile_skill")) {
                String skill = arrow.getMetadata("idz_projectile_skill").get(0).asString();
                Player victim = (Player) event.getEntity();
                if ("poison_arrow".equalsIgnoreCase(skill)) {
                    int level = 1;
                    int duration = 100;
                    if (arrow.hasMetadata("idz_poison_level")) {
                        try { level = arrow.getMetadata("idz_poison_level").get(0).asInt(); } catch (Exception ignored) {}
                    }
                    if (arrow.hasMetadata("idz_poison_duration")) {
                        try { duration = arrow.getMetadata("idz_poison_duration").get(0).asInt(); } catch (Exception ignored) {}
                    }
                    victim.addPotionEffect(new PotionEffect(PotionEffectType.POISON, Math.max(1, duration), Math.max(0, level)));
                    logger.info("毒箭命中，已施加剧毒效果: duration=" + duration + ", level=" + level);
                } else if ("ice_arrow".equalsIgnoreCase(skill)) {
                    int freezeDuration = 100;
                    int slowLevel = 2;
                    int miningLevel = 1;
                    if (arrow.hasMetadata("idz_freeze_duration")) {
                        try { freezeDuration = arrow.getMetadata("idz_freeze_duration").get(0).asInt(); } catch (Exception ignored) {}
                    }
                    if (arrow.hasMetadata("idz_freeze_level")) {
                        try { slowLevel = arrow.getMetadata("idz_freeze_level").get(0).asInt(); } catch (Exception ignored) {}
                    }
                    if (arrow.hasMetadata("idz_mining_level")) {
                        try { miningLevel = arrow.getMetadata("idz_mining_level").get(0).asInt(); } catch (Exception ignored) {}
                    }
                    victim.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Math.max(1, freezeDuration), Math.max(0, slowLevel)));
                    victim.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, Math.max(1, freezeDuration), Math.max(0, miningLevel)));
                    logger.info("冰冻箭命中，已施加冻结效果: duration=" + freezeDuration + ", slow=" + slowLevel + ", mining=" + miningLevel);
                }
            }
        }

        // 雪球命中：套用自定义伤害
        if (damager instanceof org.bukkit.entity.Snowball) {
            org.bukkit.entity.Snowball snowball = (org.bukkit.entity.Snowball) damager;
            if (snowball.hasMetadata("idz_projectile_damage")) {
                double dmg = snowball.getMetadata("idz_projectile_damage").get(0).asDouble();
                event.setDamage(dmg);
            }
        }
        // 小火球/火球命中：套用自定义伤害
        if (damager instanceof org.bukkit.entity.SmallFireball || damager instanceof org.bukkit.entity.Fireball) {
            if (damager.hasMetadata("idz_projectile_damage")) {
                double dmg = damager.getMetadata("idz_projectile_damage").get(0).asDouble();
                event.setDamage(dmg);
            }
        }

        // IDZ潜影贝子弹命中：套用自定义伤害和漂浮效果
        if (damager instanceof org.bukkit.entity.ShulkerBullet) {
            org.bukkit.entity.ShulkerBullet bullet = (org.bukkit.entity.ShulkerBullet) damager;
            if (bullet.hasMetadata("idz_shulker_damage")) {
                try {
                    // 读取子弹参数
                    double damage = bullet.getMetadata("idz_shulker_damage").get(0).asDouble();
                    int levitationDuration = bullet.hasMetadata("idz_levitation_duration") ?
                        bullet.getMetadata("idz_levitation_duration").get(0).asInt() : 100;

                    // 设置伤害
                    event.setDamage(damage);

                    // 施加漂浮效果
                    Player victim = (Player) event.getEntity();
                    victim.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.LEVITATION, levitationDuration, 0));

                    // 显示命中效果
                    Location hitLoc = victim.getLocation().add(0, 1, 0);
                    victim.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, hitLoc, 15, 0.5, 1.0, 0.5, 0.1);
                    victim.getWorld().playSound(hitLoc, org.bukkit.Sound.ENTITY_SHULKER_HURT, 1.0f, 1.2f);

                    logger.info(String.format("IDZ潜影贝子弹命中 %s，伤害: %.2f，漂浮时长: %d",
                               victim.getName(), damage, levitationDuration));
                } catch (Exception e) {
                    logger.warning("处理潜影贝子弹命中时出错: " + e.getMessage());
                }
            }
        }

        // IDZ雪球弹幕命中：处理伤害、击退和毒性效果
        if (damager instanceof org.bukkit.entity.Snowball) {
            org.bukkit.entity.Snowball snowball = (org.bukkit.entity.Snowball) damager;
            if (snowball.hasMetadata("idz_snowball_barrage")) {
                try {
                    Player victim = (Player) event.getEntity();

                    // 读取雪球参数
                    double damage = snowball.hasMetadata("idz_snowball_damage") ?
                        snowball.getMetadata("idz_snowball_damage").get(0).asDouble() : 3.0;
                    double knockbackStrength = snowball.hasMetadata("idz_snowball_knockback") ?
                        snowball.getMetadata("idz_snowball_knockback").get(0).asDouble() : 0.5;

                    // 设置伤害
                    event.setDamage(damage);

                    // 应用击退效果
                    if (knockbackStrength > 0) {
                        org.bukkit.util.Vector knockback = victim.getLocation().subtract(snowball.getLocation()).toVector().normalize();
                        knockback.multiply(knockbackStrength);
                        knockback.setY(Math.max(0.1, knockback.getY())); // 确保有向上的分量
                        victim.setVelocity(knockback);
                    }

                    // 检查是否是毒性雪球
                    if (snowball.hasMetadata("idz_poison_snowball")) {
                        // 施加中毒效果
                        victim.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.POISON, 100, 1)); // 5秒中毒

                        // 显示毒性效果
                        Location hitLoc = victim.getLocation().add(0, 1, 0);
                        victim.getWorld().spawnParticle(org.bukkit.Particle.ANGRY_VILLAGER, hitLoc, 10, 0.5, 1.0, 0.5, 0.1);

                        logger.info(String.format("IDZ毒性雪球弹幕命中 %s，伤害: %.2f，击退: %.2f，施加中毒效果",
                                   victim.getName(), damage, knockbackStrength));
                    } else {
                        logger.info(String.format("IDZ雪球弹幕命中 %s，伤害: %.2f，击退: %.2f",
                                   victim.getName(), damage, knockbackStrength));
                    }

                    // 显示雪球命中效果
                    Location hitLoc = victim.getLocation().add(0, 1, 0);
                    victim.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, hitLoc, 8, 0.3, 0.5, 0.3, 0.1);
                    victim.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR, hitLoc, 5, 0.2, 0.2, 0.2, 0.1);

                } catch (Exception e) {
                    logger.warning("处理雪球弹幕命中时出错: " + e.getMessage());
                }
            }
        }

    }

    /**
     * 处理实体死亡事件（死亡触发技能）
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        // 检查是否是IDZ怪物
        if (!entity.hasMetadata("idz_monster_id")) return;

        // 处理死亡爆炸
        if (entity.hasMetadata("idz_death_explosion")) {
            MetadataValue powerMeta = entity.getMetadata("idz_death_explosion").get(0);
            double power = powerMeta.asDouble();
            entity.getWorld().createExplosion(entity.getLocation(), (float)power, false, false);
            logger.info("死亡爆炸触发: 威力=" + power);
        }

        // 处理死亡粒子与死亡雾气
        if (entity.hasMetadata("idz_skills")) {
            for (MetadataValue meta : entity.getMetadata("idz_skills")) {
                if (!(meta.value() instanceof java.util.List)) continue;
                @SuppressWarnings("unchecked")
                java.util.List<SkillConfig> skills = (java.util.List<SkillConfig>) meta.value();
                for (SkillConfig cfg : skills) {
                    String raw = cfg.getSkillName();
                    String name = normalizeSkillName(raw).toLowerCase();
                    if (name.contains("death_particles") || name.contains("death_fog")) {
                        int count = extensions.toInt(cfg.getParameter("particle_count", cfg.getParameter("fog_particle_count", 80)));
                        double spread = extensions.toDouble(cfg.getParameter("particle_range", cfg.getParameter("fog_range", 5.0)));
                        double height = extensions.toDouble(cfg.getParameter("particle_height", cfg.getParameter("fog_height", cfg.getParameter("vertical_range", 3.0))));
                        int duration = extensions.toInt(cfg.getParameter("particle_duration", cfg.getParameter("fog_duration", 100)));
                        org.bukkit.Particle type = name.contains("fog") ? org.bukkit.Particle.CLOUD : org.bukkit.Particle.FIREWORK;
                        Location loc = entity.getLocation();
                        int ticks = Math.max(1, duration);
                        int taskId = org.bukkit.Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                            entity.getWorld().spawnParticle(type, loc, count, spread, height, spread, 0.02);
                        }, 1L, 5L).getTaskId();
                        // 在duration后停止
                        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> org.bukkit.Bukkit.getScheduler().cancelTask(taskId), ticks);
                        logger.info("死亡粒子/雾气触发: " + raw + ", count=" + count + ", spread=" + spread + ", duration=" + duration);
                    }
                }
            }
        }

        // 处理死亡召唤技能
        handleDeathSkills(entity);

        // 清理技能任务
        cleanupSkillTasks(entity);
    }

    /**
     * 处理实体受伤事件（受伤触发技能）
     */
    @EventHandler
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) return;
        LivingEntity entity = (LivingEntity) event.getEntity();

        // 检查是否是IDZ怪物
        if (!entity.hasMetadata("idz_monster_id")) return;

        // 处理受伤触发技能
        handleDamageTriggeredSkills(entity, event.getFinalDamage());
    }

    private void handleOnHitSkills(LivingEntity attacker, Player victim, EntityDamageByEntityEvent event) {
        // 检查所有命中触发技能
        for (MetadataValue meta : attacker.getMetadata("idz_skills")) {
            if (meta.value() instanceof java.util.List) {
                @SuppressWarnings("unchecked")
                java.util.List<SkillConfig> skills = (java.util.List<SkillConfig>) meta.value();
                for (SkillConfig config : skills) {
                    String skillName = config.getSkillName();
                    if (isOnHitSkill(skillName)) {
                        applyOnHitEffect(attacker, victim, skillName, config, event);
                    }
                }
            }
        }
    }

    private boolean isOnHitSkill(String skillName) {
        String normalized = normalizeSkillName(skillName);
        return normalized.contains("poison_attack") || normalized.contains("freeze_attack") ||
               normalized.contains("collision_damage") || normalized.contains("attack_heal") ||
               normalized.contains("poison_spit") || normalized.contains("breath_attack") ||
               normalized.contains("fang_attack") || normalized.contains("critical_aura") ||
               normalized.contains("inventory_chaos");
    }

    private String normalizeSkillName(String raw) {
        if (raw == null) return "";
        String lower = raw.toLowerCase();

        // 兼容 idcX_ 前缀
        if (lower.startsWith("idc")) {
            int idx = raw.indexOf('_');
            if (idx > 0 && idx + 1 < raw.length()) {
                raw = raw.substring(idx + 1);
                lower = raw.toLowerCase();
            }
        }

        // 兼容 idX_ 前缀
        if (lower.startsWith("id") && raw.length() > 2 && Character.isDigit(raw.charAt(2))) {
            int idx = raw.indexOf('_');
            if (idx > 0 && idx + 1 < raw.length()) {
                raw = raw.substring(idx + 1);
                lower = raw.toLowerCase();
            }
        }

        // 兼容常用中文技能名
        if (raw.contains("暴击光环") || raw.contains("暴擊光環") || raw.contains("暴击")) return "critical_aura";
        if (raw.contains("撞击伤害") || raw.contains("撞擊傷害") || (raw.contains("撞击") && raw.contains("伤害"))) return "collision_damage";
        if (raw.contains("唤魔者光环") || raw.contains("喚魔者光環") || (raw.contains("唤魔者") && raw.contains("光环"))) return "evoker_damage_aura";

        // IDZ技能中文名映射
        if (raw.contains("召唤强力僵尸") || raw.contains("召喚強力僵屍") || (raw.contains("召唤") && raw.contains("强力") && raw.contains("僵尸"))) return "summon_enhanced_zombie";
        if (raw.contains("物品栏混乱") || raw.contains("物品欄混亂") || (raw.contains("物品栏") && raw.contains("混乱"))) return "inventory_chaos";
        if (raw.contains("三连发箭") || raw.contains("三連發箭") || (raw.contains("三连发") && raw.contains("箭"))) return "triple_arrow";
        if (raw.contains("粒子机枪") || raw.contains("粒子機槍") || (raw.contains("粒子") && raw.contains("机枪"))) return "particle_gun";
        if (raw.contains("烟雾粒子") || raw.contains("煙霧粒子") || (raw.contains("烟雾") && raw.contains("粒子"))) return "smoke_particles";
        if (raw.contains("冰霜光环") || raw.contains("冰霜光環") || (raw.contains("冰霜") && raw.contains("光环"))) return "frost_aura";
        if (raw.contains("冲刺攻击") || raw.contains("衝刺攻擊") || (raw.contains("冲刺") && raw.contains("攻击"))) return "dash_attack";
        if (raw.contains("红色粒子球") || raw.contains("紅色粒子球") || (raw.contains("红色") && raw.contains("粒子球"))) return "red_particle_ball";
        if (raw.contains("攻击回血") || raw.contains("攻擊回血") || (raw.contains("攻击") && raw.contains("回血")) || raw.contains("生命偷取") || raw.contains("吸血")) return "lifesteal";
        if (raw.contains("潜影贝子弹") || raw.contains("潛影貝子彈") || (raw.contains("潜影贝") && raw.contains("子弹"))) return "shulker_bullet";
        if (raw.contains("潜影贝混乱") || raw.contains("潛影貝混亂") || (raw.contains("潜影贝") && raw.contains("混乱"))) return "shulker_inventory_chaos";
        if (raw.contains("潜影贝隐身") || raw.contains("潛影貝隱身") || (raw.contains("潜影贝") && raw.contains("隐身"))) return "shulker_invisibility";
        if (raw.contains("雪球弹幕") || raw.contains("雪球彈幕") || (raw.contains("雪球") && raw.contains("弹幕"))) return "snowball_barrage";
        if (raw.contains("雪人冰冻光环") || raw.contains("雪人冰凍光環") || (raw.contains("雪人") && raw.contains("冰冻") && raw.contains("光环"))) return "snow_freeze_aura";
        if (raw.contains("草方块攻击") || raw.contains("草方塊攻擊") || raw.contains("草方块伸展攻击") || raw.contains("草方塊伸展攻擊") || (raw.contains("草方块") && raw.contains("攻击"))) return "grass_block_attack";
        if (raw.contains("声波攻击") || raw.contains("聲波攻擊") || raw.contains("声波弹攻击") || raw.contains("聲波彈攻擊") || (raw.contains("声波") && raw.contains("攻击"))) return "sonic_attack";
        if (raw.contains("主动追踪") || raw.contains("主動追蹤") || raw.contains("主动追踪玩家") || raw.contains("主動追蹤玩家") || (raw.contains("主动") && raw.contains("追踪"))) return "player_tracking";
        if (raw.contains("天气控制") || raw.contains("天氣控制") || (raw.contains("天气") && raw.contains("控制"))) return "weather_control";
        if (raw.contains("粒子攻击") || raw.contains("粒子攻擊") || (raw.contains("粒子") && raw.contains("攻击"))) return "particle_attack";
        if (raw.contains("三叉戟攻击") || raw.contains("三叉戟攻擊") || (raw.contains("三叉戟") && raw.contains("攻击"))) return "trident_attack";
        if (raw.contains("闪电攻击") || raw.contains("閃電攻擊") || (raw.contains("闪电") && raw.contains("攻击"))) return "lightning_attack";
        if (raw.contains("隐身能力") || raw.contains("隱身能力") || (raw.contains("隐身") && raw.contains("能力"))) return "invisibility_skill";
        // 移除传送能力映射 - 避免与其他传送技能重复
        // if (raw.contains("传送能力") || raw.contains("傳送能力") || (raw.contains("传送") && raw.contains("能力"))) return "teleport_skill";

        // IDC20技能映射
        if (raw.contains("黑曜石攻击") || raw.contains("黑曜石方块攻击") || (raw.contains("黑曜石") && raw.contains("攻击"))) return "obsidian_attack";
        if (raw.contains("击飞黑曜石柱") || raw.contains("击飞") && raw.contains("黑曜石柱")) return "knockup_pillar";

        // 移除防猪人化的映射，这不是真正的技能
        // if (raw.contains("防猪人化") || raw.contains("防豬人化")) return "anti_zombification";

        return raw;
    }

    private void applyOnHitEffect(LivingEntity attacker, Player victim, String skillName, SkillConfig config, EntityDamageByEntityEvent event) {
        logger.info("触发命中技能: " + skillName);

        // 统一中文名归一化，防止中文技能无法命中
        String normalized = normalizeSkillName(skillName).toLowerCase();

        if (normalized.contains("attack_buff")) {
            // 攻击命中时给自身上力量buff（兼容旧字段名）
            int duration = extensions.toInt(config.getParameter("buff_duration", config.getParameter("duration", 200)));
            int amplifier = extensions.toInt(config.getParameter("buff_amplifier", config.getParameter("amplifier", 1)));
            PotionEffectType type = PotionEffectType.STRENGTH;
            attacker.addPotionEffect(new PotionEffect(type, Math.max(1, duration), Math.max(0, amplifier)));
            return;
        }

        if (normalized.contains("inventory_chaos")) {
            // IDC12式物品栏混乱：读取 chaos 参数，并在持续时间内每tick扰乱玩家1-9槽
            double chance = extensions.toDouble(config.getParameter("chaos_chance", 1.0));
            if (Math.random() <= chance) {
                int duration = Math.max(1, extensions.toInt(config.getParameter("chaos_duration", 100)));
                int affected = Math.max(1, extensions.toInt(config.getParameter("affected_slots", 5)));
                // 立即打乱一次
                shuffleHotbar(victim, affected);
                // 在持续时间内每5tick再打乱一次，模拟持续混乱
                new org.bukkit.scheduler.BukkitRunnable() {
                    int t = 0;
                    @Override public void run() {
                        if (t >= duration || victim.isDead() || !victim.isOnline()) { cancel(); return; }
                        if (t % 5 == 0) shuffleHotbar(victim, affected);
                        t++;
                    }
                }.runTaskTimer(plugin, 0L, 1L);
            }
            return;
        }

        if (normalized.contains("poison")) {
            // 兼容剧毒攻击的参数键：poison_duration/poison_level/poison_chance
            int duration = extensions.toInt(config.getParameter("poison_duration", config.getParameter("duration", 100)));
            int amplifier = Math.max(0, extensions.toInt(config.getParameter("poison_level", config.getParameter("amplifier", 1))));
            double chance = 1.0;
            try { chance = Double.parseDouble(String.valueOf(config.getParameter("poison_chance", 1.0))); } catch (Exception ignored) {}
            if (Math.random() <= chance) {
                victim.addPotionEffect(new PotionEffect(PotionEffectType.POISON, duration, amplifier));
            }

        } else if (normalized.contains("stealth")) {
            // 隐身攻击：若施法者携带一次性伤害加成，应用到本次命中并清除
            try {
                if (attacker.hasMetadata("idz_stealth_bonus")) {
                    double bonus = attacker.getMetadata("idz_stealth_bonus").get(0).asDouble();
                    event.setDamage(event.getDamage() + Math.max(0.0, bonus));
                    attacker.removeMetadata("idz_stealth_bonus", plugin);
                }
            } catch (Exception ignored) {}

        } else if (normalized.contains("freeze")) {
            // 读取两个效果的独立参数，兼容旧字段
            double chance = extensions.toDouble(config.getParameter("freeze_chance", 1.0));
            if (Math.random() <= chance) {
                int slowLevel = extensions.toInt(config.getParameter("slowness_level", 2));
                int slowDuration = extensions.toInt(config.getParameter("slowness_duration", 100));
                int miningLevel = extensions.toInt(config.getParameter("mining_level", 2));
                int miningDuration = extensions.toInt(config.getParameter("mining_duration", slowDuration));
                victim.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slowDuration, Math.max(0, slowLevel)));
                victim.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, miningDuration, Math.max(0, miningLevel)));
            }

        } else if (normalized.contains("heal")) {
            double healAmount = extensions.toDouble(config.getParameter("heal", 2.0));
            if (attacker.getHealth() + healAmount <= attacker.getMaxHealth()) {
                attacker.setHealth(attacker.getHealth() + healAmount);
            }

        } else if (normalized.contains("collision")) {
            // 撞击伤害：读取参数并提供冲撞+击退效果
            double extraDamage = extensions.toDouble(config.getParameter("collision_damage", config.getParameter("damage", 3.0)));
            double knockback = Math.max(0.0, extensions.toDouble(config.getParameter("collision_knockback", 0.8)));
            double dashSpeed = Math.max(0.1, extensions.toDouble(config.getParameter("collision_dash_speed", 1.2)));
            int dashTicks = Math.max(1, extensions.toInt(config.getParameter("collision_dash_ticks", 3)));
            double chance = Math.max(0.0, Math.min(1.0, extensions.toDouble(config.getParameter("collision_chance", 1.0))));
            if (Math.random() <= chance) {
                event.setDamage(event.getDamage() + extraDamage);
                // 让攻击者短距离朝向受害者冲刺（撞击感）
                org.bukkit.util.Vector toVictim = victim.getLocation().toVector().subtract(attacker.getLocation().toVector()).normalize();
                org.bukkit.util.Vector dash = new org.bukkit.util.Vector(toVictim.getX(), 0, toVictim.getZ()).multiply(dashSpeed);
                attacker.setVelocity(attacker.getVelocity().add(dash));
                attacker.setFallDistance(0.0f);
                // 少量tick内保持冲刺（避免被瞬时阻尼抵消）
                new org.bukkit.scheduler.BukkitRunnable() { int t=0; @Override public void run(){
                    if (t++ >= dashTicks || attacker.isDead()) { cancel(); return; }
                    attacker.setVelocity(dash);
                }}.runTaskTimer(plugin, 0L, 1L);
                // 对受害者施加击退
                org.bukkit.util.Vector kb = new org.bukkit.util.Vector(toVictim.getX(), 0.25, toVictim.getZ()).multiply(knockback);
                victim.setVelocity(victim.getVelocity().add(kb));
                victim.getWorld().spawnParticle(org.bukkit.Particle.CRIT, victim.getLocation().add(0, 1, 0), 12, 0.5, 0.5, 0.5, 0.05);
            }
        } else if (normalized.contains("critical")) {
            // 暴击光环：在命中时有概率造成倍数伤害
            double critChance = Math.max(0.0, Math.min(1.0, extensions.toDouble(config.getParameter("crit_chance", config.getParameter("critical_chance", 0.25)))));
            double critMultiplier = Math.max(1.0, extensions.toDouble(config.getParameter("crit_multiplier", config.getParameter("critical_multiplier", 1.5))));
            if (Math.random() <= critChance) {
                event.setDamage(event.getDamage() * critMultiplier);
                // 视觉提示：脚底暴击粒子
                attacker.getWorld().spawnParticle(org.bukkit.Particle.CRIT, attacker.getLocation().add(0, 0.2, 0), 16, 0.4, 0.1, 0.4, 0.02);
                attacker.getWorld().spawnParticle(org.bukkit.Particle.CRIT, victim.getLocation().add(0, 1.0, 0), 20, 0.3, 0.3, 0.3, 0.05);
            }
        }
    }

    private void handleDeathSkills(LivingEntity entity) {
        // 检查技能元数据并触发死亡召唤
        if (!entity.hasMetadata("idz_skills")) return;

        for (MetadataValue meta : entity.getMetadata("idz_skills")) {
            if (!(meta.value() instanceof java.util.List)) continue;
            @SuppressWarnings("unchecked")
            java.util.List<SkillConfig> skills = (java.util.List<SkillConfig>) meta.value();
            for (SkillConfig cfg : skills) {
                String rawName = cfg.getSkillName();
                String name = normalizeSkillName(rawName).toLowerCase();
                if (name.contains("death_summon")) {
                    // 读取参数
                    int count = extensions.toInt(cfg.getParameter("summon_count",
                            cfg.getParameter("spawn_count",
                            cfg.getParameter("zombie_count",
                            cfg.getParameter("count", 2)))));
                    double range = extensions.toDouble(cfg.getParameter("summon_range",
                            cfg.getParameter("spawn_range",
                            cfg.getParameter("range", 5.0))));

                    // 解析召唤对象：优先使用monster_id（支持 idz*/id*/idc*），其次使用summon_type回落到原版EntityType
                    String monsterId = null;
                    Object monsterIdObj = cfg.getParameter("monster_id");
                    if (monsterIdObj != null) {
                        monsterId = String.valueOf(monsterIdObj);
                    }
                    String summonTypeStr = null;
                    Object summonTypeObj = cfg.getParameter("summon_type");
                    if (summonTypeObj != null) {
                        summonTypeStr = String.valueOf(summonTypeObj);
                    }

                    logger.info(String.format("触发死亡召唤[%s]: count=%d, range=%.2f, monsterId=%s, summon_type=%s",
                            rawName, count, range, monsterId, summonTypeStr));

                    for (int i = 0; i < count; i++) {
                        Location spawnLoc = entity.getLocation().add(
                                (Math.random() - 0.5) * range * 2,
                                0,
                                (Math.random() - 0.5) * range * 2
                        );

                        boolean spawned = false;
                        try {
                            if (monsterId != null && !monsterId.isEmpty()) {
                                // 使用现有系统生成 IDZ/ID/IDC 实体
                                DeathZombieV4 dz = (DeathZombieV4) plugin;
                                ZombieHelper helper = dz.getZombieHelper();
                                if (monsterId.toLowerCase().startsWith("idz")) {
                                    // IDZ 系列
                                    org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzMgr = dz.getIDZMonsterManager();
                                    if (idzMgr != null) {
                                        idzMgr.spawnMonster(spawnLoc, monsterId);
                                        spawned = true;
                                    }
                                } else if (monsterId.toLowerCase().startsWith("idc")) {
                                    // IDC 系列
                                    if (helper != null) {
                                        spawned = helper.spawnOtherEntity(spawnLoc, monsterId);
                                    }
                                } else if (monsterId.toLowerCase().startsWith("id")) {
                                    // ID 系列
                                    if (helper != null) {
                                        // 直接使用自定义僵尸接口
                                        org.bukkit.entity.Zombie z = helper.spawnCustomZombie(spawnLoc, monsterId);
                                        spawned = (z != null);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.warning("死亡召唤生成自定义实体失败: " + e.getMessage());
                        }

                        if (!spawned) {
                            // 特殊：速度僵尸（ID6衍生）
                            if ("twin_spawn_zombie".equalsIgnoreCase(monsterId)) {
                                try {
                                    // 借助 CustomZombie 的生成风格：直接生成普通僵尸并打标记与外观（最小复用）
                                    org.bukkit.entity.Zombie newZombie = (org.bukkit.entity.Zombie) entity.getWorld().spawnEntity(spawnLoc, EntityType.ZOMBIE);
                                    newZombie.setMetadata("twin_spawn", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("not_round_zombie", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("customZombieType", new org.bukkit.metadata.FixedMetadataValue(plugin, "twin_spawn_zombie"));
                                    // 简单的黑色皮革头/靴外观可选：留给后续复用工具方法
                                } catch (Exception ignored) {}
                                spawned = true;
                            }
                        }
                        if (!spawned) {
                            // 回退：使用原版实体类型
                            EntityType type = extensions.getSummonEntityType(rawName);
                            entity.getWorld().spawnEntity(spawnLoc, type);
                        }
                    }
                }
            }
        }
    }

    private void handleDamageTriggeredSkills(LivingEntity entity, double damage) {
        // 处理受伤触发的召唤等技能（受伤召唤）
        if (!entity.hasMetadata("idz_skills")) return;
        for (MetadataValue meta : entity.getMetadata("idz_skills")) {
            if (!(meta.value() instanceof java.util.List)) continue;
            @SuppressWarnings("unchecked")
            java.util.List<SkillConfig> skills = (java.util.List<SkillConfig>) meta.value();
            for (SkillConfig cfg : skills) {
                String raw = cfg.getSkillName();
                String name = normalizeSkillName(raw).toLowerCase();
                if (!name.contains("damage_summon")) continue;

                // 读取阈值与参数
                double threshold = extensions.toDouble(cfg.getParameter("damage_threshold", 50.0));
                int count = extensions.toInt(cfg.getParameter("summon_count", 1));
                double range = extensions.toDouble(cfg.getParameter("summon_range", 5.0));
                String monsterId = cfg.getParameter("monster_id") != null ? String.valueOf(cfg.getParameter("monster_id")) : null;

                // 通过元数据累计本次受伤值
                double acc = 0.0;
                if (entity.hasMetadata("idz_damage_accum")) {
                    try { acc = entity.getMetadata("idz_damage_accum").get(0).asDouble(); } catch (Exception ignored) {}
                }
                acc += Math.max(0, damage);
                entity.setMetadata("idz_damage_accum", new org.bukkit.metadata.FixedMetadataValue(plugin, acc));

                if (acc < threshold) continue; // 累计不到阈值不触发
                // 触发后清零累计
                entity.setMetadata("idz_damage_accum", new org.bukkit.metadata.FixedMetadataValue(plugin, 0.0));

                logger.info(String.format("触发受伤召唤[%s]: threshold=%.1f, count=%d, range=%.1f, monsterId=%s, 本次累计=%.1f",
                        raw, threshold, count, range, monsterId, acc));

                for (int i = 0; i < count; i++) {
                    Location spawnLoc = entity.getLocation().add((Math.random() - 0.5) * range * 2, 0, (Math.random() - 0.5) * range * 2);
                    boolean spawned = false;
                    // 优先尝试自定义ID生成
                    try {
                        if (monsterId != null && !monsterId.isEmpty()) {
                            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                            org.Ver_zhzh.deathZombieV4.utils.ZombieHelper helper = dz.getZombieHelper();
                            if (monsterId.toLowerCase().startsWith("idz")) {
                                org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzMgr = dz.getIDZMonsterManager();
                                if (idzMgr != null) { idzMgr.spawnMonster(spawnLoc, monsterId); spawned = true; }
                            } else if (monsterId.toLowerCase().startsWith("idc")) {
                                if (helper != null) { spawned = helper.spawnOtherEntity(spawnLoc, monsterId); }
                            } else if (monsterId.toLowerCase().startsWith("id")) {
                                if (helper != null) { spawned = (helper.spawnCustomZombie(spawnLoc, monsterId) != null); }
                            } else if ("twin_spawn_zombie".equalsIgnoreCase(monsterId)) {
                                // 特殊快捷项：速度僵尸
                                try {
                                    org.bukkit.entity.Zombie newZombie = (org.bukkit.entity.Zombie) entity.getWorld().spawnEntity(spawnLoc, org.bukkit.entity.EntityType.ZOMBIE);
                                    newZombie.setMetadata("twin_spawn", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("not_round_zombie", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                    newZombie.setMetadata("customZombieType", new org.bukkit.metadata.FixedMetadataValue(plugin, "twin_spawn_zombie"));
                                    spawned = true;
                                } catch (Exception ignored) {}
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("受伤召唤生成自定义实体失败: " + e.getMessage());
                    }

                    if (!spawned) {
                        // 回退：原版僵尸
                        entity.getWorld().spawnEntity(spawnLoc, EntityType.ZOMBIE);
                    }
                }
            }
        }
    }

    private void cleanupSkillTasks(LivingEntity entity) {
        if (entity.hasMetadata("idz_skill_tasks")) {
            @SuppressWarnings("unchecked")
            java.util.List<Integer> taskIds = (java.util.List<Integer>) entity.getMetadata("idz_skill_tasks").get(0).value();
            for (Integer taskId : taskIds) {
                org.bukkit.Bukkit.getScheduler().cancelTask(taskId);
            }
            logger.info("清理技能任务: " + taskIds.size() + " 个");
        }
    }

    /**
     * 简化版：打乱玩家热键栏前 N 个槽位的工具方法
     */
    private void shuffleHotbar(Player player, int affectedSlots) {
        try {
            org.bukkit.inventory.PlayerInventory inv = player.getInventory();
            java.util.List<org.bukkit.inventory.ItemStack> items = new java.util.ArrayList<>();
            for (int i = 0; i < Math.min(9, Math.max(1, affectedSlots)); i++) {
                items.add(inv.getItem(i));
            }
            java.util.Collections.shuffle(items);
            for (int i = 0; i < items.size(); i++) {
                inv.setItem(i, items.get(i));
            }
            player.updateInventory();
        } catch (Throwable ignored) {}
    }

    /**
     * 处理攻击回血技能
     */
    private void handleLifestealSkill(LivingEntity attacker, Player victim, EntityDamageByEntityEvent event) {
        if (!attacker.hasMetadata("idz_skills")) return;

        try {
            java.util.List<MetadataValue> skillsMetadata = attacker.getMetadata("idz_skills");
            if (skillsMetadata.isEmpty()) return;

            String skillsStr = skillsMetadata.get(0).asString();
            if (skillsStr == null || skillsStr.trim().isEmpty()) return;

            String[] skills = skillsStr.split(",");
            for (String skill : skills) {
                String normalized = normalizeSkillName(skill.trim());
                if (normalized.equals("lifesteal") || normalized.equals("attack_heal")) {
                    performLifesteal(attacker, victim, event.getFinalDamage());
                    break;
                }
            }
        } catch (Exception e) {
            logger.warning("处理攻击回血技能时出错: " + e.getMessage());
        }
    }

    /**
     * 执行攻击回血效果
     */
    private void performLifesteal(LivingEntity attacker, Player victim, double damage) {
        try {
            // 读取回血参数（从实体的技能参数中）
            double healAmount = 2.0; // 默认回血量
            double healPercentage = 0.3; // 默认回血比例（30%伤害转化为回血）
            double healChance = 1.0; // 默认回血概率（100%）

            // 尝试从元数据中读取参数
            if (attacker.hasMetadata("idz_skill_params")) {
                try {
                    java.util.List<MetadataValue> paramsMetadata = attacker.getMetadata("idz_skill_params");
                    if (!paramsMetadata.isEmpty()) {
                        String paramsStr = paramsMetadata.get(0).asString();
                        // 简单的参数解析
                        if (paramsStr.contains("heal_amount=")) {
                            String[] parts = paramsStr.split("heal_amount=")[1].split("[,;]")[0].trim().split(" ");
                            healAmount = Double.parseDouble(parts[0]);
                        }
                        if (paramsStr.contains("heal_percentage=")) {
                            String[] parts = paramsStr.split("heal_percentage=")[1].split("[,;]")[0].trim().split(" ");
                            healPercentage = Double.parseDouble(parts[0]);
                        }
                        if (paramsStr.contains("heal_chance=")) {
                            String[] parts = paramsStr.split("heal_chance=")[1].split("[,;]")[0].trim().split(" ");
                            healChance = Double.parseDouble(parts[0]);
                        }
                    }
                } catch (Exception ignored) {}
            }

            // 检查回血概率
            if (Math.random() > healChance) return;

            // 计算回血量（固定值 + 伤害比例）
            double totalHeal = healAmount + (damage * healPercentage);

            // 执行回血
            double currentHealth = attacker.getHealth();
            double maxHealth = attacker.getMaxHealth();
            double newHealth = Math.min(maxHealth, currentHealth + totalHeal);

            if (newHealth > currentHealth) {
                attacker.setHealth(newHealth);

                // 显示回血效果
                Location healLoc = attacker.getLocation().add(0, 1, 0);
                attacker.getWorld().spawnParticle(org.bukkit.Particle.HEART, healLoc, 5, 0.5, 0.5, 0.5, 0.1);
                attacker.getWorld().spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER, healLoc, 3, 0.3, 0.3, 0.3, 0.0);

                // 播放回血音效
                attacker.getWorld().playSound(attacker.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 2.0f);

                logger.info(String.format("IDZ攻击回血: %s 回血 %.2f (伤害: %.2f, 当前血量: %.2f/%.2f)",
                           attacker.getType(), totalHeal, damage, newHealth, maxHealth));
            }
        } catch (Exception e) {
            logger.warning("执行攻击回血时出错: " + e.getMessage());
        }
    }
}
