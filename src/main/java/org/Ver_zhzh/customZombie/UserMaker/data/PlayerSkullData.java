package org.Ver_zhzh.customZombie.UserMaker.data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 玩家头颅数据模型
 * 用于存储自定义添加的玩家头颅信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PlayerSkullData {
    
    private String playerName;
    private String displayName;
    private String addedTime;
    
    /**
     * 默认构造函数（用于JSON反序列化）
     */
    public PlayerSkullData() {
    }
    
    /**
     * 构造函数
     * 
     * @param playerName 玩家名称
     */
    public PlayerSkullData(String playerName) {
        this.playerName = playerName;
        this.displayName = "§e" + playerName + "的头颅";
        this.addedTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    /**
     * 完整构造函数
     * 
     * @param playerName 玩家名称
     * @param displayName 显示名称
     * @param addedTime 添加时间
     */
    public PlayerSkullData(String playerName, String displayName, String addedTime) {
        this.playerName = playerName;
        this.displayName = displayName;
        this.addedTime = addedTime;
    }
    
    // Getter和Setter方法
    
    public String getPlayerName() {
        return playerName;
    }
    
    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getAddedTime() {
        return addedTime;
    }
    
    public void setAddedTime(String addedTime) {
        this.addedTime = addedTime;
    }
    
    /**
     * 检查是否为有效的玩家头颅数据
     */
    public boolean isValid() {
        return playerName != null && !playerName.trim().isEmpty();
    }
    
    /**
     * 获取格式化的添加时间
     */
    public String getFormattedAddedTime() {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(addedTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        } catch (Exception e) {
            return addedTime;
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PlayerSkullData that = (PlayerSkullData) obj;
        return playerName != null ? playerName.equalsIgnoreCase(that.playerName) : that.playerName == null;
    }
    
    @Override
    public int hashCode() {
        return playerName != null ? playerName.toLowerCase().hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return "PlayerSkullData{" +
                "playerName='" + playerName + '\'' +
                ", displayName='" + displayName + '\'' +
                ", addedTime='" + addedTime + '\'' +
                '}';
    }
}
