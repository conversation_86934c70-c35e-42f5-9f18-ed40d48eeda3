package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.EntityEquipment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.Ver_zhzh.customZombie.UserCustomEntity.AdvancedEntitySkillHandler;
import org.Ver_zhzh.customZombie.UserMaker.gui.ParticleEditorGUI;

import java.util.logging.Logger;

/**
 * IDZ怪物生成器
 * 负责根据IDZMonsterConfig配置生成实际的怪物实体
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZMonsterSpawner {
    
    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager monsterManager;
    
    // 集成其他系统
    // private AdvancedEntitySkillHandler skillHandler; // 旧集成暂不使用
    private IDZSkillExecutor skillExecutor;
    private ParticleEditorGUI particleHelper;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param monsterManager 怪物管理器
     */
    public IDZMonsterSpawner(Plugin plugin, IDZMonsterManager monsterManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.monsterManager = monsterManager;
        
        // 初始化集成系统
        initializeIntegrations();
        
        logger.info("IDZ怪物生成器初始化完成");
    }
    
    /**
     * 初始化与其他系统的集成
     */
    private void initializeIntegrations() {
        try {
            // 启用最小技能执行器
            this.skillExecutor = new IDZSkillExecutor(plugin);
            logger.info("技能系统: 最小技能执行器已启用");
        } catch (Exception e) {
            logger.warning("技能系统初始化失败: " + e.getMessage());
        }

        try {
            // 暂时禁用粒子助手集成，避免循环依赖
            // this.particleHelper = new ParticleEditorGUI(plugin, monsterManager);
            logger.info("粒子系统集成已禁用（待后续完善）");
        } catch (Exception e) {
            logger.warning("粒子系统集成失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成IDZ怪物
     * 
     * @param location 生成位置
     * @param monsterId 怪物ID
     * @return 生成的怪物实体，失败时返回null
     */
    public LivingEntity spawnMonster(Location location, String monsterId) {
        if (location == null || monsterId == null) {
            logger.warning("生成IDZ怪物失败: 位置或怪物ID为null");
            return null;
        }
        
        // 获取怪物配置
        IDZMonsterConfig config = monsterManager.getMonsterConfig(monsterId);
        if (config == null) {
            logger.warning("未找到IDZ怪物配置: " + monsterId);
            return null;
        }

        // 调试：打印配置中的技能信息
        logger.info("配置调试 - 怪物: " + monsterId + ", 技能数量: " + config.getSkills().size());
        for (SkillConfig skill : config.getSkills()) {
            logger.info("  技能: " + skill.getSkillName() + ", 参数: " + skill.getParameters());
        }
        
        try {
            logger.info("开始生成IDZ怪物: " + monsterId + " 在位置: " + formatLocation(location));
            
            // 1. 生成基础实体
            LivingEntity entity = spawnBaseEntity(location, config);
            if (entity == null) {
                logger.warning("基础实体生成失败: " + monsterId);
                return null;
            }
            
            // 2. 应用基础属性
            applyAttributes(entity, config);
            
            // 3. 应用装备
            applyEquipment(entity, config);
            
            // 4. 应用药水效果
            applyPotionEffects(entity, config);
            
            // 5. 应用技能系统
            applySkills(entity, config);
            // 尝试挂载最小技能执行器（与元数据并行）
            if (skillExecutor != null) {
                skillExecutor.attachSkills(entity, config);
            }
            
            // 6. 应用粒子效果
            applyParticleEffects(entity, config);
            
            // 7. 应用行为设置
            applyBehaviorSettings(entity, config);
            
            // 8. 设置实体标识
            setEntityIdentifiers(entity, config);
            
            logger.info("成功生成IDZ怪物: " + monsterId + " (实体类型: " + entity.getType() + ")");
            return entity;
            
        } catch (Exception e) {
            logger.severe("生成IDZ怪物时发生异常: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 生成基础实体
     */
    private LivingEntity spawnBaseEntity(Location location, IDZMonsterConfig config) {
        try {
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.ZOMBIE; // 默认为僵尸
            }
            
            // 生成实体
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, entityType);
            
            // 设置基础信息
            if (config.getDisplayName() != null && !config.getDisplayName().isEmpty()) {
                entity.setCustomName(config.getDisplayName());
                entity.setCustomNameVisible(true);
            }
            
            return entity;
            
        } catch (Exception e) {
            logger.severe("生成基础实体失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 应用怪物属性
     */
    private void applyAttributes(LivingEntity entity, IDZMonsterConfig config) {
        try {
            logger.info("开始应用属性到实体: " + config.getMonsterId());
            logger.info("配置属性 - 血量: " + config.getHealth() + ", 攻击: " + config.getAttackDamage() + ", 速度: " + config.getMovementSpeed());

            // 设置血量 - 使用兼容性方法
            if (config.getHealth() > 0) {
                try {
                    AttributeInstance healthAttr = getHealthAttribute(entity);
                    if (healthAttr != null) {
                        double oldHealth = healthAttr.getBaseValue();
                        healthAttr.setBaseValue(config.getHealth());
                        entity.setHealth(config.getHealth());
                        logger.info("血量设置成功: " + oldHealth + " → " + config.getHealth());
                    } else {
                        logger.warning("无法获取血量属性");
                    }
                } catch (Exception e) {
                    logger.warning("设置血量失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                logger.warning("血量配置无效: " + config.getHealth());
            }

            // 设置移动速度 - 使用兼容性方法
            if (config.getMovementSpeed() > 0) {
                try {
                    AttributeInstance speedAttr = getMovementSpeedAttribute(entity);
                    if (speedAttr != null) {
                        double oldSpeed = speedAttr.getBaseValue();
                        speedAttr.setBaseValue(config.getMovementSpeed());
                        logger.info("移动速度设置成功: " + oldSpeed + " → " + config.getMovementSpeed());
                    } else {
                        logger.warning("无法获取移动速度属性");
                    }
                } catch (Exception e) {
                    logger.warning("设置移动速度失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                logger.warning("移动速度配置无效: " + config.getMovementSpeed());
            }

            // 设置攻击伤害 - 使用兼容性方法
            if (config.getAttackDamage() > 0) {
                try {
                    AttributeInstance attackAttr = getAttackDamageAttribute(entity);
                    if (attackAttr != null) {
                        double oldAttack = attackAttr.getBaseValue();
                        attackAttr.setBaseValue(config.getAttackDamage());
                        logger.info("攻击伤害设置成功: " + oldAttack + " → " + config.getAttackDamage());
                    } else {
                        logger.warning("无法获取攻击伤害属性");
                    }
                } catch (Exception e) {
                    logger.warning("设置攻击伤害失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                logger.warning("攻击伤害配置无效: " + config.getAttackDamage());
            }

            // 设置护甲值
            if (config.getArmor() > 0) {
                try {
                    AttributeInstance armorAttr = entity.getAttribute(Attribute.ARMOR);
                    if (armorAttr != null) {
                        armorAttr.setBaseValue(config.getArmor());
                    }
                } catch (Exception e) {
                    logger.warning("设置护甲值失败: " + e.getMessage());
                }
            }

            // 暂时跳过护甲韧性和击退抗性，因为这些属性可能不存在于当前版本
            logger.info("跳过高级属性设置（护甲韧性、击退抗性）");
            
            logger.info("成功应用属性到实体: " + config.getMonsterId());
            
        } catch (Exception e) {
            logger.warning("应用属性失败: " + e.getMessage());
        }
    }
    
    /**
     * 应用装备
     */
    private void applyEquipment(LivingEntity entity, IDZMonsterConfig config) {
        try {
            logger.info("开始应用装备到实体: " + config.getMonsterId());

            EntityEquipment equipment = entity.getEquipment();
            if (equipment == null) {
                logger.warning("实体装备为null，无法应用装备");
                return;
            }

            // 记录装备配置信息
            logger.info("装备配置 - 主手: " + (config.getMainHand() != null ? config.getMainHand().getType() : "null") +
                       ", 头盔: " + (config.getHelmet() != null ? config.getHelmet().getType() : "null") +
                       ", 胸甲: " + (config.getChestplate() != null ? config.getChestplate().getType() : "null"));

            // 设置主手武器
            if (config.getMainHand() != null) {
                equipment.setItemInMainHand(config.getMainHand());
                equipment.setItemInMainHandDropChance(0.0f); // 不掉落
                logger.info("主手武器设置成功: " + config.getMainHand().getType());
            } else {
                logger.info("主手武器配置为空，跳过设置");
            }
            
            // 设置副手物品
            if (config.getOffHand() != null) {
                equipment.setItemInOffHand(config.getOffHand());
                equipment.setItemInOffHandDropChance(0.0f);
            }
            
            // 设置头盔
            if (config.getHelmet() != null) {
                equipment.setHelmet(config.getHelmet());
                equipment.setHelmetDropChance(0.0f);
            }
            
            // 设置胸甲
            if (config.getChestplate() != null) {
                equipment.setChestplate(config.getChestplate());
                equipment.setChestplateDropChance(0.0f);
            }
            
            // 设置护腿
            if (config.getLeggings() != null) {
                equipment.setLeggings(config.getLeggings());
                equipment.setLeggingsDropChance(0.0f);
            }
            
            // 设置靴子
            if (config.getBoots() != null) {
                equipment.setBoots(config.getBoots());
                equipment.setBootsDropChance(0.0f);
            }
            
            logger.info("成功应用装备到实体: " + config.getMonsterId());
            
        } catch (Exception e) {
            logger.warning("应用装备失败: " + e.getMessage());
        }
    }
    
    /**
     * 应用药水效果
     */
    private void applyPotionEffects(LivingEntity entity, IDZMonsterConfig config) {
        try {
            java.util.List<org.bukkit.potion.PotionEffect> effects = config.getPotionEffects();
            if (effects == null || effects.isEmpty()) {
                logger.info("未配置药水效果，跳过应用");
                return;
            }
            int applied = 0;
            for (org.bukkit.potion.PotionEffect effect : effects) {
                if (effect == null || effect.getType() == null) continue;
                entity.addPotionEffect(effect, true);
                applied++;
            }
            // 元数据标记用于调试
            entity.setMetadata("idz_potion_effects_count", new org.bukkit.metadata.FixedMetadataValue(plugin, applied));
            logger.info("成功为实体应用 " + applied + " 个药水效果: " + config.getMonsterId());

            // 某些服务端/其他监听器可能会在同tick修改效果，这里1tick后再次强制应用一遍以确保生效
            final org.bukkit.entity.LivingEntity targetRef = entity;
            final java.util.List<org.bukkit.potion.PotionEffect> effectsRef = new java.util.ArrayList<>(effects);
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                int reApplied = 0;
                for (org.bukkit.potion.PotionEffect efx : effectsRef) {
                    if (efx == null || efx.getType() == null) continue;
                    try {
                        targetRef.addPotionEffect(efx, true);
                        reApplied++;
                    } catch (Exception ignored) {}
                }
                if (reApplied > 0) {
                    targetRef.setMetadata("idz_potion_effects_count", new org.bukkit.metadata.FixedMetadataValue(plugin, reApplied));
                    logger.info("延迟1tick二次应用药水效果: " + reApplied + " 个 -> " + config.getMonsterId());
                }
            }, 1L);
        } catch (Exception e) {
            logger.warning("应用药水效果失败: " + e.getMessage());
        }
    }

    /**
     * 应用技能系统
     */
    private void applySkills(LivingEntity entity, IDZMonsterConfig config) {
        try {
            if (config.getSkills() != null && !config.getSkills().isEmpty()) {
                // 为实体设置技能标识
                entity.setMetadata("idz_monster_id", new org.bukkit.metadata.FixedMetadataValue(plugin, config.getMonsterId()));
                entity.setMetadata("idz_skills", new org.bukkit.metadata.FixedMetadataValue(plugin, config.getSkills()));

                logger.info("成功应用 " + config.getSkills().size() + " 个技能到实体: " + config.getMonsterId());
            }
        } catch (Exception e) {
            logger.warning("应用技能系统失败: " + e.getMessage());
        }
    }

    /**
     * 应用粒子效果
     */
    private void applyParticleEffects(LivingEntity entity, IDZMonsterConfig config) {
        try {
            logger.info("开始应用粒子效果到实体: " + config.getMonsterId());

            // 记录粒子配置信息
            if (config.getParticleTypes() != null && !config.getParticleTypes().isEmpty()) {
                logger.info("粒子配置 - 类型: " + String.join(", ", config.getParticleTypes()) +
                           ", 数量: " + config.getParticleCount() +
                           ", 间隔: " + config.getParticleInterval());

                // 为实体设置粒子效果标识
                entity.setMetadata("idz_particle_types", new org.bukkit.metadata.FixedMetadataValue(plugin, config.getParticleTypes()));
                entity.setMetadata("idz_particle_config", new org.bukkit.metadata.FixedMetadataValue(plugin, config));

                // 启动粒子效果任务
                startParticleEffectTask(entity, config);

                logger.info("成功应用粒子效果到实体: " + config.getMonsterId() + " (粒子类型: " + config.getParticleTypes().size() + "种)");
            } else {
                logger.info("粒子配置为空，跳过粒子效果设置");
            }
        } catch (Exception e) {
            logger.warning("应用粒子效果失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 启动粒子效果任务
     */
    private void startParticleEffectTask(LivingEntity entity, IDZMonsterConfig config) {
        if (config.getParticleInterval() <= 0) {
            return; // 如果间隔为0或负数，不启动粒子效果
        }

        plugin.getServer().getScheduler().runTaskTimer(plugin, () -> {
            if (entity == null || entity.isDead() || !entity.isValid()) {
                return; // 实体已死亡或无效，停止任务
            }

            try {
                // 生成粒子效果
                for (String particleType : config.getParticleTypes()) {
                    generateParticleEffect(entity, particleType, config);
                }
            } catch (Exception e) {
                logger.warning("生成粒子效果时出错: " + e.getMessage());
            }
        }, 20L, config.getParticleInterval()); // 1秒后开始，按配置间隔重复
    }

    /**
     * 生成单个粒子效果
     */
    private void generateParticleEffect(LivingEntity entity, String particleType, IDZMonsterConfig config) {
        try {
            Location location = entity.getLocation().clone().add(0, 1, 0); // 在实体上方生成

            // 获取粒子的高级参数配置
            IDZMonsterConfig.ParticleAdvancedConfig advancedConfig = config.getParticleAdvancedConfig(particleType);

            // 使用粒子助手生成粒子（如果可用）
            if (particleHelper != null) {
                // 这里可以调用粒子助手的方法来生成粒子
                // 暂时使用简单的粒子生成逻辑
            }

            // 简单的粒子生成逻辑
            org.bukkit.Particle bukkitParticle = getBukkitParticle(particleType);
            if (bukkitParticle != null) {
                entity.getWorld().spawnParticle(bukkitParticle, location,
                    config.getParticleCount(),
                    advancedConfig.getOffsetX(),
                    advancedConfig.getOffsetY(),
                    advancedConfig.getOffsetZ(),
                    advancedConfig.getSpeed());
            }

        } catch (Exception e) {
            logger.warning("生成粒子效果失败: " + particleType + " - " + e.getMessage());
        }
    }

    /**
     * 获取Bukkit粒子类型（简化版本）
     */
    private org.bukkit.Particle getBukkitParticle(String particleType) {
        try {
            switch (particleType.toUpperCase()) {
                case "FLAME": return org.bukkit.Particle.FLAME;
                case "SMOKE": return org.bukkit.Particle.SMOKE;
                case "HEART": return org.bukkit.Particle.HEART;
                case "ENCHANTED_HIT": return org.bukkit.Particle.ENCHANTED_HIT;
                case "REVERSE_PORTAL": return org.bukkit.Particle.REVERSE_PORTAL;
                case "PORTAL": return org.bukkit.Particle.PORTAL;
                case "CLOUD": return org.bukkit.Particle.CLOUD;
                case "EXPLOSION": return org.bukkit.Particle.EXPLOSION;
                default: return org.bukkit.Particle.FLAME; // 默认使用火焰粒子
            }
        } catch (Exception e) {
            return org.bukkit.Particle.FLAME;
        }
    }

    /**
     * 应用行为设置
     */
    private void applyBehaviorSettings(LivingEntity entity, IDZMonsterConfig config) {
        try {
            // 设置敌对性
            if (entity instanceof org.bukkit.entity.Mob) {
                org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) entity;

                // 暂时跳过跟随范围设置
                logger.info("跳过跟随范围设置（待后续完善）");
            }

            // 设置是否可以拾取物品（使用默认值）
            entity.setCanPickupItems(false); // 默认不拾取物品

            logger.info("成功应用行为设置到实体: " + config.getMonsterId());

        } catch (Exception e) {
            logger.warning("应用行为设置失败: " + e.getMessage());
        }
    }

    /**
     * 设置实体标识
     */
    private void setEntityIdentifiers(LivingEntity entity, IDZMonsterConfig config) {
        try {
            // 设置IDZ怪物标识
            entity.setMetadata("idz_monster", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
            entity.setMetadata("idz_monster_id", new org.bukkit.metadata.FixedMetadataValue(plugin, config.getMonsterId()));
            entity.setMetadata("idz_config", new org.bukkit.metadata.FixedMetadataValue(plugin, config));

            // 设置持久化标识，防止实体在区块卸载时消失
            entity.setPersistent(true);
            entity.setRemoveWhenFarAway(false);

            logger.info("成功设置实体标识: " + config.getMonsterId());

        } catch (Exception e) {
            logger.warning("设置实体标识失败: " + e.getMessage());
        }
    }

    /**
     * 获取血量属性 - 兼容不同Bukkit版本
     */
    private AttributeInstance getHealthAttribute(LivingEntity entity) {
        try {
            // 尝试新版本的属性名
            return entity.getAttribute(Attribute.valueOf("GENERIC_MAX_HEALTH"));
        } catch (Exception e1) {
            try {
                // 尝试旧版本的属性名
                return entity.getAttribute(Attribute.valueOf("MAX_HEALTH"));
            } catch (Exception e2) {
                logger.warning("无法找到血量属性，尝试的名称: GENERIC_MAX_HEALTH, MAX_HEALTH");
                return null;
            }
        }
    }

    /**
     * 获取移动速度属性 - 兼容不同Bukkit版本
     */
    private AttributeInstance getMovementSpeedAttribute(LivingEntity entity) {
        try {
            // 尝试新版本的属性名
            return entity.getAttribute(Attribute.valueOf("GENERIC_MOVEMENT_SPEED"));
        } catch (Exception e1) {
            try {
                // 尝试旧版本的属性名
                return entity.getAttribute(Attribute.valueOf("MOVEMENT_SPEED"));
            } catch (Exception e2) {
                logger.warning("无法找到移动速度属性，尝试的名称: GENERIC_MOVEMENT_SPEED, MOVEMENT_SPEED");
                return null;
            }
        }
    }

    /**
     * 获取攻击伤害属性 - 兼容不同Bukkit版本
     */
    private AttributeInstance getAttackDamageAttribute(LivingEntity entity) {
        try {
            // 尝试新版本的属性名
            return entity.getAttribute(Attribute.valueOf("GENERIC_ATTACK_DAMAGE"));
        } catch (Exception e1) {
            try {
                // 尝试旧版本的属性名
                return entity.getAttribute(Attribute.valueOf("ATTACK_DAMAGE"));
            } catch (Exception e2) {
                logger.warning("无法找到攻击伤害属性，尝试的名称: GENERIC_ATTACK_DAMAGE, ATTACK_DAMAGE");
                return null;
            }
        }
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("世界:%s, X:%.1f, Y:%.1f, Z:%.1f",
            location.getWorld().getName(), location.getX(), location.getY(), location.getZ());
    }
}
