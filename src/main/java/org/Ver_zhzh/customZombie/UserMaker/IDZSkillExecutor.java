package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.EnderCrystal;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.logging.Logger;

/**
 * 统一技能执行器（全量支持版）
 * - 支持所有97个技能的通用实现
 * - 兼容 idX_ 和 idcX_ 前缀技能名（自动归一化）
 * - 通用处理器覆盖：光环、闪电、投射物、爆炸、召唤、增益、陷阱、位移等
 * - 事件触发：攻击命中、受伤、死亡等
 * - 末影水晶保护：防止IDC22水晶被破坏和爆炸
 */
public class IDZSkillExecutor implements Listener {
    private final Plugin plugin;
    private final Logger logger;
    private final Map<String, SkillHandler> skillHandlers;
    private final IDZSkillExecutorExtensions extensions;

    public IDZSkillExecutor(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillHandlers = new HashMap<>();
        this.extensions = new IDZSkillExecutorExtensions(plugin, logger);
        initializeSkillHandlers();

        // 注册末影水晶保护监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        logger.info("技能执行器: 已注册 " + skillHandlers.size() + " 个技能处理器");
        logger.info("技能执行器: 已注册末影水晶保护监听器");
    }

    /**
     * 技能处理器接口
     */
    public interface SkillHandler {
        /**
         * 为实体挂载技能
         * @param entity 目标实体
         * @param config 技能配置
         * @return 任务ID，如果不需要定时任务返回null
         */
        Integer attach(LivingEntity entity, SkillConfig config);
    }

    /**
     * 初始化所有技能处理器
     */
    private void initializeSkillHandlers() {
        // 光环类技能（周期范围伤害/减益）
        SkillHandler auraDamage = this::handleAuraDamage;
        // 移除错误的wither_aura技能（原版IDC21没有凋零光环）
        // registerSkill("wither_aura", auraDamage);
        registerSkill("poison_aura", auraDamage);
        registerSkill("damage_aura", auraDamage);
        // 移除frost_aura和smoke_particles的错误映射，改为专门处理器
        // registerSkill("frost_aura", auraDamage);
        // registerSkill("smoke_particles", auraDamage);
        // remove wrong mapping: death_particles should be death-triggered; handled in IDZSkillEventListener
        // registerSkill("death_particles", auraDamage);
        // 去重："范围电流" 与 "电流攻击" 功能重复，保留 electric_attack
        // registerSkill("area_electric", auraDamage);

        // 闪电类技能
        SkillHandler lightning = this::handleLightning;
        registerSkill("lightning_attack", lightning);
        registerSkill("global_lightning", lightning);
        registerSkill("area_lightning", lightning);
        registerSkill("teleport_lightning", lightning);
        registerSkill("lightning_summon", lightning);

        // 投射物类技能
        SkillHandler projectile = this::handleProjectile;
        registerSkill("poison_arrow", projectile);
        registerSkill("arrow_attack", projectile);
        registerSkill("flame_attack", projectile);
        registerSkill("particle_ball", projectile);
        // 移除red_particle_ball的错误映射，改为专门处理器
        // registerSkill("red_particle_ball", projectile);
        // 移除skull_attack的错误映射，改为专门处理器（IDC21凋零头颅攻击）
        // registerSkill("skull_attack", projectile);
        // 移除snowball_barrage的错误映射，改为专门处理器
        // registerSkill("snowball_barrage", projectile);
        // 移除shulker_bullet的错误映射，改为专门处理器
        // registerSkill("shulker_bullet", projectile);
        // 移除particle_gun的错误映射，改为专门处理器
        // registerSkill("particle_gun", projectile);
        // 移除sonic_attack的错误映射，改为专门处理器
        // registerSkill("sonic_attack", projectile);

        // 爆炸类技能
        SkillHandler explosion = this::handleExplosion;
        registerSkill("explosion_skill", explosion);
        registerSkill("explosion_death", explosion);

        // 召唤类技能
        SkillHandler summon = this::handleSummon;
        registerSkill("summon_armed_zombie", summon);
        registerSkill("summon_vindicator", summon);
        registerSkill("timed_summon", summon);
        registerSkill("continuous_summon", summon);
        // registerSkill("summon_warriors", summon); // duplicate of timed_summon, remove
        registerSkill("death_summon", summon);
        registerSkill("detect_summon", summon);
        registerSkill("damage_summon", summon);
        registerSkill("summon_enhanced_zombie", summon);
        registerSkill("evoker_summon_vindicator", summon);

        // 自身增益类技能
        SkillHandler buffSelf = extensions::handleBuffSelf;
        registerSkill("max_strength", buffSelf);
        registerSkill("max_defense", buffSelf);
        registerSkill("max_speed", buffSelf);
        registerSkill("max_regeneration", buffSelf);
        registerSkill("ultimate_power", buffSelf);
        registerSkill("repair_ability", buffSelf);
        // 去重："攻击buff" 与 "战斗buff" 重复，保留 attack_buff
        // registerSkill("combat_buff", buffSelf);
        registerSkill("critical_aura", buffSelf);
        registerSkill("iron_fist", buffSelf);

        // 范围增益/减益类技能
        SkillHandler areaBuffDebuff = extensions::handleAreaBuffDebuff;
        registerSkill("area_buff", areaBuffDebuff);
        registerSkill("area_debuff", areaBuffDebuff);
        registerSkill("global_debuff", areaBuffDebuff);
        // 修复：全局伤害应使用独立处理器，而不是范围debuff处理器
        registerSkill("global_damage", this::handleGlobalDamage);
        registerSkill("protective_aura", areaBuffDebuff);
        registerSkill("aura_buff", areaBuffDebuff);

        // 陷阱控制类技能
        SkillHandler trapControl = extensions::handleTrapControl;
        registerSkill("web_trap", trapControl);
        registerSkill("knockup_pillar", trapControl);
        // 移除pillar_attack的错误映射，改为IDC22专门处理器
        // registerSkill("pillar_attack", trapControl);
        registerSkill("fence_attack", trapControl);
        registerSkill("ground_slam", trapControl);
        registerSkill("obsidian_attack", trapControl);
        // 移除crystal_attack的错误映射，改为IDC22专门处理器
        // registerSkill("crystal_attack", trapControl);
        // 移除king_obsidian_attack的错误映射（不存在的技能）
        // registerSkill("king_obsidian_attack", trapControl);

        // 位移类技能
        SkillHandler dashTeleport = extensions::handleDashTeleport;
        // 移除dash_attack的错误映射，改为专门处理器
        // registerSkill("dash_attack", dashTeleport);
        registerSkill("teleport_attack", dashTeleport);
        registerSkill("stealth_attack", dashTeleport);
        registerSkill("flight_ability", dashTeleport);
        registerSkill("dragon_flight", dashTeleport);
        registerSkill("horse_flight", dashTeleport);

        // 命中效果类技能（需要事件监听）
        SkillHandler onHit = extensions::handleOnHitEffects;
        registerSkill("poison_attack", onHit);
        registerSkill("freeze_attack", onHit);
        // registerSkill("freeze_ability", onHit); // moved to special effects handler
        registerSkill("collision_damage", onHit);
        registerSkill("attack_heal", onHit);
        registerSkill("poison_spit", onHit);
        // 移除breath_attack的错误映射，改为IDC22专门处理器
        // registerSkill("breath_attack", onHit);
        registerSkill("fang_attack", onHit);

        // 特殊效果类技能
        SkillHandler special = extensions::handleSpecialEffects;
        // 修复：IDC12式物品栏混乱应为命中触发技能，读取 chaos_* 参数
        registerSkill("inventory_chaos", onHit);
        // 移除shulker_inventory_chaos的错误映射，改为专门处理器
        // registerSkill("shulker_inventory_chaos", special);
        registerSkill("fog_generation", special);
        registerSkill("time_control", special);
        registerSkill("freeze_ability", special);
        registerSkill("dimension_control", special);
        // 移除anti_zombification技能，这不是真正的技能而是实体属性
        // registerSkill("anti_zombification", special);
        // 移除shulker_invisibility和snow_freeze_aura的错误映射，改为专门处理器
        // registerSkill("shulker_invisibility", special);
        // registerSkill("snow_freeze_aura", special);
        registerSkill("flame_ring", special);
        registerSkill("dna_spiral", special);
        registerSkill("soul_guard", special);
        registerSkill("evoker_damage_aura", special);

        // IDC14/IDC13风格的专门技能处理器
        SkillHandler machineGun = (caster, cfg) -> extensions.handleMachineGun(caster, cfg);
        SkillHandler smokeParticles = (caster, cfg) -> extensions.handleSmokeParticles(caster, cfg);
        SkillHandler frostAura = (caster, cfg) -> extensions.handleFrostAura(caster, cfg);

        registerSkill("particle_gun", machineGun);
        registerSkill("smoke_particles", smokeParticles);
        registerSkill("frost_aura", frostAura);

        // 新增的IDC风格专门技能处理器
        SkillHandler dashAttack = (caster, cfg) -> extensions.handleDashAttack(caster, cfg);
        SkillHandler redParticleBall = (caster, cfg) -> extensions.handleRedParticleBall(caster, cfg);
        SkillHandler shulkerBullet = (caster, cfg) -> extensions.handleShulkerBullet(caster, cfg);
        SkillHandler shulkerInventoryChaos = (caster, cfg) -> extensions.handleShulkerInventoryChaos(caster, cfg);

        registerSkill("dash_attack", dashAttack);
        registerSkill("red_particle_ball", redParticleBall);
        registerSkill("shulker_bullet", shulkerBullet);
        registerSkill("shulker_inventory_chaos", shulkerInventoryChaos);

        // IDC16/IDC17风格的专门技能处理器
        SkillHandler shulkerInvisibility = (caster, cfg) -> extensions.handleShulkerInvisibility(caster, cfg);
        SkillHandler snowballBarrage = (caster, cfg) -> extensions.handleSnowballBarrage(caster, cfg);
        SkillHandler snowFreezeAura = (caster, cfg) -> extensions.handleSnowFreezeAura(caster, cfg);

        registerSkill("shulker_invisibility", shulkerInvisibility);
        registerSkill("snowball_barrage", snowballBarrage);
        registerSkill("snow_freeze_aura", snowFreezeAura);

        // IDC18风格的专门技能处理器
        SkillHandler grassBlockAttack = (caster, cfg) -> extensions.handleGrassBlockAttack(caster, cfg);
        SkillHandler playerTracking = (caster, cfg) -> extensions.handlePlayerTracking(caster, cfg);
        SkillHandler sonicAttack = (caster, cfg) -> extensions.handleSonicAttack(caster, cfg);

        registerSkill("grass_block_attack", grassBlockAttack);
        registerSkill("player_tracking", playerTracking);
        registerSkill("sonic_attack", sonicAttack);

        // IDC19风格的专门技能处理器
        SkillHandler weatherControl = (caster, cfg) -> extensions.handleWeatherControl(caster, cfg);
        SkillHandler tridentAttack = (caster, cfg) -> extensions.handleTridentAttack(caster, cfg);
        SkillHandler particleAttack = (caster, cfg) -> extensions.handleParticleAttack(caster, cfg);
        SkillHandler lightningAttack = (caster, cfg) -> extensions.handleLightningAttack(caster, cfg);
        SkillHandler invisibilitySkill = (caster, cfg) -> extensions.handleInvisibilitySkill(caster, cfg);

        registerSkill("weather_control", weatherControl);
        registerSkill("trident_attack", tridentAttack);
        registerSkill("particle_attack", particleAttack);
        registerSkill("lightning_attack", lightningAttack);
        registerSkill("invisibility_skill", invisibilitySkill);
        // teleport_skill暂不实现，避免与其他传送技能重复

        // IDC20风格的专门技能处理器（避免与IDC18重复）
        SkillHandler playerTrackingIDC20 = (caster, cfg) -> extensions.handlePlayerTracking(caster, cfg); // 复用IDC18的追踪
        SkillHandler sonicAttackIDC20 = (caster, cfg) -> extensions.handleSonicAttack(caster, cfg); // 复用IDC18的声波弹

        registerSkill("player_tracking_idc20", playerTrackingIDC20);
        registerSkill("sonic_attack_idc20", sonicAttackIDC20);

        // IDC20专门的黑曜石技能处理器
        SkillHandler obsidianAttack = (caster, cfg) -> extensions.handleObsidianAttack(caster, cfg);
        SkillHandler knockupPillar = (caster, cfg) -> extensions.handleKnockupPillar(caster, cfg);

        registerSkill("obsidian_attack", obsidianAttack);
        registerSkill("knockup_pillar", knockupPillar);

        // IDC21专门的凋零头颅攻击处理器
        SkillHandler skullAttack = (caster, cfg) -> extensions.handleSkullAttack(caster, cfg);
        registerSkill("skull_attack", skullAttack);

        // IDC22专门的技能处理器（7个真实技能）
        SkillHandler crystalAttack = (caster, cfg) -> extensions.handleCrystalAttack(caster, cfg);
        registerSkill("crystal_attack", crystalAttack);

        SkillHandler breathAttack = (caster, cfg) -> extensions.handleBreathAttack(caster, cfg);
        registerSkill("breath_attack", breathAttack);

        SkillHandler obsidianBlocksAttack = (caster, cfg) -> extensions.handleObsidianBlocksAttack(caster, cfg);
        registerSkill("obsidian_blocks_attack", obsidianBlocksAttack);

        SkillHandler obsidianPillarAttack = (caster, cfg) -> extensions.handleObsidianPillarAttack(caster, cfg);
        registerSkill("obsidian_pillar_attack", obsidianPillarAttack);

        SkillHandler enderField = (caster, cfg) -> extensions.handleEnderField(caster, cfg);
        registerSkill("ender_field", enderField);

        SkillHandler summonMutants = (caster, cfg) -> extensions.handleSummonMutants(caster, cfg);
        registerSkill("summon_mutants", summonMutants);

        // 命中触发类技能（攻击回血）- 这些技能在IDZSkillEventListener中处理
        // lifesteal和attack_heal技能通过事件监听器处理，不需要在这里注册处理器

        // 重新映射：将尖牙攻击归入特殊效果处理（覆盖上面的 onHit 映射）
        registerSkill("fang_attack", special);

        // 添加更多ID系列技能
        registerSkill("electric_attack", auraDamage);
        registerSkill("thunder_attack", lightning);
        // 去重：electric_skill 与 electric_attack 功能重复，保留 electric_attack
        // registerSkill("electric_skill", auraDamage);
        // 去重："冰冻技能" 与 "冰冻能力" 重复，保留 freeze_ability
        // registerSkill("freeze_skill", auraDamage);
        registerSkill("ranged_magic_attack", projectile);
        registerSkill("random_summon", summon);
        // 去重：damage_triggered_summon 与 damage_summon 重复，移除重复注册
        // registerSkill("damage_triggered_summon", summon);
        // 去重："特殊召唤" 与 "定时召唤" 重复，保留 timed_summon
        // registerSkill("special_summon", summon);
        registerSkill("attack_buff", buffSelf);

        // 添加更多IDC系列技能
        registerSkill("triple_arrow", projectile);
        // 修复：火焰光环应使用特殊效果处理器（真实火焰粒子+参数读取），避免被通用光环覆盖
        // registerSkill("flame_ring", auraDamage);
        registerSkill("freeze_attack", onHit);
        registerSkill("web_trap", trapControl);
        registerSkill("poison_spit", onHit);
        // 移除重复的技能注册，已在上面使用专门处理器
        // registerSkill("shulker_bullet", projectile);
        // registerSkill("shulker_invisibility", special);
        // registerSkill("snowball_barrage", projectile);
        // registerSkill("snow_freeze_aura", auraDamage);
        registerSkill("dna_spiral", special);
        registerSkill("collision_damage", onHit);
        registerSkill("evoker_summon_vindicator", summon);
        registerSkill("evoker_damage_aura", auraDamage);

        logger.info("技能处理器注册完成，共支持 " + skillHandlers.size() + " 个技能");
    }

    private void registerSkill(String skillName, SkillHandler handler) {
        skillHandlers.put(skillName, handler);
    }

    /**
     * 为实体挂载所有技能
     */
    public void attachSkills(LivingEntity entity, IDZMonsterConfig config) {
        List<SkillConfig> skills = config.getSkills();
        if (skills == null || skills.isEmpty()) {
            logger.info("技能执行器: 无技能可挂载 → " + config.getMonsterId());
            return;
        }

        List<Integer> taskIds = new ArrayList<>();
        int successCount = 0;

        for (SkillConfig sc : skills) {
            String rawName = sc.getSkillName();
            String normalizedName = normalizeSkillName(rawName);

            try {
                // 详细的参数调试信息
                Map<String, Object> params = sc.getParameters();
                logger.info("技能参数调试 - " + rawName + ": " + params);

                SkillHandler handler = skillHandlers.get(normalizedName);
                if (handler != null) {
                    Integer taskId = handler.attach(entity, sc);
                    if (taskId != null) {
                        taskIds.add(taskId);
                    }
                    successCount++;
                    logger.info("技能挂载成功: " + rawName + " → " + normalizedName + " (参数: " + params.size() + "个)");
                } else {
                    logger.warning("技能执行器: 未注册的技能 → " + rawName + " (归一化: " + normalizedName + ")");
                }
            } catch (Exception e) {
                logger.warning("技能挂载失败: " + rawName + " → " + e.getMessage());
                e.printStackTrace();
            }
        }

        if (!taskIds.isEmpty()) {
            entity.setMetadata("idz_skill_tasks", new FixedMetadataValue(plugin, taskIds));
        }

        logger.info("技能执行器: 已为实体挂载 " + successCount + "/" + skills.size() + " 个技能 → " + config.getMonsterId());
    }

    private String normalizeSkillName(String raw) {
        if (raw == null) return "";
        String lower = raw.toLowerCase();

        // 兼容 idcX_ 前缀：如 idc5_area_electric → area_electric
        if (lower.startsWith("idc")) {
            int idx = raw.indexOf('_');
            if (idx > 0 && idx + 1 < raw.length()) {
                return raw.substring(idx + 1);
            }
        }

        // 兼容 idX_ 前缀：如 id5_poison_attack → poison_attack
        if (lower.startsWith("id") && raw.length() > 2 && Character.isDigit(raw.charAt(2))) {
            int idx = raw.indexOf('_');
            if (idx > 0 && idx + 1 < raw.length()) {
                return raw.substring(idx + 1);
            }
        }

        // 兼容中文技能名 → 英文内部名（防止前端以中文名传入时无法映射）
        if (raw.contains("瞬移") && (raw.contains("雷") || raw.contains("电") || raw.contains("電"))) {
            return "teleport_lightning";
        }
        if (raw.contains("受伤触发召唤") || raw.equalsIgnoreCase("damage_triggered_summon")) {
            return "damage_summon"; // 归一化
        }
        if (raw.contains("电击技能") || raw.equalsIgnoreCase("electric_skill")) {
            return "electric_attack"; // 归一化
        }
        if (raw.contains("远程魔法") || raw.contains("远程魔法攻击")) {
            return "ranged_magic_attack";
        }
        if ((raw.contains("雷电") || raw.contains("雷電") || raw.contains("雷击") || raw.contains("雷擊")) && raw.contains("召")) {
            return "lightning_summon";
        }
        if (raw.contains("时间控制") || raw.contains("時間控制") || (raw.contains("时间") && raw.contains("控制"))) {
            return "time_control";
        }
        if ((raw.contains("全局") || raw.contains("全体")) && (raw.contains("雷"))) {
            return "global_lightning";
        }
        // 常用中文技能名称归一化
        if (raw.contains("剧毒攻击") || raw.contains("劇毒攻击") || raw.contains("剧毒")) {
            return "poison_attack";
        }
        if (raw.contains("烈焰攻击") || raw.contains("烈焰")) {
            return "flame_attack";
        }
        if (raw.contains("闪电攻击") || raw.contains("閃電攻击") || raw.contains("雷电攻击") || raw.contains("雷电") || raw.contains("雷擊") || raw.contains("雷击")) {
            return "lightning_attack";
        }
        if (raw.contains("暴击光环") || raw.contains("暴擊光環") || raw.contains("暴击")) {
            return "critical_aura";
        }
        if (raw.contains("唤魔者光环") || raw.contains("喚魔者光環") || (raw.contains("唤魔者") && raw.contains("光环"))) {
            return "evoker_damage_aura";
        }
        if (raw.contains("撞击伤害") || raw.contains("撞擊傷害") || (raw.contains("撞击") && raw.contains("伤害"))) {
            return "collision_damage";
        }

        // IDZ技能中文名映射
        if (raw.contains("召唤强力僵尸") || raw.contains("召喚強力僵屍") || (raw.contains("召唤") && raw.contains("强力") && raw.contains("僵尸"))) {
            return "summon_enhanced_zombie";
        }
        if (raw.contains("物品栏混乱") || raw.contains("物品欄混亂") || (raw.contains("物品栏") && raw.contains("混乱"))) {
            return "inventory_chaos";
        }
        if (raw.contains("三连发箭") || raw.contains("三連發箭") || (raw.contains("三连发") && raw.contains("箭"))) {
            return "triple_arrow";
        }
        if (raw.contains("粒子机枪") || raw.contains("粒子機槍") || (raw.contains("粒子") && raw.contains("机枪"))) {
            return "particle_gun";
        }
        if (raw.contains("烟雾粒子") || raw.contains("煙霧粒子") || (raw.contains("烟雾") && raw.contains("粒子"))) {
            return "smoke_particles";
        }
        if (raw.contains("冰霜光环") || raw.contains("冰霜光環") || (raw.contains("冰霜") && raw.contains("光环"))) {
            return "frost_aura";
        }
        if (raw.contains("冲刺攻击") || raw.contains("衝刺攻擊") || (raw.contains("冲刺") && raw.contains("攻击"))) {
            return "dash_attack";
        }
        if (raw.contains("红色粒子球") || raw.contains("紅色粒子球") || (raw.contains("红色") && raw.contains("粒子球"))) {
            return "red_particle_ball";
        }
        if (raw.contains("攻击回血") || raw.contains("攻擊回血") || (raw.contains("攻击") && raw.contains("回血")) || raw.contains("生命偷取") || raw.contains("吸血")) {
            return "lifesteal";
        }
        if (raw.contains("潜影贝子弹") || raw.contains("潛影貝子彈") || (raw.contains("潜影贝") && raw.contains("子弹"))) {
            return "shulker_bullet";
        }
        if (raw.contains("潜影贝混乱") || raw.contains("潛影貝混亂") || (raw.contains("潜影贝") && raw.contains("混乱"))) {
            return "shulker_inventory_chaos";
        }
        if (raw.contains("潜影贝隐身") || raw.contains("潛影貝隱身") || (raw.contains("潜影贝") && raw.contains("隐身"))) {
            return "shulker_invisibility";
        }
        if (raw.contains("雪球弹幕") || raw.contains("雪球彈幕") || (raw.contains("雪球") && raw.contains("弹幕"))) {
            return "snowball_barrage";
        }
        if (raw.contains("雪人冰冻光环") || raw.contains("雪人冰凍光環") || (raw.contains("雪人") && raw.contains("冰冻") && raw.contains("光环"))) {
            return "snow_freeze_aura";
        }
        if (raw.contains("草方块攻击") || raw.contains("草方塊攻擊") || raw.contains("草方块伸展攻击") || raw.contains("草方塊伸展攻擊") || (raw.contains("草方块") && raw.contains("攻击"))) {
            return "grass_block_attack";
        }
        if (raw.contains("声波攻击") || raw.contains("聲波攻擊") || raw.contains("声波弹攻击") || raw.contains("聲波彈攻擊") || (raw.contains("声波") && raw.contains("攻击"))) {
            return "sonic_attack";
        }
        if (raw.contains("主动追踪") || raw.contains("主動追蹤") || raw.contains("主动追踪玩家") || raw.contains("主動追蹤玩家") || (raw.contains("主动") && raw.contains("追踪"))) {
            return "player_tracking";
        }
        if (raw.contains("天气控制") || raw.contains("天氣控制") || (raw.contains("天气") && raw.contains("控制"))) {
            return "weather_control";
        }
        if (raw.contains("粒子攻击") || raw.contains("粒子攻擊") || (raw.contains("粒子") && raw.contains("攻击"))) {
            return "particle_attack";
        }
        if (raw.contains("三叉戟攻击") || raw.contains("三叉戟攻擊") || (raw.contains("三叉戟") && raw.contains("攻击"))) {
            return "trident_attack";
        }
        if (raw.contains("闪电攻击") || raw.contains("閃電攻擊") || (raw.contains("闪电") && raw.contains("攻击"))) {
            return "lightning_attack";
        }
        if (raw.contains("隐身能力") || raw.contains("隱身能力") || (raw.contains("隐身") && raw.contains("能力"))) {
            return "invisibility_skill";
        }
        // 移除传送能力映射 - 避免与其他传送技能重复
        // if (raw.contains("传送能力") || raw.contains("傳送能力") || (raw.contains("传送") && raw.contains("能力"))) {
        //     return "teleport_skill";
        // }

        // IDC20技能映射（避免与其他版本重复）
        if (raw.contains("黑曜石攻击") || raw.contains("黑曜石方块攻击") || (raw.contains("黑曜石") && raw.contains("攻击"))) {
            return "obsidian_attack";
        }
        if (raw.contains("击飞黑曜石柱") || raw.contains("击飞") && raw.contains("黑曜石柱")) {
            return "knockup_pillar";
        }

        // 移除防猪人化的映射，这不是真正的技能
        // if (raw.contains("防猪人化") || raw.contains("防豬人化")) return "anti_zombification";

        // 统一别名：召唤战士 → 定时召唤
        if (raw.equalsIgnoreCase("summon_warriors")) return "timed_summon";
        return raw;
    }

    // =========== 通用技能处理器 ===========

    /**
     * 光环类技能：周期性对范围内玩家造成伤害/减益
     */
    private Integer handleAuraDamage(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("光环技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        // 多种参数名兼容（优先读取通用光环/IDC7配置）
        int interval = toInt(cfg.getParameter("aura_interval",
                      cfg.getParameter("damage_interval",
                      cfg.getParameter("interval",
                      cfg.getParameter("electric_interval",
                      cfg.getParameter("poison_interval",
                      cfg.getParameter("wither_interval", 100)))))));

        double damage = toDouble(cfg.getParameter("aura_damage",
                        cfg.getParameter("damage",
                        cfg.getParameter("electric_damage",
                        cfg.getParameter("poison_damage",
                        cfg.getParameter("wither_damage", 5.0))))));

        double range = toDouble(cfg.getParameter("aura_range",
                       cfg.getParameter("range",
                       cfg.getParameter("electric_range",
                       cfg.getParameter("poison_range",
                       cfg.getParameter("wither_range", 10.0))))));

        if (interval <= 0) interval = 100;
        final int intervalLocal = interval;

        String skillName = cfg.getSkillName();
        logger.info(String.format("挂载技能[%s]: interval=%d, damage=%.2f, range=%.2f (从%d个参数中读取)",
                   skillName, intervalLocal, damage, range, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            World world = caster.getWorld();
            Location center = caster.getLocation();

            // 可视化光环
            try {
                String lower = (skillName == null) ? "" : skillName.toLowerCase();
                if (lower.contains("evoker") || skillName.contains("唤魔者")) {
                    // 唤魔者光环：4个较大粒子球按顺时针快速环绕（每tick渲染一段时间）
                    final int balls = 4;
                    final double radius = Math.max(1.2, Math.min(3.5, toDouble(cfg.getParameter("evoker_aura_radius", range / 2.2))));
                    final double speed = Math.max(0.05, toDouble(cfg.getParameter("evoker_aura_speed", 0.35))); // 每tick角速度
                    final int defaultAnim = Math.max(20, intervalLocal);
                    final int animTicks = Math.max(20, Math.min(200, toInt(cfg.getParameter("evoker_aura_anim_ticks", defaultAnim))));
                    final org.bukkit.World w = caster.getWorld();
                    new org.bukkit.scheduler.BukkitRunnable(){
                        double phase = 0.0;
                        int t = 0;
                        @Override public void run(){
                            if (!isAlive(caster)) { cancel(); return; }
                            org.bukkit.Location c = caster.getLocation();
                            double baseY = c.getY() + 1.25;
                            for (int i = 0; i < balls; i++) {
                                double ang = phase + (2 * Math.PI * i / balls);
                                double x = c.getX() + Math.cos(ang) * radius;
                                double z = c.getZ() + Math.sin(ang) * radius;
                                org.bukkit.Location orb = new org.bukkit.Location(w, x, baseY, z);
                                // 主体使用灵魂火焰，辅以青色Dust更贴近图示
                                w.spawnParticle(org.bukkit.Particle.SOUL_FIRE_FLAME, orb, 6, 0.04, 0.04, 0.04, 0.0);
                                w.spawnParticle(org.bukkit.Particle.DUST, orb, 1, 0, 0, 0, 0,
                                        new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(0, 255, 255), 1.2f));
                            }
                            phase += speed; t++;
                            if (t >= animTicks) { cancel(); }
                        }
                    }.runTaskTimer(plugin, 0L, 1L);
                } else {
                    // 默认：在脚底生成环形粒子光环（可视化），半径与光环范围一致
                    int points = Math.max(12, (int) Math.min(48, Math.round(range * 4)));
                    double y = center.getY() + 0.05;
                    for (int i = 0; i < points; i++) {
                        double ang = 2 * Math.PI * i / points;
                        double x = center.getX() + range * Math.cos(ang);
                        double z = center.getZ() + range * Math.sin(ang);
                        org.bukkit.Location pl = new org.bukkit.Location(world, x, y, z);
                        world.spawnParticle(org.bukkit.Particle.DUST, pl, 1, 0.02, 0.02, 0.02, 0,
                                new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(0, 255, 255), 0.9f));
                    }
                }
            } catch (Throwable ignored) {}

            for (Player p : world.getPlayers()) {
                if (!p.isOnline() || p.isDead()) continue;
                if (p.getLocation().distanceSquared(center) <= range * range) {
                    p.damage(damage, caster);
                    // 根据技能类型添加特殊效果（支持参数配置）
                    extensions.applyAuraEffect(p, skillName, cfg);
                }
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 闪电类技能：对最近/范围玩家落雷
     */
    private Integer handleLightning(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("闪电技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        int interval = toInt(cfg.getParameter("teleport_interval",
                      cfg.getParameter("skill_interval",
                      cfg.getParameter("interval",
                      cfg.getParameter("lightning_interval",
                      cfg.getParameter("thunder_interval",
                      cfg.getParameter("global_interval", 100)))))));

        double damage = toDouble(cfg.getParameter("teleport_damage",
                        cfg.getParameter("damage",
                        cfg.getParameter("lightning_damage",
                        cfg.getParameter("thunder_damage",
                        cfg.getParameter("global_damage", 8.0))))));

        double range = toDouble(cfg.getParameter("teleport_range",
                       cfg.getParameter("range",
                       cfg.getParameter("lightning_range",
                       cfg.getParameter("thunder_range",
                       cfg.getParameter("global_range", 10.0))))));

        int count = toInt(cfg.getParameter("thunder_count",
                    cfg.getParameter("lightning_count",
                    cfg.getParameter("global_count",
                    cfg.getParameter("count", 1)))));
        if (count <= 0) count = 1;

        if (interval <= 0) interval = 100;

        String skillName = cfg.getSkillName() == null ? "" : cfg.getSkillName();
        String lower = skillName.toLowerCase();
        boolean teleportOnStrike = lower.contains("teleport") || skillName.contains("瞬移");
        boolean summonOnStrike = lower.contains("summon") || skillName.contains("召") || Boolean.TRUE.equals(cfg.getParameter("summon_on_strike"));
        // 召唤相关参数（沿用死亡召唤/周期召唤的GUI键名）
        final String monsterIdOnStrike = (cfg.getParameter("monster_id") != null)
                ? String.valueOf(cfg.getParameter("monster_id")) : null;
        final int summonEach = Math.max(1, toInt(cfg.getParameter("summon_count_on_strike",
                cfg.getParameter("summon_count", cfg.getParameter("count_on_strike", 1)))));

        logger.info(String.format("挂载技能[%s]: interval=%d, damage=%.2f, range=%.2f, count=%d, teleport=%s, summon=%s (从%d个参数中读取)",
                   skillName, interval, damage, range, count, teleportOnStrike, summonOnStrike, allParams.size()));

        final int strikeCount = count; // lambda 捕获需要final
        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;

            java.util.function.Consumer<Player> strike = (Player p) -> {
                if (p == null || p.isDead()) return;

                // 瞬移雷击：雷击瞬间将施法者瞬移至玩家位置附近
                if (teleportOnStrike) {
                    try {
                        Location tLoc = p.getLocation();
                        caster.teleport(tLoc);
                    } catch (Exception e) {
                        logger.warning("瞬移雷击: 瞬移失败 → " + e.getMessage());
                    }
                }

                for (int i = 0; i < strikeCount; i++) {
                    double offsetX = (Math.random() - 0.5) * 2.0;
                    double offsetZ = (Math.random() - 0.5) * 2.0;
                    org.bukkit.Location loc = p.getLocation().clone().add(offsetX, 0, offsetZ);
                    p.getWorld().strikeLightningEffect(loc);
                    p.damage(damage, caster);

                    // 雷电召唤：在雷击点召唤配置的怪物
                    if (summonOnStrike && monsterIdOnStrike != null && !monsterIdOnStrike.isEmpty()) {
                        try {
                            for (int s = 0; s < summonEach; s++) {
                                Location spawnLoc = loc.clone().add((Math.random() - 0.5) * 1.5, 0, (Math.random() - 0.5) * 1.5);
                                boolean ok = spawnByMonsterId(spawnLoc, monsterIdOnStrike);
                                if (!ok) {
                                    // 回退到默认根据技能名推断
                                    EntityType entityType = extensions.getSummonEntityType(skillName);
                                    caster.getWorld().spawnEntity(spawnLoc, entityType);
                                }
                            }
                        } catch (Exception ex) {
                            logger.warning("雷电召唤: 召唤失败 → " + ex.getMessage());
                        }
                    }
                }
            };

            if (lower.contains("global")) {
                boolean targeted = false;
                // 若是游戏内实体，则仅对同一游戏中的玩家落雷
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    String gameName = null;
                    try {
                        if (caster.hasMetadata("gameSession")) {
                            gameName = caster.getMetadata("gameSession").get(0).asString();
                        } else if (caster.hasMetadata("gameName")) {
                            gameName = caster.getMetadata("gameName").get(0).asString();
                        }
                    } catch (Exception ignored) {}

                    if (gameName != null && !gameName.isEmpty()) {
                        for (Player p : Bukkit.getOnlinePlayers()) {
                            if (!p.isDead() && dz.getGameSessionManager().isPlayerInGame(p, gameName)) {
                                strike.accept(p);
                                targeted = true;
                            }
                        }
                    }
                }
                // 否则（例如 /czm 生成）按原逻辑攻击全服在线玩家
                if (!targeted) {
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        if (!p.isDead()) {
                            strike.accept(p);
                        }
                    }
                }
            } else {
                Player target = findNearestPlayer(caster, range);
                if (target != null) {
                    strike.accept(target);
                }
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 投射物类技能：发射箭/雪球/火焰弹等
     */
    private Integer handleProjectile(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("投射物技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        String skillName = cfg.getSkillName();

        // 支持多种参数名
        int interval = toInt(cfg.getParameter("attack_interval",
                      cfg.getParameter("shoot_interval",
                      cfg.getParameter("arrow_interval",
                      cfg.getParameter("flame_interval",
                      cfg.getParameter("interval", 60))))));

        double range = toDouble(cfg.getParameter("ball_range",
                       cfg.getParameter("flame_range",
                       cfg.getParameter("attack_range",
                       cfg.getParameter("shoot_range",
                       cfg.getParameter("arrow_range",
                       cfg.getParameter("range", 15.0)))))));

        double damage = toDouble(cfg.getParameter("ball_damage",
                        cfg.getParameter("flame_damage",
                        cfg.getParameter("arrow_damage",
                        cfg.getParameter("projectile_damage",
                        cfg.getParameter("damage", 5.0))))));

        int count = toInt(cfg.getParameter("arrow_count",
                   cfg.getParameter("projectile_count",
                   cfg.getParameter("count", 1))));

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, count=%d (从%d个参数中读取)",
                   skillName, interval, range, damage, count, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                // 根据技能类型发射不同投射物
                for (int i = 0; i < count; i++) {
                    extensions.launchProjectile(caster, target, skillName);
                }
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 全局伤害：对全体在线玩家周期性造成伤害
     */
    private Integer handleGlobalDamage(LivingEntity caster, SkillConfig cfg) {
        Map<String, Object> all = cfg.getParameters();
        double damage = toDouble(cfg.getParameter("global_damage", 10.0));
        int interval = Math.max(1, toInt(cfg.getParameter("damage_interval", cfg.getParameter("interval", 400))));
        boolean sound = Boolean.TRUE.equals(cfg.getParameter("sound_enabled")) ||
                         "true".equalsIgnoreCase(String.valueOf(cfg.getParameter("sound_enabled")));
        logger.info(String.format("挂载技能[%s-全局伤害]: damage=%.2f, interval=%d (参数=%d)", cfg.getSkillName(), damage, interval, all.size()));
        BukkitTask task2 = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!extensions.isAlive(caster)) return;
            for (Player p : caster.getWorld().getPlayers()) {
                if (!p.isOnline() || p.isDead()) continue;
                p.damage(damage, caster);
                if (sound) {
                    p.getWorld().playSound(p.getLocation(), org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);
                }
            }
        }, interval, interval);
        return task2.getTaskId();
    }

    /**
     * 爆炸类技能：自爆或范围爆炸
     */
    private Integer handleExplosion(LivingEntity caster, SkillConfig cfg) {
        String skillName = cfg.getSkillName();
        // 读取参数：爆炸对范围内玩家生效（不对自身）
        int interval = toInt(cfg.getParameter("explosion_interval", cfg.getParameter("interval", 200)));
        double range = toDouble(cfg.getParameter("explosion_range", cfg.getParameter("range", 6.0)));
        double power = toDouble(cfg.getParameter("explosion_power", cfg.getParameter("power", 3.0)));
        boolean fire = Boolean.TRUE.equals(cfg.getParameter("explosion_fire")) || "true".equalsIgnoreCase(String.valueOf(cfg.getParameter("explosion_fire")));
        boolean damageBlocks = Boolean.TRUE.equals(cfg.getParameter("explosion_damage_blocks")) || "true".equalsIgnoreCase(String.valueOf(cfg.getParameter("explosion_damage_blocks")));

        logger.info(String.format("挂载技能[%s-爆炸]: interval=%d, range=%.2f, power=%.2f, fire=%s, breakBlocks=%s",
                skillName, interval, range, power, fire, damageBlocks));

        if (skillName.contains("death")) {
            // 死亡爆炸：注册参数到元数据，死亡时由监听器读取
            caster.setMetadata("idz_death_explosion", new FixedMetadataValue(plugin, power));
            caster.setMetadata("idz_death_explosion_fire", new FixedMetadataValue(plugin, fire));
            caster.setMetadata("idz_death_explosion_break", new FixedMetadataValue(plugin, damageBlocks));
            return null;
        }

        // 定时爆炸：对范围内玩家进行爆炸效果
        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            World world = caster.getWorld();
            Location center = caster.getLocation();
            for (Player p : world.getPlayers()) {
                if (!p.isOnline() || p.isDead()) continue;
                if (p.getLocation().distanceSquared(center) <= range * range) {
                    Location loc = p.getLocation();
                    world.createExplosion(loc, (float) power, fire, damageBlocks);
                }
            }
        }, Math.max(1, interval), Math.max(1, interval));
        return task.getTaskId();
    }

    /**
     * 召唤类技能：召唤僵尸/卫兵等
     */
    private Integer handleSummon(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String rawName = cfg.getSkillName();
        String normalized = normalizeSkillName(rawName);
        logger.info("召唤技能参数读取 - " + rawName + ": " + allParams);

        // 死亡召唤：不启动定时任务，改为在死亡事件中触发
        if (normalized.contains("death_summon")) {
            logger.info("挂载技能[" + rawName + "]: 注册为死亡触发型召唤，等待实体死亡时触发");
            return null;
        }

        // 支持多种参数名（存活期间的周期召唤）
        int interval = toInt(cfg.getParameter("summon_interval",
                      cfg.getParameter("spawn_interval",
                      cfg.getParameter("detect_interval",
                      cfg.getParameter("interval", 300)))));

        int count = toInt(cfg.getParameter("summon_count",
                   cfg.getParameter("spawn_count",
                   cfg.getParameter("zombie_count",
                   cfg.getParameter("count", 2)))));

        double range = toDouble(cfg.getParameter("summon_range",
                       cfg.getParameter("spawn_range",
                       cfg.getParameter("detect_range",
                       cfg.getParameter("range", 5.0)))));

        // 新增：可选的怪物ID（沿用死亡召唤的选择GUI）
        // 读取一次即可，供定时任务闭包使用
        final String monsterId = (cfg.getParameter("monster_id") != null)
                ? String.valueOf(cfg.getParameter("monster_id")) : null;

        // 连续/随机召唤额外参数
        final int maxSummons = Math.max(0, toInt(cfg.getParameter("max_summons", 0))); // 0=不限制
        final String monsterPool = (cfg.getParameter("monster_pool") != null) ? String.valueOf(cfg.getParameter("monster_pool")) : null;
        final double speedMul = Math.max(0.0, toDouble(cfg.getParameter("speed_multiplier", 0.0))); // 0=不修改

        logger.info(String.format("挂载技能[%s]: interval=%d, count=%d, range=%.2f, monsterId=%s, max=%d, pool=%s, spd=%.2f (从%d个参数中读取)",
                   rawName, interval, count, range, monsterId, maxSummons, monsterPool, speedMul, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;

            // 检测召唤：仅当范围内存在玩家时才进行召唤
            if (normalized.contains("detect_summon")) {
                Player nearby = findNearestPlayer(caster, range);
                if (nearby == null) return; // 范围内无玩家 → 不召唤
            }

            // 持续召唤：在召唤前检查数量上限（通过 idz_owner 标记归属）
            if (normalized.contains("continuous_summon") || normalized.contains("random_summon")) {
                if (maxSummons > 0) {
                    int owned = 0;
                    for (Entity e : caster.getWorld().getEntities()) {
                        if (!(e instanceof LivingEntity)) continue;
                        if (!e.hasMetadata("idz_owner")) continue;
                        try {
                            java.util.UUID owner = java.util.UUID.fromString(String.valueOf(e.getMetadata("idz_owner").get(0).value()));
                            if (owner.equals(caster.getUniqueId())) owned++;
                        } catch (Exception ignored) {}
                    }
                    if (owned >= maxSummons) return; // 达到上限，本次不召唤
                }
            }

            for (int i = 0; i < count; i++) {
                Location spawnLoc = caster.getLocation().add(
                    (Math.random() - 0.5) * range * 2,
                    0,
                    (Math.random() - 0.5) * range * 2
                );

                boolean spawned = false;
                try {
                    // 若为随机召唤且提供了候选池，则随机挑选一个ID作为本次召唤对象
                    String chosenId = monsterId;
                    if (normalized.contains("random_summon") && monsterPool != null && !monsterPool.trim().isEmpty()) {
                        String[] arr = monsterPool.split(",");
                        if (arr.length > 0) {
                            String pick = arr[(int) (Math.random() * arr.length)].trim();
                            if (!pick.isEmpty()) chosenId = pick;
                        }
                    }
                    // 修复：召唤武装僵尸应默认生成真正的ID8
                    if ((normalized.contains("summon_armed_zombie") || rawName.contains("武装")) && (chosenId == null || chosenId.isEmpty())) {
                        chosenId = "id8";
                    }

                    if (chosenId != null && !chosenId.isEmpty()) {
                        // 使用现有系统生成 IDZ/ID/IDC 实体
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        org.Ver_zhzh.deathZombieV4.utils.ZombieHelper helper = dz.getZombieHelper();
                        String mid = chosenId.toLowerCase();
                        if (mid.startsWith("idz")) {
                            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzMgr = dz.getIDZMonsterManager();
                            if (idzMgr != null) {
                                idzMgr.spawnMonster(spawnLoc, chosenId);
                                spawned = true;
                            }
                        } else if (mid.startsWith("idc")) {
                            if (helper != null) {
                                spawned = helper.spawnOtherEntityAtLocation(spawnLoc, chosenId);
                            }
                        } else if (mid.startsWith("id")) {
                            if (helper != null) {
                                org.bukkit.entity.Zombie z = helper.spawnCustomZombie(spawnLoc, chosenId);
                                spawned = (z != null);
                            }
                        } else if ("twin_spawn_zombie".equalsIgnoreCase(chosenId)) {
                            // 特殊：速度僵尸（ID6衍生）
                            org.bukkit.entity.Zombie newZombie = (org.bukkit.entity.Zombie) caster.getWorld().spawnEntity(spawnLoc, org.bukkit.entity.EntityType.ZOMBIE);
                            newZombie.setMetadata("twin_spawn", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                            newZombie.setMetadata("not_round_zombie", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                            newZombie.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                            newZombie.setMetadata("customZombieType", new org.bukkit.metadata.FixedMetadataValue(plugin, "twin_spawn_zombie"));
                            spawned = true;
                        }
                    }
                } catch (Exception e) {
                    logger.warning("定时召唤生成自定义实体失败: " + e.getMessage());
                }

                if (!spawned) {
                    // 回退：使用原版实体类型推断
                    EntityType entityType = extensions.getSummonEntityType(rawName);
                    LivingEntity spawnedLe = (LivingEntity) caster.getWorld().spawnEntity(spawnLoc, entityType);
                    // 统一赋予归属与速度倍率
                    try {
                        spawnedLe.setMetadata("idz_owner", new FixedMetadataValue(plugin, caster.getUniqueId().toString()));
                        if (speedMul > 0.0) {
                            spawnedLe.addPotionEffect(new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.SPEED, 20 * 60 * 60, Math.max(0, (int) Math.round(speedMul - 1.0))));
                        }
                        // 如果是“召唤武装僵尸”，并且回退生成了原版僵尸，则根据参数为其装备
                        if (entityType == EntityType.ZOMBIE && (normalized.contains("summon_armed_zombie") || rawName.contains("武装"))) {
                            int equipLevel = Math.max(1, Math.min(5, toInt(cfg.getParameter("zombie_equipment", 0))));
                            org.bukkit.inventory.EntityEquipment eq = ((LivingEntity) spawnedLe).getEquipment();
                            if (eq != null) {
                                switch (equipLevel) {
                                    case 1:
                                        eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.STONE_SWORD));
                                        eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.LEATHER_HELMET));
                                        eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.LEATHER_CHESTPLATE));
                                        eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.LEATHER_LEGGINGS));
                                        eq.setBoots(new org.bukkit.inventory.ItemStack(Material.LEATHER_BOOTS));
                                        break;
                                    case 2:
                                        eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.IRON_SWORD));
                                        eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.CHAINMAIL_HELMET));
                                        eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.CHAINMAIL_CHESTPLATE));
                                        eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.CHAINMAIL_LEGGINGS));
                                        eq.setBoots(new org.bukkit.inventory.ItemStack(Material.CHAINMAIL_BOOTS));
                                        break;
                                    case 3:
                                        eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.IRON_SWORD));
                                        eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.IRON_HELMET));
                                        eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.IRON_CHESTPLATE));
                                        eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.IRON_LEGGINGS));
                                        eq.setBoots(new org.bukkit.inventory.ItemStack(Material.IRON_BOOTS));
                                        break;
                                    case 4:
                                        eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.DIAMOND_SWORD));
                                        eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.IRON_HELMET));
                                        eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.IRON_CHESTPLATE));
                                        eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.IRON_LEGGINGS));
                                        eq.setBoots(new org.bukkit.inventory.ItemStack(Material.IRON_BOOTS));
                                        break;
                                    case 5:
                                    default:
                                        eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.DIAMOND_SWORD));
                                        eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.DIAMOND_HELMET));
                                        eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.DIAMOND_CHESTPLATE));
                                        eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.DIAMOND_LEGGINGS));
                                        eq.setBoots(new org.bukkit.inventory.ItemStack(Material.DIAMOND_BOOTS));
                                        break;
                                }
                                // 降低掉落概率
                                try {
                                    eq.setItemInMainHandDropChance(0.05f);
                                    eq.setHelmetDropChance(0.02f);
                                    eq.setChestplateDropChance(0.02f);
                                    eq.setLeggingsDropChance(0.02f);
                                    eq.setBootsDropChance(0.02f);
                                } catch (Throwable ignored) {}
                            }
                        }

                        // 如果是"召唤强化僵尸"，并且回退生成了原版僵尸，则为其装备下界合金装备（IDC12风格）
                        if (entityType == EntityType.ZOMBIE && (normalized.contains("summon_enhanced_zombie") || rawName.contains("强化") || rawName.contains("強化"))) {
                            org.bukkit.inventory.EntityEquipment eq = ((LivingEntity) spawnedLe).getEquipment();
                            if (eq != null) {
                                // IDC12风格：全套下界合金装备
                                eq.setItemInMainHand(new org.bukkit.inventory.ItemStack(Material.NETHERITE_SWORD));
                                eq.setHelmet(new org.bukkit.inventory.ItemStack(Material.NETHERITE_HELMET));
                                eq.setChestplate(new org.bukkit.inventory.ItemStack(Material.NETHERITE_CHESTPLATE));
                                eq.setLeggings(new org.bukkit.inventory.ItemStack(Material.NETHERITE_LEGGINGS));
                                eq.setBoots(new org.bukkit.inventory.ItemStack(Material.NETHERITE_BOOTS));
                                // 降低掉落概率
                                try {
                                    eq.setItemInMainHandDropChance(0.01f);
                                    eq.setHelmetDropChance(0.01f);
                                    eq.setChestplateDropChance(0.01f);
                                    eq.setLeggingsDropChance(0.01f);
                                    eq.setBootsDropChance(0.01f);
                                } catch (Throwable ignored) {}
                                // 设置更高的生命值（IDC12风格强化）
                                spawnedLe.setMaxHealth(40.0);
                                spawnedLe.setHealth(40.0);
                            }
                        }
                    } catch (Exception ignored) {}

                } else {
                    // 自定义生成的实体：尝试打上归属并应用速度倍率
                    try {
                        // 最近生成的实体可能无法直接引用，只能在范围内寻找最近新生物打标
                        for (Entity near : caster.getWorld().getNearbyEntities(spawnLoc, 1.5, 1.5, 1.5)) {
                            if (!(near instanceof LivingEntity)) continue;
                            LivingEntity le = (LivingEntity) near;
                            if (!le.hasMetadata("idz_owner")) {
                                le.setMetadata("idz_owner", new FixedMetadataValue(plugin, caster.getUniqueId().toString()));
                                if (speedMul > 0.0) {
                                    le.addPotionEffect(new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.SPEED, 20 * 60 * 60, Math.max(0, (int) Math.round(speedMul - 1.0))));
                                }
                                break;
                            }
                        }
                    } catch (Exception ignored) {}
                }
            }
        }, interval, interval);
        return task.getTaskId();
    }


    /**
     * 通用：根据 monster_id 派发生成 IDZ/ID/IDC 自定义实体
     * 返回 true 表示成功，false 表示需要回退
     */
    private boolean spawnByMonsterId(Location spawnLoc, String monsterId) {
        try {
            if (monsterId == null || monsterId.isEmpty()) return false;
            String mid = monsterId.toLowerCase();
            if (!(plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4)) return false;
            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
            org.Ver_zhzh.deathZombieV4.utils.ZombieHelper helper = dz.getZombieHelper();

            if (mid.startsWith("idz")) {
                org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzMgr = dz.getIDZMonsterManager();
                if (idzMgr != null) {
                    idzMgr.spawnMonster(spawnLoc, monsterId);
                    return true;
                }
            } else if (mid.startsWith("idc")) {
                if (helper != null) {
                    return helper.spawnOtherEntityAtLocation(spawnLoc, monsterId);
                }
            } else if (mid.startsWith("id")) {
                if (helper != null) {
                    org.bukkit.entity.Zombie z = helper.spawnCustomZombie(spawnLoc, monsterId);
                    return z != null;
                }
            } else if ("twin_spawn_zombie".equalsIgnoreCase(monsterId)) {
                org.bukkit.entity.Zombie newZombie = (org.bukkit.entity.Zombie) spawnLoc.getWorld().spawnEntity(spawnLoc, org.bukkit.entity.EntityType.ZOMBIE);
                newZombie.setMetadata("twin_spawn", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                newZombie.setMetadata("not_round_zombie", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                newZombie.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                newZombie.setMetadata("customZombieType", new org.bukkit.metadata.FixedMetadataValue(plugin, "twin_spawn_zombie"));
                return true;
            }
        } catch (Exception e) {
            logger.warning("spawnByMonsterId 失败: " + e.getMessage());
        }
        return false;
    }

    // =========== 小工具 ===========

    private boolean isAlive(Entity e) {
        return e != null && e.isValid() && !e.isDead();
    }

    private Player findNearestPlayer(LivingEntity from, double range) {
        World w = from.getWorld();
        Location c = from.getLocation();
        Player best = null;
        double bestD2 = range * range;
        for (Player p : w.getPlayers()) {
            if (!p.isOnline() || p.isDead()) continue;
            double d2 = p.getLocation().distanceSquared(c);
            if (d2 <= bestD2) {
                bestD2 = d2;
                best = p;
            }
        }
        return best;
    }

    private int toInt(Object v) {
        if (v instanceof Number) return ((Number) v).intValue();
        try { return Integer.parseInt(String.valueOf(v)); } catch (Exception e) { return 0; }
    }

    private double toDouble(Object v) {
        if (v instanceof Number) return ((Number) v).doubleValue();
        try { return Double.parseDouble(String.valueOf(v)); } catch (Exception e) { return 0.0; }
    }

    private boolean toBoolean(Object v) {
        if (v instanceof Boolean) return (Boolean) v;
        return Boolean.parseBoolean(String.valueOf(v));
    }

    // =========== 末影水晶保护监听器 ===========

    /**
     * 防止所有IDZ/IDC系列末影水晶受到伤害
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEnderCrystalDamage(EntityDamageEvent event) {
        if (event.getEntity() instanceof EnderCrystal) {
            EnderCrystal crystal = (EnderCrystal) event.getEntity();

            // 检查是否是需要保护的末影水晶
            if (isProtectedCrystal(crystal)) {
                event.setCancelled(true); // 取消伤害事件
                String crystalType = getProtectedCrystalType(crystal);
                logger.info("阻止" + crystalType + "末影水晶受到伤害，保护方块不被破坏");
            }
        }
    }

    /**
     * 防止所有IDZ/IDC系列末影水晶爆炸破坏方块
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEnderCrystalExplode(EntityExplodeEvent event) {
        if (event.getEntity() instanceof EnderCrystal) {
            EnderCrystal crystal = (EnderCrystal) event.getEntity();

            // 检查是否是需要保护的末影水晶
            if (isProtectedCrystal(crystal)) {
                event.setCancelled(true); // 取消爆炸事件
                String crystalType = getProtectedCrystalType(crystal);
                logger.info("阻止" + crystalType + "末影水晶爆炸，保护方块不被破坏");
            }
        }
    }

    /**
     * 检查是否是需要保护的末影水晶
     */
    private boolean isProtectedCrystal(EnderCrystal crystal) {
        // IDC22系列水晶
        if (crystal.hasMetadata("idc22_crystal") || crystal.hasMetadata("no_explosion")) {
            return true;
        }

        // MutationKing系列水晶
        if (crystal.hasMetadata("mutationKingCrystal") ||
            crystal.hasMetadata("mutationKingOrbitalCrystal")) {
            return true;
        }

        // UserCustomEntity系列水晶
        if (crystal.hasMetadata("userCustomEntityCrystal")) {
            return true;
        }

        // IDZ技能系统生成的水晶
        if (crystal.hasMetadata("idz_crystal") ||
            crystal.hasMetadata("idz_skill_crystal")) {
            return true;
        }

        // 通用保护标记
        if (crystal.hasMetadata("protected_crystal") ||
            crystal.hasMetadata("no_block_damage")) {
            return true;
        }

        return false;
    }

    /**
     * 获取受保护水晶的类型描述
     */
    private String getProtectedCrystalType(EnderCrystal crystal) {
        if (crystal.hasMetadata("idc22_crystal")) {
            return "IDC22";
        } else if (crystal.hasMetadata("mutationKingCrystal")) {
            return "MutationKing";
        } else if (crystal.hasMetadata("mutationKingOrbitalCrystal")) {
            return "MutationKing环绕";
        } else if (crystal.hasMetadata("userCustomEntityCrystal")) {
            return "UserCustomEntity";
        } else if (crystal.hasMetadata("idz_crystal")) {
            return "IDZ技能";
        } else if (crystal.hasMetadata("idz_skill_crystal")) {
            return "IDZ技能系统";
        } else {
            return "受保护";
        }
    }
}



