package org.Ver_zhzh.customZombie.UserMaker.managers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.data.PlayerSkullData;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * 玩家头颅管理器
 * 负责玩家头颅的存储、读取和创建
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PlayerSkullManager {
    
    private final Plugin plugin;
    private final Logger logger;
    private final File dataFile;
    private final File backupFile;
    private final Gson gson;
    
    private List<PlayerSkullData> playerSkulls;
    
    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public PlayerSkullManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();

        // 创建player_skulls文件夹
        File playerSkullsFolder = new File(plugin.getDataFolder(), "player_skulls");
        this.dataFile = new File(playerSkullsFolder, "player_skulls.json");
        this.backupFile = new File(playerSkullsFolder, "player_skulls_backup.json");

        this.gson = new GsonBuilder().setPrettyPrinting().create();
        this.playerSkulls = new ArrayList<>();

        // 确保数据文件夹存在
        if (!plugin.getDataFolder().exists()) {
            plugin.getDataFolder().mkdirs();
        }

        // 确保player_skulls文件夹存在
        if (!playerSkullsFolder.exists()) {
            playerSkullsFolder.mkdirs();
            logger.info("创建玩家头颅数据文件夹: " + playerSkullsFolder.getAbsolutePath());
        }

        // 加载数据
        loadPlayerSkulls();

        logger.info("玩家头颅管理器初始化完成，已加载 " + playerSkulls.size() + " 个玩家头颅");
    }
    
    /**
     * 加载玩家头颅数据
     */
    private void loadPlayerSkulls() {
        if (!dataFile.exists()) {
            logger.info("玩家头颅数据文件不存在，创建新文件");
            savePlayerSkulls();
            return;
        }
        
        try (FileReader reader = new FileReader(dataFile)) {
            Type listType = new TypeToken<List<PlayerSkullData>>(){}.getType();
            List<PlayerSkullData> loadedSkulls = gson.fromJson(reader, listType);
            
            if (loadedSkulls != null) {
                playerSkulls = loadedSkulls;
                logger.info("成功加载 " + playerSkulls.size() + " 个玩家头颅数据");
            } else {
                playerSkulls = new ArrayList<>();
                logger.info("玩家头颅数据文件为空，初始化空列表");
            }
            
        } catch (Exception e) {
            logger.severe("加载玩家头颅数据时出错: " + e.getMessage());
            e.printStackTrace();
            
            // 尝试从备份恢复
            if (restoreFromBackup()) {
                logger.info("已从备份文件恢复玩家头颅数据");
            } else {
                playerSkulls = new ArrayList<>();
                logger.warning("无法恢复数据，使用空列表");
            }
        }
    }
    
    /**
     * 保存玩家头颅数据
     */
    private void savePlayerSkulls() {
        try {
            // 创建备份
            if (dataFile.exists()) {
                Files.copy(dataFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            }
            
            // 保存数据
            try (FileWriter writer = new FileWriter(dataFile)) {
                gson.toJson(playerSkulls, writer);
                logger.info("成功保存 " + playerSkulls.size() + " 个玩家头颅数据");
            }
            
        } catch (Exception e) {
            logger.severe("保存玩家头颅数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从备份恢复数据
     */
    private boolean restoreFromBackup() {
        if (!backupFile.exists()) {
            return false;
        }
        
        try (FileReader reader = new FileReader(backupFile)) {
            Type listType = new TypeToken<List<PlayerSkullData>>(){}.getType();
            List<PlayerSkullData> backupSkulls = gson.fromJson(reader, listType);
            
            if (backupSkulls != null) {
                playerSkulls = backupSkulls;
                return true;
            }
            
        } catch (Exception e) {
            logger.warning("恢复备份数据时出错: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 添加玩家头颅
     * 
     * @param playerName 玩家名称
     * @return 是否添加成功
     */
    public boolean addPlayerSkull(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }
        
        playerName = playerName.trim();
        
        // 检查是否已存在
        if (hasPlayerSkull(playerName)) {
            return false;
        }
        
        // 添加新头颅
        PlayerSkullData skullData = new PlayerSkullData(playerName);
        playerSkulls.add(skullData);
        
        // 保存数据
        savePlayerSkulls();
        
        logger.info("成功添加玩家头颅: " + playerName);
        return true;
    }
    
    /**
     * 检查是否已存在指定玩家的头颅
     * 
     * @param playerName 玩家名称
     * @return 是否存在
     */
    public boolean hasPlayerSkull(String playerName) {
        if (playerName == null) return false;
        
        return playerSkulls.stream()
                .anyMatch(skull -> skull.getPlayerName().equalsIgnoreCase(playerName.trim()));
    }
    
    /**
     * 获取所有玩家头颅数据
     * 
     * @return 玩家头颅列表
     */
    public List<PlayerSkullData> getAllPlayerSkulls() {
        return new ArrayList<>(playerSkulls);
    }
    
    /**
     * 获取玩家头颅数量
     * 
     * @return 头颅数量
     */
    public int getPlayerSkullCount() {
        return playerSkulls.size();
    }
    
    /**
     * 创建玩家头颅物品
     * 
     * @param playerName 玩家名称
     * @return 玩家头颅物品，如果失败返回null
     */
    public ItemStack createPlayerSkullItem(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return null;
        }
        
        try {
            ItemStack skull = new ItemStack(Material.PLAYER_HEAD);
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();
            
            if (skullMeta != null) {
                // 设置玩家头颅
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName.trim());
                skullMeta.setOwningPlayer(offlinePlayer);
                
                // 设置显示名称和描述
                skullMeta.setDisplayName("§e" + playerName + "的头颅");
                List<String> lore = new ArrayList<>();
                lore.add("§7玩家: §f" + playerName);
                lore.add("§7类型: §a自定义玩家头颅");
                lore.add("§7点击选择此头颅");
                skullMeta.setLore(lore);
                
                skull.setItemMeta(skullMeta);
                return skull;
            }
            
        } catch (Exception e) {
            logger.warning("创建玩家头颅时出错: " + playerName + " - " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 移除玩家头颅
     * 
     * @param playerName 玩家名称
     * @return 是否移除成功
     */
    public boolean removePlayerSkull(String playerName) {
        if (playerName == null) return false;
        
        boolean removed = playerSkulls.removeIf(skull -> 
            skull.getPlayerName().equalsIgnoreCase(playerName.trim()));
        
        if (removed) {
            savePlayerSkulls();
            logger.info("成功移除玩家头颅: " + playerName);
        }
        
        return removed;
    }
    
    /**
     * 清空所有玩家头颅
     */
    public void clearAllPlayerSkulls() {
        playerSkulls.clear();
        savePlayerSkulls();
        logger.info("已清空所有玩家头颅数据");
    }
}
