package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;
import org.Ver_zhzh.customZombie.UserMaker.SkillConfig;

import java.util.*;
import java.util.logging.Logger;

/**
 * 技能选择器GUI
 * 用于选择和配置IDZ怪物的技能
 *
 * <AUTHOR>
 * @version 1.0
 */
public class SkillSelectorGUI implements Listener {

    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager idzManager;

    // 技能配置编辑器
    private SkillConfigGUI skillConfigGUI;

    private static final String GUI_TITLE = "§6技能选择器";
    private static final String CATEGORY_GUI_TITLE = "§6技能分类";

    // 当前编辑状态追踪
    private final Map<Player, String> editingMonster;
    private final Map<Player, Integer> currentPage;
    private final Map<Player, String> currentCategory;

    // 技能分类和数据
    private final Map<String, List<SkillTemplate>> skillCategories;
    private final Map<String, Material> categoryIcons;

    // 槽位常量
    private static final int ITEMS_PER_PAGE = 28; // 7x4的技能显示区域
    private static final int PREV_PAGE_SLOT = 45;
    private static final int NEXT_PAGE_SLOT = 53;
    private static final int BACK_SLOT = 49;
    private static final int CATEGORY_SLOT = 4;

    /**
     * 技能模板内部类
     */
    public static class SkillTemplate {
        public final String id;
        public final String name;
        public final String description;
        public final String source;
        public final Material icon;
        public final List<String> lore;

        public SkillTemplate(String id, String name, String description, String source, Material icon) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.source = source;
            this.icon = icon;
            this.lore = Arrays.asList(
                "§7" + description,
                "§7来源: §e" + source,
                "",
                "§e左键点击添加技能",
                "§c右键点击移除技能"
            );
        }
    }

    /**
     * 构造函数
     */
    public SkillSelectorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.idzManager = idzManager;
        this.editingMonster = new HashMap<>();
        this.currentPage = new HashMap<>();
        this.currentCategory = new HashMap<>();
        this.skillCategories = new HashMap<>();
        this.categoryIcons = new HashMap<>();

        // 初始化技能库
        initializeSkillLibrary();

        // 初始化技能配置编辑器
        this.skillConfigGUI = new SkillConfigGUI(plugin, idzManager);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        logger.info("技能选择器GUI初始化完成，加载了 " + getTotalSkillCount() + " 个技能 (ID系列:" + skillCategories.get("id_series").size() + "个, IDC系列:" + skillCategories.get("idc_series").size() + "个)");
    }

    /**
     * 初始化技能库 - 完整的85种技能
     */
    private void initializeSkillLibrary() {
        // ID系列技能 (35个技能)
        List<SkillTemplate> idSkills = Arrays.asList(
            // ID5 剧毒僵尸
            new SkillTemplate("id5_poison_attack", "剧毒攻击", "攻击时使玩家获得剧毒buff", "ID5-剧毒僵尸", Material.SPIDER_EYE),

            // ID6 双生僵尸
            new SkillTemplate("id6_death_summon", "死亡召唤", "死亡时召唤X个僵尸", "ID6-双生僵尸", Material.ZOMBIE_HEAD),

            // ID7 骷髅僵尸
            new SkillTemplate("id7_arrow_attack", "箭矢攻击", "每X秒发射X箭矢攻击", "ID7-骷髅僵尸", Material.ARROW),

            // ID10 法师僵尸
            new SkillTemplate("id10_timed_summon", "定时召唤", "每X秒召唤X个僵尸", "ID10-法师僵尸", Material.SPAWNER),

            // ID11 自爆僵尸
            new SkillTemplate("id11_explosion_death", "自爆攻击", "在玩家身边时自动进入爆炸时间，3秒后死亡并且产生爆炸", "ID11-自爆僵尸", Material.TNT),

            // ID12 毒箭僵尸
            new SkillTemplate("id12_poison_arrow", "毒箭攻击", "每X秒发射X带有XXXbuff的箭矢攻击", "ID12-毒箭僵尸", Material.TIPPED_ARROW),

            // ID13 电击僵尸
            new SkillTemplate("id13_electric_attack", "电击攻击", "每X秒对玩家造成电击（粒子特效+直接伤害）", "ID13-电击僵尸", Material.LIGHTNING_ROD),

            // ID14 冰冻僵尸
            new SkillTemplate("id14_freeze_attack", "冰冻攻击", "每X秒对玩家造成冰冻（粒子特效+buff效果）", "ID14-冰冻僵尸", Material.ICE),

            // ID15 暗影僵尸
            new SkillTemplate("id15_teleport_attack", "传送攻击", "每X秒传送到玩家身边", "ID15-暗影僵尸", Material.ENDER_PEARL),

            // ID17 雷霆僵尸
            new SkillTemplate("id17_thunder_attack", "雷霆攻击", "每X秒对玩家造成X次闪电攻击", "ID17-雷霆僵尸", Material.TRIDENT),

            // ID18 变异科学家 - 拆分为独立技能
            new SkillTemplate("damage_summon", "受伤召唤", "每受到50点伤害召唤X僵尸一次", "ID18-变异科学家", Material.REDSTONE),
            new SkillTemplate("area_debuff", "范围debuff", "玩家在该僵尸10*10范围内获得反胃，减速效果", "ID18-变异科学家", Material.POISONOUS_POTATO),
            new SkillTemplate("global_damage", "全局伤害", "每20秒对所有玩家直接造成10点伤害，并播放爆炸音效", "ID18-变异科学家", Material.TNT_MINECART),

            // ID19 变异法师 - 拆分为独立技能
            new SkillTemplate("area_lightning", "范围闪电", "每X秒使在该僵尸6*6范围的玩家被闪电击中", "ID19-变异法师", Material.LIGHTNING_ROD),
            new SkillTemplate("area_buff", "范围buff", "玩家在该僵尸10*10范围内获得buff效果", "ID19-变异法师", Material.BEACON),
            new SkillTemplate("detect_summon", "检测召唤", "每X秒检测附近5*5范围是否有玩家，如果有，就生成X个僵尸", "ID19-变异法师", Material.OBSERVER),

            // ID20 气球僵尸 - 拆分为独立技能
            new SkillTemplate("flight_ability", "飞行效果", "拥有飞行效果，缓慢上升", "ID20-气球僵尸", Material.ELYTRA),
            new SkillTemplate("death_particles", "死亡粒子", "死亡后释放五彩气球粒子，随机弹跳", "ID20-气球僵尸", Material.FIREWORK_ROCKET),

            // ID21 迷雾僵尸 - 拆分为独立技能
            new SkillTemplate("fog_generation", "迷雾生成", "定期在周围生成迷雾，降低附近玩家的视野和移动速度", "ID21-迷雾僵尸", Material.GRAY_DYE),
            new SkillTemplate("death_fog", "死亡雾气", "死亡后在周围生成大量雾气粒子", "ID21-迷雾僵尸", Material.LIGHT_GRAY_DYE),

            // ID22 变异雷霆僵尸 - 拆分为独立技能
            new SkillTemplate("global_lightning", "全局雷电", "每X秒释放一次雷电，对所以玩家造成伤害并播放雷电音效和雷电爆炸粒子", "ID22-变异雷霆僵尸", Material.LIGHTNING_ROD),
            new SkillTemplate("teleport_lightning", "瞬移雷击", "瞬移到玩家身边并释放雷电攻击", "ID22-变异雷霆僵尸", Material.END_ROD),
            new SkillTemplate("lightning_summon", "雷电召唤", "召唤雷电生物", "ID22-变异雷霆僵尸", Material.SPAWNER),
            new SkillTemplate("time_control", "时间控制", "操控时间流速", "ID22-变异雷霆僵尸", Material.CLOCK),
            new SkillTemplate("freeze_ability", "冻结能力", "冻结玩家", "ID22-变异雷霆僵尸", Material.PACKED_ICE),

            // ID23 变异战士僵尸 - 拆分为独立技能
            new SkillTemplate("attack_buff", "攻击buff", "攻击时获得力量效果", "ID23-变异战士僵尸", Material.BLAZE_POWDER),
            // new SkillTemplate("summon_warriors", "召唤战士", "召唤武装战士", "ID23-变异战士僵尸", Material.IRON_SWORD), // 重复于定时召唤，已移除
            new SkillTemplate("aura_buff", "光环buff", "周围敌对生物获得增益效果(显示范围)", "ID23-变异战士僵尸", Material.TOTEM_OF_UNDYING),
            new SkillTemplate("explosion_skill", "爆炸技能", "特殊爆炸攻击", "ID23-变异战士僵尸", Material.FIRE_CHARGE),

            // ID24 变异刺客僵尸 - 拆分为独立技能
            new SkillTemplate("stealth_attack", "隐身攻击", "隐身瞬移攻击", "ID24-变异刺客僵尸", Material.GLASS),
            new SkillTemplate("continuous_summon", "持续召唤", "持续召唤刺客", "ID24-变异刺客僵尸", Material.PHANTOM_MEMBRANE),
            new SkillTemplate("global_debuff", "全局debuff", "对所有玩家施加负面效果", "ID24-变异刺客僵尸", Material.WITHER_ROSE),

            // ID25 终极毁灭僵尸 - 拆分为独立技能
            new SkillTemplate("random_summon", "随机召唤", "随机召唤各种怪物", "ID25-终极毁灭僵尸", Material.SPAWNER),
            // 去重：受伤触发召唤 与 受伤召唤 重复，移除
            // new SkillTemplate("damage_triggered_summon", "受伤触发召唤", "受到伤害时召唤", "ID25-终极毁灭僵尸", Material.REDSTONE_BLOCK),
            new SkillTemplate("ranged_magic_attack", "远程魔法攻击", "远程魔法攻击", "ID25-终极毁灭僵尸", Material.BOW)
            // 去重：电击技能 与 电击攻击 重复，移除
            // new SkillTemplate("electric_skill", "电击技能", "强力电击攻击", "ID25-终极毁灭僵尸", Material.LIGHTNING_ROD),
            // 去重：与 攻击buff(attack_buff) 重复，移除 GUI 入口
            // new SkillTemplate("combat_buff", "战斗buff", "攻击时获得增益", "ID25-终极毁灭僵尸", Material.BLAZE_POWDER),
            // 去重：与 冻结能力(freeze_ability) 重复，移除 GUI 入口
            // new SkillTemplate("freeze_skill", "冻结技能", "冻结敌人", "ID25-终极毁灭僵尸", Material.BLUE_ICE),
            // 去重：与 电击攻击(electric_attack) 重复，移除 GUI 入口
            // new SkillTemplate("area_electric", "范围电流", "大范围电流攻击", "ID25-终极毁灭僵尸", Material.CONDUIT),
            // 去重：与 定时召唤(timed_summon) 重复，移除 GUI 入口
            // new SkillTemplate("special_summon", "特殊召唤", "召唤特殊生物", "ID25-终极毁灭僵尸", Material.DRAGON_EGG)
        );

        // IDC系列技能 (50个技能)
        List<SkillTemplate> idcSkills = Arrays.asList(
            // IDC1 变异僵尸01
            new SkillTemplate("idc1_poison_attack", "剧毒攻击", "每次攻击对玩家施加一个剧毒效果，持续X秒，并且对玩家造成X点伤害", "IDC1-变异僵尸01", Material.SPIDER_EYE),

            // IDC3 变异烈焰人
            new SkillTemplate("idc3_flame_attack", "烈焰攻击", "每X秒向玩家发射烈焰粒子和烈焰弹，玩家碰到烈焰粒子时会造成X点伤害，被烈焰弹炸到会造成X点伤害", "IDC3-变异烈焰人", Material.FIRE_CHARGE),

            // IDC4 变异爬行者
            new SkillTemplate("idc4_lightning_attack", "闪电攻击", "每X秒对最近的玩家造成一道闪电伤害", "IDC4-变异爬行者", Material.LIGHTNING_ROD),

            // IDC5 变异末影螨
            // 去重：IDC5范围电流 与 电击攻击(electric_attack) 表意重复，移除 GUI 入口
            // new SkillTemplate("idc5_area_electric", "范围电流", "每X秒对自己10*10的范围内释放电流(电流用粒子效果替代，电流对玩家造成X点伤害)", "IDC5-变异末影螨", Material.CONDUIT),

            // IDC6 变异蜘蛛 - 拆分为独立技能
            new SkillTemplate("web_trap", "蜘蛛网陷阱", "在玩家脚下创造蜘蛛网，X秒后消失", "IDC6-变异蜘蛛", Material.COBWEB),
            new SkillTemplate("poison_spit", "毒液喷射", "释放毒液使玩家获得剧毒", "IDC6-变异蜘蛛", Material.SLIME_BALL),

            // IDC7 灾厄卫道士 - 拆分为独立技能
            new SkillTemplate("summon_vindicator", "召唤卫道士", "每X秒召唤两个卫道士", "IDC7-灾厄卫道士", Material.IRON_AXE),
            new SkillTemplate("particle_ball", "粒子球攻击", "发射粒子球，对玩家造成X点伤害", "IDC7-灾厄卫道士", Material.FIREWORK_STAR),
            new SkillTemplate("damage_aura", "伤害光环", "脚底存在粒子光环，在光环内每X秒造成X点伤害", "IDC7-灾厄卫道士", Material.BEACON),

            // IDC8 灾厄唤魔者 - 拆分为独立技能
            new SkillTemplate("evoker_summon_vindicator", "唤魔者召唤", "召唤卫道士仆从", "IDC8-灾厄唤魔者", Material.IRON_AXE),
            new SkillTemplate("dna_spiral", "DNA螺旋魔法", "粒子DNA螺旋魔法攻击", "IDC8-灾厄唤魔者", Material.NAUTILUS_SHELL),
            new SkillTemplate("fang_attack", "尖牙攻击", "圈式内收缩尖牙攻击", "IDC8-灾厄唤魔者", Material.BONE),
            new SkillTemplate("evoker_damage_aura", "唤魔者光环", "脚底存在粒子光环，在光环内每X秒造成X点伤害", "IDC8-灾厄唤魔者", Material.BEACON),

            // IDC9 灾厄劫掠兽
            new SkillTemplate("idc9_critical_aura", "暴击光环", "脚底暴击圆圈，脚底存在粒子光环，在光环内每X秒造成X点伤害", "IDC9-灾厄劫掠兽", Material.REDSTONE),

            // IDC10 变异僵尸马 - 拆分为独立技能
            new SkillTemplate("collision_damage", "撞击伤害", "撞击玩家造成伤害", "IDC10-变异僵尸马", Material.IRON_HORSE_ARMOR),
            new SkillTemplate("summon_armed_zombie", "召唤武装僵尸", "每X秒召唤召唤武装僵尸", "IDC10-变异僵尸马", Material.ZOMBIE_SPAWN_EGG),
            new SkillTemplate("horse_flight", "马匹飞行", "具有飞行能力", "IDC10-变异僵尸马", Material.ELYTRA),

            // IDC11 变异岩浆怪
            new SkillTemplate("idc11_flame_ring", "火焰光环", "启用火焰粒子环，脚底存在粒子光环，在光环内每X秒造成X点伤害，造成火焰效果", "IDC11-变异岩浆怪", Material.MAGMA_BLOCK),

            // IDC12 变异尸壳 - 拆分为独立技能
            new SkillTemplate("summon_enhanced_zombie", "召唤强化僵尸", "召唤强化僵尸", "IDC12-变异尸壳", Material.ZOMBIE_SPAWN_EGG),
            new SkillTemplate("inventory_chaos", "物品栏混乱", "攻击玩家时混乱玩家的1-9格物品栏", "IDC12-变异尸壳", Material.HOPPER),

            // IDC13 变异骷髅 - 拆分为独立技能
            new SkillTemplate("triple_arrow", "三连射箭", "发射三连射箭矢", "IDC13-变异骷髅", Material.ARROW),
            new SkillTemplate("frost_aura", "冰霜光环", "冰霜光环效果", "IDC13-变异骷髅", Material.ICE),

            // IDC14 变异僵尸3 - 拆分为独立技能
            new SkillTemplate("particle_gun", "粒子机枪", "粒子机枪攻击", "IDC14-变异僵尸3", Material.CROSSBOW),
            new SkillTemplate("smoke_particles", "烟雾粒子", "释放烟雾粒子", "IDC14-变异僵尸3", Material.GRAY_DYE),
            new SkillTemplate("dash_attack", "冲刺攻击", "快速冲刺攻击", "IDC14-变异僵尸3", Material.FEATHER),

            // IDC15 鲜血猪灵 - 拆分为独立技能
            new SkillTemplate("red_particle_ball", "红色粒子球", "发射红色粒子球攻击", "IDC15-鲜血猪灵", Material.REDSTONE_BLOCK),
            new SkillTemplate("attack_heal", "攻击回血", "攻击时恢复生命值", "IDC15-鲜血猪灵", Material.GOLDEN_APPLE),
            // 移除防猪人化技能 - 这不是真正的技能，而是实体属性
            // new SkillTemplate("anti_zombification", "防猪人化", "防止被僵尸化", "IDC15-鲜血猪灵", Material.GOLDEN_CARROT),

            // IDC16 暗影潜影贝 - 拆分为独立技能
            new SkillTemplate("shulker_bullet", "潜影贝子弹", "发射潜影贝子弹", "IDC16-暗影潜影贝", Material.SHULKER_SHELL),
            new SkillTemplate("shulker_inventory_chaos", "潜影贝混乱", "混乱玩家物品栏", "IDC16-暗影潜影贝", Material.HOPPER),
            new SkillTemplate("shulker_invisibility", "潜影贝隐身", "进入隐身状态", "IDC16-暗影潜影贝", Material.GLASS),

            // IDC17 变异雪傀儡 - 拆分为独立技能
            new SkillTemplate("snowball_barrage", "雪球弹幕", "连续发射雪球", "IDC17-变异雪傀儡", Material.SNOWBALL),
            new SkillTemplate("snow_freeze_aura", "雪人冰冻光环", "冰冻光环效果", "IDC17-变异雪傀儡", Material.SNOW_BLOCK),

            // IDC18 变异铁傀儡 - 正确的三大技能
            new SkillTemplate("grass_block_attack", "草方块伸展攻击", "发射草方块延伸攻击，造成44点伤害", "IDC18-变异铁傀儡", Material.GRASS_BLOCK),
            new SkillTemplate("sonic_attack", "声波弹攻击", "发射声波弹进行远程攻击", "IDC18-变异铁傀儡", Material.ECHO_SHARD),
            new SkillTemplate("player_tracking", "主动追踪玩家", "智能追踪最近的玩家", "IDC18-变异铁傀儡", Material.COMPASS),

            // IDC19 变异僵尸Max - 正确的五大技能（移除传送能力避免重复）
            new SkillTemplate("weather_control", "天气控制", "控制天气变化，召唤雷雨", "IDC19-变异僵尸Max", Material.LIGHTNING_ROD),
            new SkillTemplate("particle_attack", "粒子攻击", "发射粒子进行攻击", "IDC19-变异僵尸Max", Material.FIREWORK_STAR),
            new SkillTemplate("trident_attack", "三叉戟攻击", "投掷三叉戟进行远程攻击", "IDC19-变异僵尸Max", Material.TRIDENT),
            new SkillTemplate("lightning_attack", "闪电攻击", "召唤闪电攻击玩家", "IDC19-变异僵尸Max", Material.END_ROD),
            new SkillTemplate("invisibility_skill", "隐身能力", "周期性隐身躲避攻击", "IDC19-变异僵尸Max", Material.POTION),

            // IDC20 灵魂坚守者 - 正确的四大技能
            new SkillTemplate("player_tracking_idc20", "主动追踪玩家", "智能追踪最近的玩家", "IDC20-灵魂坚守者", Material.COMPASS),
            new SkillTemplate("sonic_attack_idc20", "声波弹攻击", "发射声波弹进行远程攻击", "IDC20-灵魂坚守者", Material.ECHO_SHARD),
            new SkillTemplate("obsidian_attack", "黑曜石方块攻击", "发射黑曜石延伸攻击", "IDC20-灵魂坚守者", Material.OBSIDIAN),
            new SkillTemplate("knockup_pillar", "击飞黑曜石柱", "向上击飞玩家并生成黑曜石柱", "IDC20-灵魂坚守者", Material.CRYING_OBSIDIAN),

            // IDC21 凋零领主 - 只有2个真实技能（移除错误的凋零光环）
            new SkillTemplate("skull_attack", "凋零头颅攻击", "高速凋零头颅攻击", "IDC21-凋零领主", Material.WITHER_SKELETON_SKULL),
            new SkillTemplate("fence_attack", "下界栅栏攻击", "下界栅栏攻击", "IDC21-凋零领主", Material.NETHER_BRICK_FENCE),

            // IDC22 异变之王 - 7个真实技能（与原版完全一致）
            new SkillTemplate("crystal_attack", "末影水晶攻击", "环绕水晶+子弹攻击+治疗", "IDC22-异变之王", Material.END_CRYSTAL),
            new SkillTemplate("breath_attack", "龙息攻击", "龙息范围攻击", "IDC22-异变之王", Material.DRAGON_BREATH),
            new SkillTemplate("obsidian_blocks_attack", "黑曜石块攻击", "发射黑曜石方块（5波连续攻击）", "IDC22-异变之王", Material.OBSIDIAN),
            new SkillTemplate("obsidian_pillar_attack", "黑曜石柱攻击", "生成巨大黑曜石柱（3个柱子）", "IDC22-异变之王", Material.CRYING_OBSIDIAN),
            new SkillTemplate("fence_attack", "下界栅栏攻击", "5条射线的下界栅栏攻击", "IDC22-异变之王", Material.NETHER_BRICK_FENCE),
            new SkillTemplate("ender_field", "末影粒子减速场", "末影粒子减速场攻击", "IDC22-异变之王", Material.END_PORTAL_FRAME),
            new SkillTemplate("summon_mutants", "召唤变异生物", "召唤变异生物协助战斗", "IDC22-异变之王", Material.SPAWNER)
        );

        // 分类存储（移除特殊技能分类）
        skillCategories.put("id_series", idSkills);
        skillCategories.put("idc_series", idcSkills);

        // 分类图标
        categoryIcons.put("id_series", Material.ZOMBIE_HEAD);
        categoryIcons.put("idc_series", Material.WITHER_SKELETON_SKULL);
    }

    /**
     * 打开技能选择器主界面
     */
    public void openSkillSelector(Player player, String monsterId) {
        editingMonster.put(player, monsterId);
        currentPage.put(player, 0);
        currentCategory.put(player, "id_series"); // 默认显示ID系列

        openCategoryView(player);
    }

    /**
     * 打开分类选择界面
     */
    private void openCategoryView(Player player) {
        String monsterId = editingMonster.get(player);
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        Inventory gui = Bukkit.createInventory(null, 27, CATEGORY_GUI_TITLE + " - " + config.getDisplayName());

        // 填充背景
        fillBackground(gui, Material.PURPLE_STAINED_GLASS_PANE);

        // ID系列技能分类
        ItemStack idSeriesButton = createButton(Material.ZOMBIE_HEAD,
            "§aID系列技能 (35个)",
            Arrays.asList(
                "§7来自ID1-ID25怪物的技能",
                "§7包含基础攻击和特殊能力",
                "§7技能数量: §e" + skillCategories.get("id_series").size() + " §7个",
                "§7涵盖剧毒、召唤、雷电、冰冻等",
                "",
                "§e点击查看ID系列技能"
            ));
        gui.setItem(11, idSeriesButton);

        // IDC系列技能分类
        ItemStack idcSeriesButton = createButton(Material.WITHER_SKELETON_SKULL,
            "§cIDC系列技能 (50个)",
            Arrays.asList(
                "§7来自IDC1-IDC22怪物的技能",
                "§7包含高级能力和免疫效果",
                "§7技能数量: §e" + skillCategories.get("idc_series").size() + " §7个",
                "§7涵盖烈焰、闪电、光环、终极技能等",
                "",
                "§e点击查看IDC系列技能"
            ));
        gui.setItem(13, idcSeriesButton);

        // 已添加技能管理
        ItemStack managedSkillsButton = createButton(Material.ENCHANTED_BOOK,
            "§d已添加技能管理",
            Arrays.asList(
                "§7管理当前怪物的已选技能",
                "§7已选择: §b" + config.getSkillIds().size() + " §7个技能",
                "§7可以移除、配置已选技能",
                "",
                "§e点击管理已添加的技能"
            ));
        gui.setItem(15, managedSkillsButton);

        // 技能库统计信息
        ItemStack skillStatsButton = createButton(Material.KNOWLEDGE_BOOK,
            "§6技能库统计",
            Arrays.asList(
                "§7=== 技能库信息 ===",
                "§aID系列技能: §e" + skillCategories.get("id_series").size() + " §7个",
                "§cIDC系列技能: §e" + skillCategories.get("idc_series").size() + " §7个",
                "§6总技能数: §e" + getTotalSkillCount() + " §7个",
                "",
                "§7=== 当前怪物 ===",
                "§b已选择技能: §e" + config.getSkillIds().size() + " §7个",
                config.getSkillIds().isEmpty() ? "§7(尚未选择任何技能)" : "§7点击上方管理已选技能"
            ));
        gui.setItem(22, skillStatsButton);

        player.openInventory(gui);
    }

    /**
     * 打开技能列表界面
     */
    private void openSkillList(Player player, String category) {
        String monsterId = editingMonster.get(player);
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        currentCategory.put(player, category);
        int page = currentPage.getOrDefault(player, 0);

        List<SkillTemplate> skills = skillCategories.get(category);
        if (skills == null) return;

        String categoryName = getCategoryDisplayName(category);
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE + " - " + categoryName);

        // 填充背景
        fillBackground(gui, Material.LIGHT_GRAY_STAINED_GLASS_PANE);

        // 显示技能
        displaySkills(gui, skills, page, config.getSkillIds());

        // 设置控制按钮
        setupNavigationButtons(gui, page, skills.size());

        player.openInventory(gui);
    }

    /**
     * 显示技能列表
     */
    private void displaySkills(Inventory gui, List<SkillTemplate> skills, int page, List<String> selectedSkills) {
        int startIndex = page * ITEMS_PER_PAGE;
        int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, skills.size());

        for (int i = startIndex; i < endIndex; i++) {
            SkillTemplate skill = skills.get(i);
            int slot = 10 + ((i - startIndex) % 7) + ((i - startIndex) / 7) * 9;

            // 检查技能是否已选择
            boolean isSelected = selectedSkills.contains(skill.id);

            ItemStack skillItem = createSkillItem(skill, isSelected);
            gui.setItem(slot, skillItem);
        }
    }

    /**
     * 创建技能物品
     */
    private ItemStack createSkillItem(SkillTemplate skill, boolean isSelected) {
        Material material = isSelected ? Material.ENCHANTED_BOOK : skill.icon;
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        String name = (isSelected ? "§a✓ " : "§7") + skill.name;
        meta.setDisplayName(name);

        List<String> lore = new ArrayList<>(skill.lore);
        if (isSelected) {
            lore.add("");
            lore.add("§a✓ 已选择此技能");
            lore.add("§e左键点击配置技能参数");
        }
        meta.setLore(lore);

        item.setItemMeta(meta);
        return item;
    }

    /**
     * 设置导航按钮
     */
    private void setupNavigationButtons(Inventory gui, int page, int totalSkills) {
        int totalPages = (totalSkills + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;

        // 上一页按钮
        if (page > 0) {
            ItemStack prevButton = createButton(Material.ARROW,
                "§e← 上一页",
                Arrays.asList("§7第 " + page + " 页"));
            gui.setItem(PREV_PAGE_SLOT, prevButton);
        }

        // 下一页按钮
        if (page < totalPages - 1) {
            ItemStack nextButton = createButton(Material.ARROW,
                "§e下一页 →",
                Arrays.asList("§7第 " + (page + 2) + " 页"));
            gui.setItem(NEXT_PAGE_SLOT, nextButton);
        }

        // 返回按钮
        ItemStack backButton = createButton(Material.BARRIER,
            "§c返回分类选择",
            Arrays.asList("§7返回技能分类界面"));
        gui.setItem(BACK_SLOT, backButton);

        // 页面信息
        ItemStack pageInfo = createButton(Material.PAPER,
            "§6页面信息",
            Arrays.asList(
                "§7当前页: §e" + (page + 1) + " / " + totalPages,
                "§7总技能数: §e" + totalSkills
            ));
        gui.setItem(4, pageInfo);
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE) && !title.startsWith(CATEGORY_GUI_TITLE) && !title.startsWith("§6已添加技能管理")) {
            return;
        }

        event.setCancelled(true);

        if (title.startsWith(CATEGORY_GUI_TITLE)) {
            handleCategoryClick(player, event.getRawSlot());
        } else if (title.startsWith(GUI_TITLE)) {
            // 不再使用中键，保持兼容参数但固定为false
            boolean isMiddleClick = false;
            handleSkillListClick(player, event.getRawSlot(), event.isRightClick(), isMiddleClick);
        } else if (title.startsWith("§6已添加技能管理")) {
            handleSelectedSkillsClick(player, event.getRawSlot(), event.isRightClick());
        }
    }

    /**
     * 处理分类选择点击
     */
    private void handleCategoryClick(Player player, int slot) {
        switch (slot) {
            case 11: // ID系列
                openSkillList(player, "id_series");
                break;
            case 13: // IDC系列
                openSkillList(player, "idc_series");
                break;
            case 15: // 已添加技能管理
                openSelectedSkillsManager(player);
                break;
            case 22: // 技能库统计（只显示信息，不处理点击）
                break;
        }
    }

    /**
     * 处理技能列表点击
     */
    private void handleSkillListClick(Player player, int slot, boolean isRightClick, boolean isMiddleClick) {
        String monsterId = editingMonster.get(player);
        String category = currentCategory.get(player);
        int page = currentPage.getOrDefault(player, 0);

        if (slot == PREV_PAGE_SLOT && page > 0) {
            currentPage.put(player, page - 1);
            openSkillList(player, category);
        } else if (slot == NEXT_PAGE_SLOT) {
            currentPage.put(player, page + 1);
            openSkillList(player, category);
        } else if (slot == BACK_SLOT) {
            openCategoryView(player);
        } else if (slot >= 10 && slot <= 43) {
            // 技能选择区域
            handleSkillSelection(player, slot, isRightClick, isMiddleClick);
        }
    }

    /**
     * 处理技能选择
     */
    private void handleSkillSelection(Player player, int slot, boolean isRightClick, boolean isMiddleClick) {
        String monsterId = editingMonster.get(player);
        String category = currentCategory.get(player);
        int page = currentPage.getOrDefault(player, 0);

        List<SkillTemplate> skills = skillCategories.get(category);
        if (skills == null) return;

        // 计算技能索引
        int row = (slot - 10) / 9;
        int col = (slot - 10) % 9;
        if (col > 6) return; // 超出技能显示区域

        int skillIndex = page * ITEMS_PER_PAGE + row * 7 + col;
        if (skillIndex >= skills.size()) return;

        SkillTemplate skill = skills.get(skillIndex);
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        List<String> currentSkills = new ArrayList<>(config.getSkillIds());

        // 新逻辑：
        // - 右键：移除技能
        // - 左键：若已选择→打开参数编辑；未选择→添加并写入默认参数
        if (isRightClick) {
            config.removeSkill(skill.id);
            player.sendMessage(ChatColor.RED + "已移除技能: " + skill.name);
        } else {
            if (currentSkills.contains(skill.id)) {
                // 已选择 → 打开参数编辑
                skillConfigGUI.openSkillConfigEditor(player, monsterId, skill.id);
                return;
            } else {
                // 未选择 → 添加技能并写入默认参数
                addSkillWithDefaultParameters(config, skill.id);
                player.sendMessage(ChatColor.GREEN + "已添加技能: " + skill.name);
            }
        }

        // 更新配置
        idzManager.updateMonsterConfig(monsterId, config);

        // 刷新界面
        openSkillList(player, category);
    }

    /**
     * 处理已选技能管理界面点击
     */
    private void handleSelectedSkillsClick(Player player, int slot, boolean isRightClick) {
        String monsterId = editingMonster.get(player);
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        // 添加调试信息
        logger.info("已选技能管理点击 - 玩家: " + player.getName() + ", 槽位: " + slot + ", 右键: " + isRightClick);

        if (slot == 49) { // 返回按钮
            logger.info("点击返回按钮");
            openCategoryView(player);
        } else if (slot == 45) { // 清空所有技能按钮
            logger.info("点击清空按钮");
            clearAllSkills(player, monsterId);
        } else if (slot >= 10 && slot <= 43 && slot % 9 != 0 && slot % 9 != 8) {
            // 技能操作区域
            logger.info("进入技能操作区域处理");
            handleSelectedSkillOperation(player, slot, isRightClick, monsterId);
        } else {
            logger.info("点击位置不在处理范围内 - 槽位: " + slot);
        }
    }

    /**
     * 处理已选技能操作
     */
    private void handleSelectedSkillOperation(Player player, int slot, boolean isRightClick, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            logger.info("配置为空，无法处理技能操作");
            return;
        }

        List<String> selectedSkills = config.getSkillIds();
        logger.info("当前已选技能数量: " + selectedSkills.size());

        // 计算技能索引
        int row = (slot - 10) / 9;
        int col = (slot - 10) % 9;
        logger.info("槽位计算 - 槽位: " + slot + ", 行: " + row + ", 列: " + col);

        // 检查是否在有效的技能区域（排除边框）
        // 有效区域：每行的第1-7列（col 0-6），排除第0列和第8列
        if (slot % 9 == 0 || slot % 9 == 8) {
            logger.info("点击边框位置，忽略");
            return; // 边框位置
        }

        // 重新计算技能索引，考虑到技能是从槽位10开始的
        int skillIndex = row * 7 + col;
        logger.info("计算的技能索引: " + skillIndex + ", 技能总数: " + selectedSkills.size());

        if (skillIndex >= selectedSkills.size()) {
            logger.info("技能索引超出范围，忽略");
            return;
        }

        String skillId = selectedSkills.get(skillIndex);
        logger.info("操作技能: " + skillId + ", 右键: " + isRightClick);

        if (isRightClick) {
            // 右键移除技能
            config.removeSkill(skillId);
            idzManager.updateMonsterConfig(monsterId, config);

            player.sendMessage(ChatColor.RED + "已移除技能: " + skillId);

            // 如果没有技能了，返回分类界面
            if (config.getSkills().isEmpty()) {
                player.sendMessage(ChatColor.GRAY + "所有技能已移除，返回技能选择界面");
                openCategoryView(player);
            } else {
                // 刷新已选技能管理界面
                openSelectedSkillsManager(player);
            }
        } else {
            // 左键配置技能参数
            logger.info("尝试打开技能配置编辑器 - 技能ID: " + skillId);
            skillConfigGUI.openSkillConfigEditor(player, monsterId, skillId);
        }
    }

    /**
     * 清空所有技能
     */
    private void clearAllSkills(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        config.clearSkills();
        idzManager.updateMonsterConfig(monsterId, config);

        player.sendMessage(ChatColor.RED + "已清空所有技能！");
        player.sendMessage(ChatColor.GRAY + "返回技能选择界面");

        // 返回分类界面
        openCategoryView(player);
    }

    /**
     * 添加技能并设置默认参数
     */
    private void addSkillWithDefaultParameters(IDZMonsterConfig config, String skillId) {
        System.out.println("[DEBUG] addSkillWithDefaultParameters called with skillId: " + skillId);
        logger.info("开始为技能添加默认参数 - 技能ID: " + skillId);

        // 检查技能是否已经存在
        for (SkillConfig existingSkill : config.getSkills()) {
            if (existingSkill.getSkillName().equals(skillId)) {
                System.out.println("[DEBUG] Skill already exists: " + skillId + ", skipping");
                logger.warning("技能已存在，跳过添加: " + skillId);
                return;
            }
        }

        // 获取技能的默认参数
        Map<String, Object> defaultParameters = skillConfigGUI.getSkillDefaultParameters(skillId);

        logger.info("获取到的默认参数: " + (defaultParameters != null ? defaultParameters.size() : "null") + " 个");
        if (defaultParameters != null) {
            logger.info("默认参数内容: " + defaultParameters);
        }

        if (defaultParameters != null && !defaultParameters.isEmpty()) {
            // 使用新的技能配置结构
            SkillConfig skillConfig = new SkillConfig(skillId, defaultParameters);
            config.addSkill(skillConfig);

            logger.info("为技能 " + skillId + " 添加了 " + defaultParameters.size() + " 个默认参数");
            logger.info("技能配置创建成功，参数: " + skillConfig.getParameters());
        } else {
            // 如果没有默认参数，创建空的技能配置
            SkillConfig skillConfig = new SkillConfig(skillId);
            config.addSkill(skillConfig);

            logger.warning("为技能 " + skillId + " 创建了空的技能配置 - 没有找到默认参数");
        }
    }

    // 辅助方法
    private void fillBackground(Inventory gui, Material material) {
        ItemStack background = new ItemStack(material);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);

        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, background);
            }
        }
    }

    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * 打开已选技能管理界面
     */
    private void openSelectedSkillsManager(Player player) {
        String monsterId = editingMonster.get(player);
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        List<String> selectedSkills = config.getSkillIds();
        if (selectedSkills.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "当前怪物尚未选择任何技能！");
            player.sendMessage(ChatColor.GRAY + "请先从ID系列或IDC系列中选择技能。");
            return;
        }

        Inventory gui = Bukkit.createInventory(null, 54, "§6已添加技能管理 - " + config.getDisplayName());

        // 填充背景
        fillBackground(gui, Material.PURPLE_STAINED_GLASS_PANE);

        // 显示已选技能
        displaySelectedSkills(gui, selectedSkills);

        // 设置控制按钮
        setupSelectedSkillsButtons(gui, selectedSkills.size());

        player.openInventory(gui);
    }

    /**
     * 显示已选技能
     */
    private void displaySelectedSkills(Inventory gui, List<String> selectedSkills) {
        int slot = 10;
        for (String skillId : selectedSkills) {
            // 查找技能模板
            SkillTemplate skillTemplate = findSkillTemplate(skillId);
            if (skillTemplate == null) {
                // 如果找不到模板，创建一个基础显示
                skillTemplate = new SkillTemplate(skillId, skillId, "未知技能", "未知来源", Material.PAPER);
            }

            ItemStack skillItem = createSelectedSkillItem(skillTemplate);
            gui.setItem(slot, skillItem);

            slot++;
            if (slot % 9 == 8) slot += 2; // 跳过边框
            if (slot >= 44) break; // 避免超出界面
        }
    }

    /**
     * 查找技能模板
     */
    private SkillTemplate findSkillTemplate(String skillId) {
        for (List<SkillTemplate> skillList : skillCategories.values()) {
            for (SkillTemplate template : skillList) {
                if (template.id.equals(skillId)) {
                    return template;
                }
            }
        }
        return null;
    }

    /**
     * 创建已选技能物品
     */
    private ItemStack createSelectedSkillItem(SkillTemplate skill) {
        ItemStack item = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName("§a✓ " + skill.name);

        List<String> lore = new ArrayList<>();
        lore.add("§7" + skill.description);
        lore.add("§7来源: §e" + skill.source);
        lore.add("");
        lore.add("§a✓ 已添加到怪物");
        lore.add("");
        lore.add("§e左键点击配置参数");
        lore.add("§c右键点击移除技能");

        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * 设置已选技能管理按钮
     */
    private void setupSelectedSkillsButtons(Inventory gui, int skillCount) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回技能选择",
            Arrays.asList("§7返回技能分类界面"));
        gui.setItem(49, backButton);

        // 清空所有技能按钮
        ItemStack clearAllButton = createButton(Material.BARRIER,
            "§c清空所有技能",
            Arrays.asList(
                "§7移除当前怪物的所有技能",
                "§c警告: 此操作不可撤销！",
                "",
                "§e点击确认清空"
            ));
        gui.setItem(45, clearAllButton);

        // 技能统计信息
        ItemStack statsButton = createButton(Material.BOOK,
            "§6技能统计",
            Arrays.asList(
                "§7当前已选技能: §e" + skillCount + " §7个",
                "§7可配置参数的技能数量",
                "§7左键配置，右键移除"
            ));
        gui.setItem(4, statsButton);
    }

    private String getCategoryDisplayName(String category) {
        switch (category) {
            case "id_series": return "ID系列技能";
            case "idc_series": return "IDC系列技能";
            default: return "未知分类";
        }
    }

    private int getTotalSkillCount() {
        return skillCategories.values().stream().mapToInt(List::size).sum();
    }

    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        currentPage.remove(player);
        currentCategory.remove(player);

        // 清理技能配置编辑器
        if (skillConfigGUI != null) {
            skillConfigGUI.cleanupPlayer(player);
        }
    }
}
