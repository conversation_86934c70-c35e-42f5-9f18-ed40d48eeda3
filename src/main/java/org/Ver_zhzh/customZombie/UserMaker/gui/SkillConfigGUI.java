package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;
import org.Ver_zhzh.customZombie.UserMaker.SkillConfig;

import java.util.*;
import java.util.logging.Logger;

/**
 * 技能配置编辑器GUI
 * 用于详细配置IDZ怪物的技能参数
 *
 * <AUTHOR>
 * @version 1.0
 */
public class SkillConfigGUI implements Listener {

    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager idzManager;

    private static final String GUI_TITLE = "§6技能配置编辑器";

    // 当前编辑状态追踪
    private final Map<Player, String> editingMonster;
    private final Map<Player, String> editingSkill;
    private final Map<Player, String> waitingForInput;
    private final Map<Player, String> inputType;

    // 技能参数模板定义
    private final Map<String, SkillParameterTemplate> skillParameterTemplates;

    /**
     * 技能参数模板类
     */
    public static class SkillParameterTemplate {
        public final String skillId;
        public final String skillName;
        public final Map<String, ParameterInfo> parameters;

        public SkillParameterTemplate(String skillId, String skillName) {
            this.skillId = skillId;
            this.skillName = skillName;
            this.parameters = new HashMap<>();
        }

        public void addParameter(String key, String name, String description, Object defaultValue,
                               Object minValue, Object maxValue, String unit) {
            parameters.put(key, new ParameterInfo(name, description, defaultValue, minValue, maxValue, unit));
        }
    }

    /**
     * 参数信息类
     */
    public static class ParameterInfo {
        public final String name;
        public final String description;
        public final Object defaultValue;
        public final Object minValue;
        public final Object maxValue;
        public final String unit;

        public ParameterInfo(String name, String description, Object defaultValue,
                           Object minValue, Object maxValue, String unit) {
            this.name = name;
            this.description = description;
            this.defaultValue = defaultValue;
            this.minValue = minValue;
            this.maxValue = maxValue;
            this.unit = unit;
        }
    }

    /**
     * 构造函数
     */
    private final MonsterIdPickerGUI monsterIdPickerGUI;

    public SkillConfigGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.idzManager = idzManager;
        this.editingMonster = new HashMap<>();
        this.editingSkill = new HashMap<>();
        this.waitingForInput = new HashMap<>();
        this.inputType = new HashMap<>();
        this.skillParameterTemplates = new HashMap<>();

        // 初始化技能参数模板
        initializeSkillParameterTemplates();

        // 初始化怪物ID选择器GUI
        this.monsterIdPickerGUI = new MonsterIdPickerGUI(plugin, idzManager, this);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        logger.info("技能配置编辑器GUI初始化完成，加载了 " + skillParameterTemplates.size() + " 个技能模板");
    }

    /**
     * 初始化技能参数模板
     */
    private void initializeSkillParameterTemplates() {
        // 剧毒攻击技能
        SkillParameterTemplate poisonAttack = new SkillParameterTemplate("poison_attack", "剧毒攻击");
        poisonAttack.addParameter("poison_level", "剧毒等级", "剧毒效果的等级（0=1级，1=2级）", 1, 0, Integer.MAX_VALUE, "级");
        poisonAttack.addParameter("poison_duration", "持续时间", "剧毒效果持续时间(-1=永久)", 80, -1, Integer.MAX_VALUE, "tick");
        poisonAttack.addParameter("poison_chance", "触发概率", "攻击时触发剧毒的概率", 0.8, 0.0, Double.MAX_VALUE, "%");
        skillParameterTemplates.put("poison_attack", poisonAttack);

        // 死亡召唤技能
        SkillParameterTemplate deathSummon = new SkillParameterTemplate("death_summon", "死亡召唤");
        deathSummon.addParameter("summon_count", "召唤数量", "死亡时召唤的僵尸数量", 2, 0, Integer.MAX_VALUE, "个");
        deathSummon.addParameter("summon_range", "召唤范围", "召唤僵尸的范围", 5.0, 0.0, Double.MAX_VALUE, "格");
        deathSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("death_summon", deathSummon);

        // 远程魔法攻击（新增参数模板）
        SkillParameterTemplate rangedMagic = new SkillParameterTemplate("ranged_magic_attack", "远程魔法攻击");
        rangedMagic.addParameter("magic_interval", "施法间隔", "远程魔法的施法间隔", 80, 20, 600, "tick");
        rangedMagic.addParameter("magic_range", "施法范围", "目标检测范围", 18.0, 5.0, 40.0, "格");
        rangedMagic.addParameter("magic_damage", "魔法伤害", "命中造成的伤害", 6.0, 1.0, 40.0, "点");
        rangedMagic.addParameter("magic_speed", "粒子速度", "魔法弹道速度", 1.2, 0.1, 5.0, "x");
        rangedMagic.addParameter("magic_particle", "粒子类型", "如 FLAME, SOUL_FIRE_FLAME, ENCHANTMENT_TABLE", "ENCHANTMENT_TABLE", null, null, "");
        rangedMagic.addParameter("magic_color", "粒子颜色RGB", "如 255,80,200（部分粒子支持）", "", null, null, "");
        rangedMagic.addParameter("magic_trail", "尾迹粒子", "尾迹粒子类型（可选）", "CRIT_MAGIC", null, null, "");
        rangedMagic.addParameter("magic_count", "弹幕数量", "每次释放的弹幕数", 1, 1, 10, "发");
        rangedMagic.addParameter("magic_spread", "散布角度", "多发弹幕的散布角度(度)", 8.0, 0.0, 60.0, "°");
        skillParameterTemplates.put("ranged_magic_attack", rangedMagic);

        // 箭矢攻击技能
        SkillParameterTemplate arrowAttack = new SkillParameterTemplate("arrow_attack", "箭矢攻击");
        arrowAttack.addParameter("arrow_damage", "箭矢伤害", "每支箭矢造成的伤害", 4.0, 1.0, 20.0, "点");
        arrowAttack.addParameter("arrow_speed", "箭矢速度", "箭矢飞行速度", 1.6, 0.5, 5.0, "倍");
        arrowAttack.addParameter("attack_range", "攻击范围", "箭矢攻击的最大范围", 16.0, 5.0, 50.0, "格");
        arrowAttack.addParameter("attack_interval", "攻击间隔", "两次箭矢攻击的间隔时间", 60, 20, 600, "tick");
        skillParameterTemplates.put("arrow_attack", arrowAttack);

        // 雷电攻击技能
        SkillParameterTemplate lightningAttack = new SkillParameterTemplate("lightning_attack", "雷电攻击");
        lightningAttack.addParameter("lightning_damage", "雷电伤害", "雷电造成的直接伤害", 8.0, 2.0, 30.0, "点");
        lightningAttack.addParameter("lightning_range", "攻击范围", "雷电攻击的范围", 10.0, 3.0, 30.0, "格");
        lightningAttack.addParameter("lightning_interval", "攻击间隔", "雷电攻击的冷却时间", 100, 40, 1200, "tick");
        skillParameterTemplates.put("lightning_attack", lightningAttack);


        // 电击攻击技能（ID13）
        SkillParameterTemplate electricAttack = new SkillParameterTemplate("electric_attack", "电击攻击");
        electricAttack.addParameter("electric_damage", "电击伤害", "电击造成的直接伤害", 5.0, 1.0, 30.0, "点");
        electricAttack.addParameter("electric_range", "电击范围", "电击影响的范围", 6.0, 2.0, 20.0, "格");
        electricAttack.addParameter("electric_interval", "攻击间隔", "电击攻击的冷却时间", 100, 20, 1200, "tick");
        skillParameterTemplates.put("electric_attack", electricAttack);

        // 冰冻攻击技能（包含缓慢+挖掘疲劳两个效果）
        SkillParameterTemplate freezeAttack = new SkillParameterTemplate("freeze_attack", "冰冻攻击");
        freezeAttack.addParameter("slowness_level", "缓慢等级", "缓慢效果的等级(0=1级)", 2, 0, 9, "级");
        freezeAttack.addParameter("slowness_duration", "缓慢持续时间", "缓慢效果持续时间", 100, 20, 1200, "tick");
        freezeAttack.addParameter("mining_level", "挖掘疲劳等级", "挖掘疲劳效果等级(0=1级)", 2, 0, 9, "级");
        freezeAttack.addParameter("mining_duration", "挖掘疲劳持续时间", "挖掘疲劳效果持续时间", 100, 20, 1200, "tick");
        freezeAttack.addParameter("freeze_chance", "触发概率", "攻击时触发冰冻的概率", 0.8, 0.0, 1.0, "%");
        skillParameterTemplates.put("freeze_attack", freezeAttack);

        // 传送攻击技能
        SkillParameterTemplate teleportAttack = new SkillParameterTemplate("teleport_attack", "传送攻击");

        // 雷霆攻击技能（ID17）
        SkillParameterTemplate thunderAttack = new SkillParameterTemplate("thunder_attack", "雷霆攻击");
        thunderAttack.addParameter("thunder_damage", "雷霆伤害", "每次落雷造成的伤害", 8.0, 1.0, 40.0, "点");
        thunderAttack.addParameter("thunder_range",  "雷霆范围", "搜索/影响范围", 10.0, 3.0, 40.0, "格");
        thunderAttack.addParameter("thunder_interval","攻击间隔", "两次雷霆攻击间隔", 100, 20, 1200, "tick");
        thunderAttack.addParameter("thunder_count",  "连击次数", "一次攻击落雷次数", 1, 1, 10, "次");
        skillParameterTemplates.put("thunder_attack", thunderAttack);

        teleportAttack.addParameter("teleport_range", "传送范围", "瞬移的最大距离", 16.0, 5.0, 50.0, "格");
        teleportAttack.addParameter("teleport_interval", "传送间隔", "两次传送的冷却时间", 200, 60, 1200, "tick");
        teleportAttack.addParameter("teleport_damage", "传送伤害", "传送攻击的额外伤害", 2.0, 0.0, 10.0, "点");
        skillParameterTemplates.put("teleport_attack", teleportAttack);

        // 范围闪电技能
        SkillParameterTemplate areaLightning = new SkillParameterTemplate("area_lightning", "范围闪电");
        areaLightning.addParameter("lightning_range", "闪电范围", "范围闪电的作用范围", 6.0, 3.0, 20.0, "格");
        areaLightning.addParameter("lightning_interval", "闪电间隔", "范围闪电的冷却时间", 120, 60, 600, "tick");
        areaLightning.addParameter("lightning_damage", "闪电伤害", "每道闪电造成的伤害", 6.0, 2.0, 20.0, "点");
        skillParameterTemplates.put("area_lightning", areaLightning);

        // 全局雷电技能
        SkillParameterTemplate globalLightning = new SkillParameterTemplate("global_lightning", "全局雷电");
        globalLightning.addParameter("global_damage", "全局伤害", "对所有玩家造成的伤害", 10.0, 2.0, 30.0, "点");
        globalLightning.addParameter("global_interval", "攻击间隔", "全局雷电的冷却时间", 300, 100, 1200, "tick");
        globalLightning.addParameter("sound_enabled", "音效开关", "是否播放雷电音效", true, null, null, "");
        skillParameterTemplates.put("global_lightning", globalLightning);

        // 时间控制技能
        SkillParameterTemplate timeControl = new SkillParameterTemplate("time_control", "时间控制");
        timeControl.addParameter("time_slow_duration", "减速持续时间", "时间减速效果持续时间", 120, 40, 600, "tick");
        timeControl.addParameter("time_slow_interval", "控制间隔", "时间控制的冷却时间", 200, 100, 1200, "tick");
        timeControl.addParameter("slow_multiplier", "减速倍数", "时间流速减慢倍数", 0.5, 0.1, 0.9, "倍");
        skillParameterTemplates.put("time_control", timeControl);

        // 烈焰攻击技能
        SkillParameterTemplate flameAttack = new SkillParameterTemplate("flame_attack", "烈焰攻击");
        flameAttack.addParameter("flame_damage", "烈焰伤害", "烈焰弹造成的伤害", 6.0, 2.0, 20.0, "点");
        flameAttack.addParameter("flame_interval", "攻击间隔", "烈焰攻击的冷却时间", 80, 40, 400, "tick");
        flameAttack.addParameter("flame_range", "攻击范围", "烈焰攻击的最大范围", 15.0, 5.0, 30.0, "格");
        skillParameterTemplates.put("flame_attack", flameAttack);

        // 蜘蛛网陷阱技能
        SkillParameterTemplate webTrap = new SkillParameterTemplate("web_trap", "蜘蛛网陷阱");
        webTrap.addParameter("web_duration", "蜘蛛网持续时间", "蜘蛛网存在的时间", 100, 40, 600, "tick");
        webTrap.addParameter("web_range", "放置范围", "蜘蛛网放置的范围", 3.0, 1.0, 10.0, "格");
        webTrap.addParameter("trap_interval", "陷阱间隔", "蜘蛛网陷阱的冷却时间", 150, 60, 600, "tick");
        skillParameterTemplates.put("web_trap", webTrap);

        // 伤害光环技能
        SkillParameterTemplate damageAura = new SkillParameterTemplate("damage_aura", "伤害光环");
        damageAura.addParameter("aura_damage", "光环伤害", "光环每次造成的伤害", 3.0, 1.0, 10.0, "点");
        damageAura.addParameter("aura_range", "光环范围", "伤害光环的作用范围", 5.0, 2.0, 15.0, "格");
        damageAura.addParameter("aura_interval", "伤害间隔", "光环伤害的间隔时间", 40, 20, 200, "tick");
        skillParameterTemplates.put("damage_aura", damageAura);

        // 召唤卫道士技能
        SkillParameterTemplate summonVindicator = new SkillParameterTemplate("summon_vindicator", "召唤卫道士");
        summonVindicator.addParameter("summon_count", "召唤数量", "每次召唤的卫道士数量", 2, 1, 5, "个");
        summonVindicator.addParameter("summon_interval", "召唤间隔", "召唤卫道士的冷却时间", 200, 100, 1200, "tick");
        summonVindicator.addParameter("summon_range", "召唤范围", "卫道士召唤的范围", 5.0, 2.0, 15.0, "格");
        skillParameterTemplates.put("summon_vindicator", summonVindicator);

        // DNA螺旋魔法技能
        SkillParameterTemplate dnaSpiral = new SkillParameterTemplate("dna_spiral", "DNA螺旋魔法");
        dnaSpiral.addParameter("spiral_damage", "螺旋伤害", "DNA螺旋魔法造成的伤害", 5.0, 2.0, 15.0, "点");
        dnaSpiral.addParameter("spiral_range", "魔法范围", "DNA螺旋的作用范围", 8.0, 3.0, 20.0, "格");
        dnaSpiral.addParameter("spiral_interval", "魔法间隔", "DNA螺旋魔法的冷却时间", 120, 60, 600, "tick");
        skillParameterTemplates.put("dna_spiral", dnaSpiral);

        // IDC22末影水晶攻击技能（更新为IDC22参数，使用tick单位）
        SkillParameterTemplate crystalAttack = new SkillParameterTemplate("crystal_attack", "末影水晶攻击");
        crystalAttack.addParameter("crystal_damage", "水晶子弹伤害", "末影水晶子弹伤害", 15.0, 5.0, 30.0, "点");
        crystalAttack.addParameter("crystal_heal_amount", "水晶治疗量", "末影水晶治疗量", 50.0, 20.0, 100.0, "点");
        crystalAttack.addParameter("crystal_orbital_count", "环绕水晶数量", "环绕水晶数量", 3, 1, 8, "个");
        crystalAttack.addParameter("crystal_orbital_radius", "环绕半径", "环绕水晶半径", 3.0, 1.0, 10.0, "格");
        crystalAttack.addParameter("crystal_orbital_lifetime", "水晶生命周期", "环绕水晶生命周期", 200, 100, 600, "tick");
        crystalAttack.addParameter("crystal_attack_mode", "攻击模式", "粒子攻击(true)或烈焰弹(false)", true, false, true, "布尔");
        crystalAttack.addParameter("attack_interval", "攻击间隔", "末影水晶攻击冷却时间", 300, 100, 600, "tick");
        skillParameterTemplates.put("crystal_attack", crystalAttack);

        // IDC22龙息攻击技能（更新为IDC22参数，使用tick单位）
        SkillParameterTemplate breathAttack = new SkillParameterTemplate("breath_attack", "龙息攻击");
        breathAttack.addParameter("breath_damage", "龙息伤害", "龙息造成的伤害", 12.0, 5.0, 25.0, "点");
        breathAttack.addParameter("breath_range", "龙息范围", "龙息攻击的范围", 30.0, 10.0, 50.0, "格");
        breathAttack.addParameter("attack_interval", "攻击间隔", "龙息攻击冷却时间", 200, 60, 400, "tick");
        skillParameterTemplates.put("breath_attack", breathAttack);

        // IDC22黑曜石块攻击技能（使用tick单位）
        SkillParameterTemplate obsidianBlocksAttack = new SkillParameterTemplate("obsidian_blocks_attack", "黑曜石块攻击");
        obsidianBlocksAttack.addParameter("obsidian_blocks_damage", "黑曜石块伤害", "黑曜石块造成的伤害", 12.0, 5.0, 25.0, "点");
        obsidianBlocksAttack.addParameter("obsidian_blocks_waves", "攻击波数", "黑曜石块攻击波数", 5, 2, 10, "波");
        obsidianBlocksAttack.addParameter("obsidian_blocks_per_wave", "每波方块数", "每波黑曜石方块数量", 7, 3, 15, "个");
        obsidianBlocksAttack.addParameter("obsidian_blocks_wave_interval", "波次间隔", "波次间隔时间", 40, 20, 100, "tick");
        obsidianBlocksAttack.addParameter("obsidian_blocks_speed", "发射速度", "黑曜石方块发射速度", 15, 5, 30, "倍数");
        obsidianBlocksAttack.addParameter("attack_interval", "攻击间隔", "黑曜石块攻击冷却时间", 160, 60, 300, "tick");
        skillParameterTemplates.put("obsidian_blocks_attack", obsidianBlocksAttack);

        // IDC22黑曜石柱攻击技能（使用tick单位）
        SkillParameterTemplate obsidianPillarAttack = new SkillParameterTemplate("obsidian_pillar_attack", "黑曜石柱攻击");
        obsidianPillarAttack.addParameter("obsidian_pillar_damage", "黑曜石柱伤害", "黑曜石柱造成的伤害", 18.0, 8.0, 35.0, "点");
        obsidianPillarAttack.addParameter("obsidian_pillar_count", "柱子数量", "黑曜石柱数量", 3, 1, 8, "个");
        obsidianPillarAttack.addParameter("obsidian_pillar_growth_time", "柱子生长时间", "黑曜石柱生长时间", 160, 80, 400, "tick");
        obsidianPillarAttack.addParameter("obsidian_pillar_random_range", "随机范围", "柱子位置随机范围", 8, 3, 20, "格");
        obsidianPillarAttack.addParameter("attack_interval", "攻击间隔", "黑曜石柱攻击冷却时间", 100, 40, 240, "tick");
        skillParameterTemplates.put("obsidian_pillar_attack", obsidianPillarAttack);

        // IDC22末影粒子减速场技能（使用tick单位）
        SkillParameterTemplate enderField = new SkillParameterTemplate("ender_field", "末影粒子减速场");
        enderField.addParameter("ender_field_radius", "减速场半径", "末影粒子减速场半径", 10.0, 5.0, 25.0, "格");
        enderField.addParameter("ender_field_duration", "减速场持续时间", "末影粒子减速场持续时间", 200, 100, 600, "tick");
        enderField.addParameter("attack_interval", "攻击间隔", "末影粒子减速场冷却时间", 400, 160, 800, "tick");
        skillParameterTemplates.put("ender_field", enderField);

        // IDC22召唤变异生物技能（使用tick单位）
        SkillParameterTemplate summonMutants = new SkillParameterTemplate("summon_mutants", "召唤变异生物");
        summonMutants.addParameter("summon_range", "召唤范围", "召唤变异生物的范围", 30.0, 10.0, 50.0, "格");
        summonMutants.addParameter("attack_interval", "攻击间隔", "召唤变异生物冷却时间", 80, 40, 200, "tick");
        skillParameterTemplates.put("summon_mutants", summonMutants);

        // 继续添加更多技能模板...
        addMoreSkillTemplates();

        logger.info("初始化了 " + skillParameterTemplates.size() + " 个技能参数模板");
    }

    /**
     * 转换技能ID为模板ID
     * 移除ID前缀以匹配技能参数模板
     */
    private String convertSkillIdToTemplateId(String skillId) {
        // 移除ID前缀 (如 "id7_arrow_attack" -> "arrow_attack")
        if (skillId.startsWith("id") && skillId.contains("_")) {
            int firstUnderscore = skillId.indexOf("_");
            if (firstUnderscore > 0 && firstUnderscore < skillId.length() - 1) {
                return skillId.substring(firstUnderscore + 1);
            }
        }

        // 移除IDC前缀 (如 "idc7_summon_vindicator" -> "summon_vindicator")
        if (skillId.startsWith("idc") && skillId.contains("_")) {
            int firstUnderscore = skillId.indexOf("_");
            if (firstUnderscore > 0 && firstUnderscore < skillId.length() - 1) {
                return skillId.substring(firstUnderscore + 1);
            }
        }

        // 如果没有前缀，直接返回原ID
        return skillId;
    }

    /**
     * 添加更多技能模板（剩余69个技能）
     */
    private void addMoreSkillTemplates() {
        // 定时召唤技能
        SkillParameterTemplate timedSummon = new SkillParameterTemplate("timed_summon", "定时召唤");
        timedSummon.addParameter("summon_interval", "召唤间隔", "定时召唤的间隔时间", 120, 60, 1200, "tick");
        timedSummon.addParameter("summon_count", "召唤数量", "每次召唤的数量", 1, 1, 5, "个");
        timedSummon.addParameter("summon_range", "召唤范围", "召唤的范围", 5.0, 2.0, 15.0, "格");
        // 新增：与“死亡召唤”一致，支持选择具体要召唤的怪物
        timedSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("timed_summon", timedSummon);

        // 自爆攻击技能
        SkillParameterTemplate explosionDeath = new SkillParameterTemplate("explosion_death", "自爆攻击");
        explosionDeath.addParameter("explosion_power", "爆炸威力", "自爆的爆炸威力", 3.0, 1.0, 10.0, "级");
        explosionDeath.addParameter("explosion_fire", "是否起火", "爆炸是否产生火焰", false, null, null, "");
        explosionDeath.addParameter("explosion_damage_blocks", "破坏方块", "爆炸是否破坏方块", false, null, null, "");
        explosionDeath.addParameter("fuse_time", "引爆时间", "自爆倒计时", 60, 20, 200, "tick");
        skillParameterTemplates.put("explosion_death", explosionDeath);

        // 毒箭攻击技能
        SkillParameterTemplate poisonArrow = new SkillParameterTemplate("poison_arrow", "毒箭攻击");
        poisonArrow.addParameter("arrow_damage", "箭矢伤害", "毒箭造成的伤害", 4.0, 1.0, 15.0, "点");
        poisonArrow.addParameter("poison_level", "剧毒等级", "毒箭的剧毒等级", 1, 0, 9, "级");
        poisonArrow.addParameter("poison_duration", "剧毒持续时间", "剧毒效果持续时间", 100, 20, 600, "tick");
        poisonArrow.addParameter("attack_interval", "攻击间隔", "毒箭攻击间隔", 80, 40, 400, "tick");
        skillParameterTemplates.put("poison_arrow", poisonArrow);

        // 受伤召唤技能
        SkillParameterTemplate damageSummon = new SkillParameterTemplate("damage_summon", "受伤召唤");
        damageSummon.addParameter("damage_threshold", "伤害阈值", "触发召唤的伤害阈值", 50.0, 10.0, 200.0, "点");
        damageSummon.addParameter("summon_count", "召唤数量", "每次召唤的数量", 1, 1, 5, "个");
        damageSummon.addParameter("summon_range", "召唤范围", "召唤的范围", 5.0, 2.0, 15.0, "格");
        // 新增：可选择召唤的怪物ID（复用死亡召唤的选择器GUI）
        damageSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("damage_summon", damageSummon);

        // 范围debuff技能
        SkillParameterTemplate areaDebuff = new SkillParameterTemplate("area_debuff", "范围debuff");
        areaDebuff.addParameter("debuff_range", "debuff范围", "负面效果的作用范围", 10.0, 5.0, 25.0, "格");
        areaDebuff.addParameter("nausea_level", "反胃等级", "反胃效果等级", 1, 0, 9, "级");
        areaDebuff.addParameter("slowness_level", "缓慢等级", "缓慢效果等级", 1, 0, 9, "级");
        areaDebuff.addParameter("effect_interval", "效果间隔", "负面效果刷新间隔", 40, 20, 200, "tick");
        // 新增：debuff持续时长，便于具体效果配置
        areaDebuff.addParameter("debuff_duration", "debuff持续", "给玩家施加负面效果的持续时间", 100, 20, 1200, "tick");
        skillParameterTemplates.put("area_debuff", areaDebuff);

        // 全局伤害技能
        SkillParameterTemplate globalDamage = new SkillParameterTemplate("global_damage", "全局伤害");
        globalDamage.addParameter("global_damage", "全局伤害", "对所有玩家造成的伤害", 10.0, 2.0, 50.0, "点");
        globalDamage.addParameter("damage_interval", "伤害间隔", "全局伤害的间隔时间", 400, 200, 2400, "tick");
        globalDamage.addParameter("sound_enabled", "音效开关", "是否播放爆炸音效", true, null, null, "");
        skillParameterTemplates.put("global_damage", globalDamage);

        // 范围buff技能
        SkillParameterTemplate areaBuff = new SkillParameterTemplate("area_buff", "范围buff");
        areaBuff.addParameter("buff_range", "buff范围", "增益效果的作用范围", 10.0, 5.0, 25.0, "格");
        areaBuff.addParameter("strength_level", "力量等级", "力量效果等级", 1, 0, 9, "级");
        areaBuff.addParameter("speed_level", "速度等级", "速度效果等级", 1, 0, 9, "级");
        areaBuff.addParameter("effect_interval", "效果间隔", "增益效果刷新间隔", 40, 20, 200, "tick");
        skillParameterTemplates.put("area_buff", areaBuff);

        // 检测召唤技能
        SkillParameterTemplate detectSummon = new SkillParameterTemplate("detect_summon", "检测召唤");
        detectSummon.addParameter("detect_range", "检测范围", "检测玩家的范围", 5.0, 2.0, 15.0, "格");
        detectSummon.addParameter("detect_interval", "检测间隔", "检测的间隔时间", 100, 40, 600, "tick");
        detectSummon.addParameter("summon_count", "召唤数量", "检测到玩家时召唤的数量", 1, 1, 5, "个");
        // 新增：复用死亡召唤/定时召唤的怪物选择参数
        detectSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("detect_summon", detectSummon);

        // 飞行能力技能
        SkillParameterTemplate flightAbility = new SkillParameterTemplate("flight_ability", "飞行能力");
        flightAbility.addParameter("flight_speed", "飞行速度", "飞行时的移动速度", 0.3, 0.1, 1.0, "倍");
        flightAbility.addParameter("ascent_speed", "上升速度", "缓慢上升的速度", 0.1, 0.05, 0.5, "格/tick");
        flightAbility.addParameter("max_height", "最大高度", "飞行的最大高度", 10.0, 5.0, 50.0, "格");
        skillParameterTemplates.put("flight_ability", flightAbility);

        // 死亡粒子技能
        SkillParameterTemplate deathParticles = new SkillParameterTemplate("death_particles", "死亡粒子");
        deathParticles.addParameter("particle_count", "粒子数量", "死亡时释放的粒子数量", 50, 10, 200, "个");
        deathParticles.addParameter("particle_range", "粒子范围", "粒子散布的范围", 5.0, 2.0, 15.0, "格");
        deathParticles.addParameter("particle_duration", "粒子持续时间", "粒子存在的时间", 100, 40, 400, "tick");
        skillParameterTemplates.put("death_particles", deathParticles);

        // 迷雾生成技能
        SkillParameterTemplate fogGeneration = new SkillParameterTemplate("fog_generation", "迷雾生成");
        fogGeneration.addParameter("fog_range", "迷雾范围", "迷雾的作用范围", 8.0, 3.0, 20.0, "格");
        fogGeneration.addParameter("fog_interval", "迷雾间隔", "生成迷雾的间隔", 120, 60, 600, "tick");
        fogGeneration.addParameter("fog_duration", "迷雾持续时间", "迷雾存在的时间", 200, 100, 1200, "tick");
        fogGeneration.addParameter("blindness_level", "失明等级", "迷雾造成的失明等级", 1, 0, 9, "级");
        skillParameterTemplates.put("fog_generation", fogGeneration);

        // 死亡雾气技能
        SkillParameterTemplate deathFog = new SkillParameterTemplate("death_fog", "死亡雾气");
        deathFog.addParameter("fog_particle_count", "雾气粒子数量", "死亡时释放的雾气粒子数量", 100, 20, 500, "个");
        deathFog.addParameter("fog_range", "雾气范围", "雾气散布的范围", 8.0, 3.0, 20.0, "格");
        deathFog.addParameter("fog_duration", "雾气持续时间", "雾气存在的时间", 150, 60, 600, "tick");
        skillParameterTemplates.put("death_fog", deathFog);

        // 瞬移雷击技能
        SkillParameterTemplate teleportLightning = new SkillParameterTemplate("teleport_lightning", "瞬移雷击");
        teleportLightning.addParameter("teleport_range", "瞬移范围", "瞬移的最大距离", 16.0, 5.0, 50.0, "格");
        teleportLightning.addParameter("lightning_damage", "雷击伤害", "瞬移后雷击造成的伤害", 8.0, 3.0, 25.0, "点");
        teleportLightning.addParameter("skill_interval", "技能间隔", "瞬移雷击的冷却时间", 200, 100, 1200, "tick");
        skillParameterTemplates.put("teleport_lightning", teleportLightning);

        // 雷电召唤技能（复用“怪物ID”选择参数，与死亡召唤/定时召唤一致）
        SkillParameterTemplate lightningSummon = new SkillParameterTemplate("lightning_summon", "雷电召唤");
        lightningSummon.addParameter("summon_count", "召唤数量", "召唤雷电生物的数量", 1, 1, 3, "个");
        lightningSummon.addParameter("summon_interval", "召唤间隔", "雷电召唤的间隔时间", 300, 150, 1800, "tick");
        lightningSummon.addParameter("summon_range", "召唤范围", "雷电生物召唤的范围", 8.0, 3.0, 20.0, "格");
        lightningSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("lightning_summon", lightningSummon);

        // 冻结能力技能
        SkillParameterTemplate freezeAbility = new SkillParameterTemplate("freeze_ability", "冻结能力");
        freezeAbility.addParameter("freeze_duration", "冻结持续时间", "冻结效果持续时间", 100, 40, 600, "tick");
        freezeAbility.addParameter("freeze_range", "冻结范围", "冻结效果的作用范围", 5.0, 2.0, 15.0, "格");
        freezeAbility.addParameter("freeze_interval", "冻结间隔", "冻结能力的冷却时间", 200, 100, 1200, "tick");
        skillParameterTemplates.put("freeze_ability", freezeAbility);

        // 召唤战士技能
        // 移除重复：召唤战士与定时召唤重复，统一使用 timed_summon
        // SkillParameterTemplate summonWarriors = new SkillParameterTemplate("summon_warriors", "召唤战士");
        // summonWarriors.addParameter("warrior_count", "战士数量", "召唤武装战士的数量", 2, 1, 5, "个");
        // summonWarriors.addParameter("summon_interval", "召唤间隔", "召唤战士的间隔时间", 250, 120, 1500, "tick");
        // summonWarriors.addParameter("warrior_equipment", "战士装备等级", "召唤战士的装备等级", 1, 1, 5, "级");
        // skillParameterTemplates.put("summon_warriors", summonWarriors);

        // 光环buff技能
        SkillParameterTemplate auraBuff = new SkillParameterTemplate("aura_buff", "光环buff");
        auraBuff.addParameter("aura_range", "光环范围", "光环增益的作用范围", 8.0, 3.0, 20.0, "格");
        auraBuff.addParameter("strength_bonus", "力量加成", "光环提供的力量加成", 2, 1, 10, "级");
        auraBuff.addParameter("defense_bonus", "防御加成", "光环提供的防御加成", 2, 1, 10, "级");
        skillParameterTemplates.put("aura_buff", auraBuff);

        // 攻击buff（对自身的buff，复用自身增益处理器）
        SkillParameterTemplate attackBuff = new SkillParameterTemplate("attack_buff", "攻击buff");
        attackBuff.addParameter("buff_duration", "持续时间", "获得力量效果的持续时间", 200, 40, 1200, "tick");
        attackBuff.addParameter("buff_amplifier", "等级", "力量效果等级(0=I)", 1, 0, 9, "级");
        skillParameterTemplates.put("attack_buff", attackBuff);

        // 爆炸技能
        SkillParameterTemplate explosionSkill = new SkillParameterTemplate("explosion_skill", "爆炸技能");
        explosionSkill.addParameter("explosion_damage", "爆炸伤害", "爆炸造成的伤害", 8.0, 3.0, 25.0, "点");
        explosionSkill.addParameter("explosion_range", "爆炸范围", "爆炸的作用范围", 4.0, 2.0, 12.0, "格");
        explosionSkill.addParameter("explosion_interval", "爆炸间隔", "爆炸技能的冷却时间", 180, 80, 1000, "tick");
        skillParameterTemplates.put("explosion_skill", explosionSkill);

        // 隐身攻击技能
        SkillParameterTemplate stealthAttack = new SkillParameterTemplate("stealth_attack", "隐身攻击");
        stealthAttack.addParameter("stealth_duration", "隐身持续时间", "隐身状态持续时间", 100, 40, 400, "tick");
        stealthAttack.addParameter("teleport_range", "瞬移范围", "隐身瞬移的距离", 12.0, 5.0, 30.0, "格");
        stealthAttack.addParameter("attack_bonus", "攻击加成", "隐身攻击的伤害加成", 3.0, 1.0, 10.0, "点");
        stealthAttack.addParameter("skill_interval", "技能间隔", "隐身攻击的冷却时间", 250, 120, 1500, "tick");
        skillParameterTemplates.put("stealth_attack", stealthAttack);

        // 持续召唤技能
        SkillParameterTemplate continuousSummon = new SkillParameterTemplate("continuous_summon", "持续召唤");
        continuousSummon.addParameter("summon_interval", "召唤间隔", "持续召唤的间隔时间", 80, 40, 400, "tick");
        continuousSummon.addParameter("summon_count", "每次召唤数量", "每次召唤的数量", 1, 1, 3, "个");
        continuousSummon.addParameter("max_summons", "最大召唤数", "同时存在的最大召唤数", 5, 2, 15, "个");
        // 补充：召唤僵尸类型选择（复用死亡召唤/定时召唤的怪物选择器）
        continuousSummon.addParameter("monster_id", "怪物ID", "ID/IDC/IDZ 自定义ID或快捷选择", "", null, null, "");
        skillParameterTemplates.put("continuous_summon", continuousSummon);

        // 全局debuff技能
        SkillParameterTemplate globalDebuff = new SkillParameterTemplate("global_debuff", "全局debuff");
        globalDebuff.addParameter("debuff_duration", "debuff持续时间", "负面效果持续时间", 200, 100, 1200, "tick");
        globalDebuff.addParameter("debuff_interval", "debuff间隔", "全局debuff的冷却时间", 400, 200, 2400, "tick");
        globalDebuff.addParameter("weakness_level", "虚弱等级", "虚弱效果等级", 2, 1, 9, "级");
        globalDebuff.addParameter("slowness_level", "缓慢等级", "缓慢效果等级", 0, 0, 9, "级");
        globalDebuff.addParameter("poison_level", "剧毒等级", "剧毒效果等级", 1, 0, 9, "级");
        globalDebuff.addParameter("wither_level", "凋零等级", "凋零效果等级", 0, 0, 9, "级");
        // 可选：自定义效果组合，例如 "WEAKNESS:1,SLOWNESS:2,BLINDNESS:0"
        globalDebuff.addParameter("effects", "额外效果(可选)", "逗号分隔药水效果:等级，例如 WEAKNESS:1,SLOWNESS:2", "", null, null, "");
        skillParameterTemplates.put("global_debuff", globalDebuff);

        // 随机召唤技能（新增模板）
        SkillParameterTemplate randomSummon = new SkillParameterTemplate("random_summon", "随机召唤");
        randomSummon.addParameter("summon_interval", "召唤间隔", "随机召唤的间隔时间", 120, 40, 1200, "tick");
        randomSummon.addParameter("summon_count", "每次召唤数量", "每次随机召唤的数量", 1, 1, 5, "个");
        randomSummon.addParameter("summon_range", "召唤范围", "召唤的范围", 6.0, 2.0, 20.0, "格");
        randomSummon.addParameter("max_summons", "最大召唤数", "同时存在的最大召唤数", 8, 2, 30, "个");
        // ID 列表池：以逗号分隔的 idz*/id*/idc*，随机选择
        randomSummon.addParameter("monster_pool", "候选ID池", "逗号分隔的 ID/IDC/IDZ 列表，随机从中召唤", "idz1_scout,id5_runner,idc3_guard", null, null, "");
        // 召唤体速度调整（可选）
        randomSummon.addParameter("speed_multiplier", "速度倍率", "对召唤体应用的速度倍率", 1.0, 0.1, 3.0, "x");
        skillParameterTemplates.put("random_summon", randomSummon);

        // 继续添加IDC系列技能模板
        addIDCSkillTemplates();
    }

    /**
     * 添加IDC系列技能模板
     */
    private void addIDCSkillTemplates() {
        // 范围电流技能
        // 去重：范围电流(area_electric) 与 电击攻击(electric_attack) 语义重复，移除参数模板
        // SkillParameterTemplate areaElectric = new SkillParameterTemplate("area_electric", "范围电流");
        // areaElectric.addParameter("electric_range", "电流范围", "范围电流的作用范围", 10.0, 5.0, 25.0, "格");
        // areaElectric.addParameter("electric_damage", "电流伤害", "电流造成的伤害", 5.0, 2.0, 20.0, "点");
        // areaElectric.addParameter("electric_interval", "电流间隔", "范围电流的间隔时间", 100, 50, 600, "tick");
        // skillParameterTemplates.put("area_electric", areaElectric);

        // 毒液喷射技能
        SkillParameterTemplate poisonSpit = new SkillParameterTemplate("poison_spit", "毒液喷射");
        poisonSpit.addParameter("spit_range", "喷射范围", "毒液喷射的最大距离", 8.0, 3.0, 20.0, "格");
        poisonSpit.addParameter("poison_level", "剧毒等级", "毒液的剧毒等级", 2, 1, 9, "级");
        poisonSpit.addParameter("poison_duration", "剧毒持续时间", "剧毒效果持续时间", 120, 40, 600, "tick");
        poisonSpit.addParameter("spit_interval", "喷射间隔", "毒液喷射的冷却时间", 80, 40, 400, "tick");
        skillParameterTemplates.put("poison_spit", poisonSpit);

        // 粒子球攻击技能
        SkillParameterTemplate particleBall = new SkillParameterTemplate("particle_ball", "粒子球攻击");
        particleBall.addParameter("ball_damage", "粒子球伤害", "粒子球造成的伤害", 6.0, 2.0, 20.0, "点");
        particleBall.addParameter("ball_speed", "粒子球速度", "粒子球飞行速度", 1.5, 0.5, 3.0, "倍");
        particleBall.addParameter("ball_range", "攻击范围", "粒子球攻击的最大范围", 15.0, 5.0, 40.0, "格");
        particleBall.addParameter("attack_interval", "攻击间隔", "粒子球攻击的间隔", 100, 50, 500, "tick");
        skillParameterTemplates.put("particle_ball", particleBall);

        // 唤魔者召唤技能
        SkillParameterTemplate evokerSummonVindicator = new SkillParameterTemplate("evoker_summon_vindicator", "唤魔者召唤");
        evokerSummonVindicator.addParameter("summon_count", "召唤数量", "唤魔者召唤卫道士的数量", 2, 1, 5, "个");
        evokerSummonVindicator.addParameter("summon_interval", "召唤间隔", "唤魔者召唤的间隔时间", 200, 100, 1200, "tick");
        evokerSummonVindicator.addParameter("vindicator_health", "卫道士生命值", "召唤卫道士的生命值", 24.0, 10.0, 100.0, "点");
        skillParameterTemplates.put("evoker_summon_vindicator", evokerSummonVindicator);

        // 尖牙攻击技能
        SkillParameterTemplate fangAttack = new SkillParameterTemplate("fang_attack", "尖牙攻击");
        fangAttack.addParameter("fang_damage", "尖牙伤害", "尖牙攻击造成的伤害", 6.0, 2.0, 20.0, "点");
        fangAttack.addParameter("fang_count", "尖牙数量", "圈式尖牙的数量", 8, 4, 16, "个");
        fangAttack.addParameter("fang_radius", "尖牙半径", "尖牙圈的半径", 4.0, 2.0, 10.0, "格");
        fangAttack.addParameter("attack_interval", "攻击间隔", "尖牙攻击的间隔", 120, 60, 600, "tick");
        skillParameterTemplates.put("fang_attack", fangAttack);

        // 唤魔者光环技能
        SkillParameterTemplate evokerDamageAura = new SkillParameterTemplate("evoker_damage_aura", "唤魔者光环");
        evokerDamageAura.addParameter("aura_damage", "光环伤害", "光环每次造成的伤害", 4.0, 1.0, 15.0, "点");
        evokerDamageAura.addParameter("aura_range", "光环范围", "伤害光环的作用范围", 6.0, 3.0, 20.0, "格");
        evokerDamageAura.addParameter("damage_interval", "伤害间隔", "光环伤害的间隔时间", 50, 20, 200, "tick");
        skillParameterTemplates.put("evoker_damage_aura", evokerDamageAura);

        // 暴击光环技能
        SkillParameterTemplate criticalAura = new SkillParameterTemplate("critical_aura", "暴击光环");
        criticalAura.addParameter("crit_damage", "暴击伤害", "暴击光环造成的伤害", 8.0, 3.0, 25.0, "点");
        criticalAura.addParameter("crit_range", "暴击范围", "暴击光环的作用范围", 5.0, 2.0, 15.0, "格");
        criticalAura.addParameter("crit_interval", "暴击间隔", "暴击光环的间隔时间", 60, 30, 300, "tick");
        criticalAura.addParameter("crit_chance", "暴击概率", "触发暴击的概率", 0.3, 0.1, 1.0, "%");
        skillParameterTemplates.put("critical_aura", criticalAura);

        // 撞击伤害技能
        SkillParameterTemplate collisionDamage = new SkillParameterTemplate("collision_damage", "撞击伤害");
        collisionDamage.addParameter("collision_damage", "撞击伤害", "撞击玩家造成的伤害", 6.0, 2.0, 20.0, "点");
        collisionDamage.addParameter("knockback_force", "击退力度", "撞击的击退力度", 2.0, 0.5, 5.0, "倍");
        collisionDamage.addParameter("collision_range", "撞击范围", "撞击检测的范围", 2.0, 1.0, 5.0, "格");
        skillParameterTemplates.put("collision_damage", collisionDamage);

        // 召唤武装僵尸技能
        SkillParameterTemplate summonArmedZombie = new SkillParameterTemplate("summon_armed_zombie", "召唤武装僵尸");
        summonArmedZombie.addParameter("summon_count", "召唤数量", "召唤武装僵尸的数量", 1, 1, 3, "个");
        summonArmedZombie.addParameter("summon_interval", "召唤间隔", "召唤武装僵尸的间隔", 180, 80, 1000, "tick");
        summonArmedZombie.addParameter("zombie_equipment", "僵尸装备等级", "武装僵尸的装备等级", 2, 1, 5, "级");
        skillParameterTemplates.put("summon_armed_zombie", summonArmedZombie);

        // 马匹飞行技能
        SkillParameterTemplate horseFlight = new SkillParameterTemplate("horse_flight", "马匹飞行");
        horseFlight.addParameter("flight_speed", "飞行速度", "马匹飞行的速度", 0.4, 0.2, 1.0, "倍");
        horseFlight.addParameter("flight_height", "飞行高度", "马匹飞行的高度", 8.0, 3.0, 20.0, "格");
        horseFlight.addParameter("flight_duration", "飞行持续时间", "每次飞行的持续时间", 200, 100, 1200, "tick");
        skillParameterTemplates.put("horse_flight", horseFlight);

        // 火焰光环技能
        SkillParameterTemplate flameRing = new SkillParameterTemplate("flame_ring", "火焰光环");
        flameRing.addParameter("flame_damage", "火焰伤害", "火焰光环造成的伤害", 4.0, 1.0, 15.0, "点");
        flameRing.addParameter("flame_range", "火焰范围", "火焰光环的作用范围", 5.0, 2.0, 15.0, "格");
        flameRing.addParameter("burn_duration", "燃烧持续时间", "火焰效果持续时间", 80, 20, 400, "tick");
        flameRing.addParameter("damage_interval", "伤害间隔", "火焰光环的伤害间隔", 40, 20, 200, "tick");
        skillParameterTemplates.put("flame_ring", flameRing);

        // 召唤强化僵尸技能
        SkillParameterTemplate summonEnhancedZombie = new SkillParameterTemplate("summon_enhanced_zombie", "召唤强化僵尸");
        summonEnhancedZombie.addParameter("summon_count", "召唤数量", "召唤强化僵尸的数量", 1, 1, 3, "个");
        summonEnhancedZombie.addParameter("summon_interval", "召唤间隔", "召唤强化僵尸的间隔", 200, 100, 1200, "tick");
        summonEnhancedZombie.addParameter("zombie_health", "僵尸生命值", "强化僵尸的生命值", 30.0, 15.0, 100.0, "点");
        summonEnhancedZombie.addParameter("zombie_damage", "僵尸攻击力", "强化僵尸的攻击力", 5.0, 2.0, 15.0, "点");
        skillParameterTemplates.put("summon_enhanced_zombie", summonEnhancedZombie);

        // 物品栏混乱技能
        SkillParameterTemplate inventoryChaos = new SkillParameterTemplate("inventory_chaos", "物品栏混乱");
        inventoryChaos.addParameter("chaos_chance", "混乱概率", "攻击时触发混乱的概率", 0.3, 0.1, 1.0, "%");
        inventoryChaos.addParameter("chaos_duration", "混乱持续时间", "物品栏混乱的持续时间", 100, 40, 400, "tick");
        inventoryChaos.addParameter("affected_slots", "影响槽位数", "被混乱的物品栏槽位数", 5, 1, 9, "个");
        skillParameterTemplates.put("inventory_chaos", inventoryChaos);

        // 三连射箭技能
        SkillParameterTemplate tripleArrow = new SkillParameterTemplate("triple_arrow", "三连射箭");
        tripleArrow.addParameter("arrow_damage", "箭矢伤害", "每支箭矢造成的伤害", 4.0, 1.0, 15.0, "点");
        tripleArrow.addParameter("arrow_spread", "箭矢散布", "三连射箭的散布角度", 15.0, 5.0, 45.0, "度");
        tripleArrow.addParameter("attack_interval", "攻击间隔", "三连射箭的间隔", 80, 40, 400, "tick");
        skillParameterTemplates.put("triple_arrow", tripleArrow);

        // 冰霜光环技能
        SkillParameterTemplate frostAura = new SkillParameterTemplate("frost_aura", "冰霜光环");
        frostAura.addParameter("frost_range", "冰霜范围", "冰霜光环的作用范围", 6.0, 3.0, 20.0, "格");
        frostAura.addParameter("slowness_level", "缓慢等级", "冰霜光环的缓慢等级", 2, 1, 9, "级");
        frostAura.addParameter("frost_interval", "冰霜间隔", "冰霜光环的刷新间隔", 40, 20, 200, "tick");
        skillParameterTemplates.put("frost_aura", frostAura);

        // 继续添加更多技能模板
        addFinalSkillTemplates();
    }

    /**
     * 添加最后的技能模板
     */
    private void addFinalSkillTemplates() {
        // 粒子机枪技能
        SkillParameterTemplate particleGun = new SkillParameterTemplate("particle_gun", "粒子机枪");
        particleGun.addParameter("gun_damage", "机枪伤害", "粒子机枪每发的伤害", 2.0, 0.5, 8.0, "点");
        particleGun.addParameter("fire_rate", "射击速度", "粒子机枪的射击速度", 5, 2, 20, "发/秒");
        particleGun.addParameter("gun_range", "射击范围", "粒子机枪的射击范围", 20.0, 8.0, 50.0, "格");
        particleGun.addParameter("burst_duration", "连射持续时间", "每次连射的持续时间", 60, 20, 200, "tick");
        skillParameterTemplates.put("particle_gun", particleGun);

        // 烟雾粒子技能
        SkillParameterTemplate smokeParticles = new SkillParameterTemplate("smoke_particles", "烟雾粒子");
        smokeParticles.addParameter("smoke_range", "烟雾范围", "烟雾粒子的作用范围", 8.0, 3.0, 20.0, "格");
        smokeParticles.addParameter("smoke_duration", "烟雾持续时间", "烟雾存在的时间", 150, 60, 600, "tick");
        smokeParticles.addParameter("blindness_level", "失明等级", "烟雾造成的失明等级", 1, 0, 9, "级");
        smokeParticles.addParameter("smoke_interval", "烟雾间隔", "释放烟雾的间隔", 120, 60, 600, "tick");
        skillParameterTemplates.put("smoke_particles", smokeParticles);

        // 冲刺攻击技能
        SkillParameterTemplate dashAttack = new SkillParameterTemplate("dash_attack", "冲刺攻击");
        dashAttack.addParameter("dash_speed", "冲刺速度", "冲刺时的移动速度", 2.0, 1.0, 5.0, "倍");
        dashAttack.addParameter("dash_damage", "冲刺伤害", "冲刺攻击造成的伤害", 6.0, 2.0, 20.0, "点");
        dashAttack.addParameter("dash_range", "冲刺距离", "冲刺的最大距离", 8.0, 3.0, 20.0, "格");
        dashAttack.addParameter("dash_interval", "冲刺间隔", "冲刺攻击的冷却时间", 150, 80, 800, "tick");
        skillParameterTemplates.put("dash_attack", dashAttack);

        // 红色粒子球技能
        SkillParameterTemplate redParticleBall = new SkillParameterTemplate("red_particle_ball", "红色粒子球");
        redParticleBall.addParameter("ball_damage", "粒子球伤害", "红色粒子球造成的伤害", 7.0, 3.0, 25.0, "点");
        redParticleBall.addParameter("ball_speed", "粒子球速度", "红色粒子球的飞行速度", 1.8, 0.8, 4.0, "倍");
        redParticleBall.addParameter("attack_interval", "攻击间隔", "红色粒子球攻击的间隔", 100, 50, 500, "tick");
        skillParameterTemplates.put("red_particle_ball", redParticleBall);

        // 攻击回血技能
        SkillParameterTemplate attackHeal = new SkillParameterTemplate("attack_heal", "攻击回血");
        attackHeal.addParameter("heal_amount", "回血量", "每次攻击恢复的生命值", 3.0, 1.0, 10.0, "点");
        attackHeal.addParameter("heal_chance", "回血概率", "攻击时触发回血的概率", 0.5, 0.1, 1.0, "%");
        attackHeal.addParameter("max_heal_percent", "最大回血百分比", "回血不超过最大生命值的百分比", 0.8, 0.3, 1.0, "%");
        skillParameterTemplates.put("attack_heal", attackHeal);

        // 移除防猪人化技能 - 这不是真正的技能，而是实体属性
        // SkillParameterTemplate antiZombification = new SkillParameterTemplate("anti_zombification", "防猪人化");
        // antiZombification.addParameter("immunity_duration", "免疫持续时间", "防猪人化免疫的持续时间", 1200, 600, 6000, "tick");
        // antiZombification.addParameter("resistance_level", "抗性等级", "对僵尸化的抗性等级", 3, 1, 10, "级");
        // skillParameterTemplates.put("anti_zombification", antiZombification);

        // 潜影贝子弹技能
        SkillParameterTemplate shulkerBullet = new SkillParameterTemplate("shulker_bullet", "潜影贝子弹");
        shulkerBullet.addParameter("bullet_damage", "子弹伤害", "潜影贝子弹造成的伤害", 4.0, 1.0, 15.0, "点");
        shulkerBullet.addParameter("levitation_duration", "漂浮持续时间", "子弹造成的漂浮效果持续时间", 100, 40, 400, "tick");
        shulkerBullet.addParameter("bullet_speed", "子弹速度", "潜影贝子弹的飞行速度", 1.0, 0.3, 3.0, "倍");
        shulkerBullet.addParameter("attack_interval", "攻击间隔", "潜影贝子弹攻击的间隔", 120, 60, 600, "tick");
        skillParameterTemplates.put("shulker_bullet", shulkerBullet);

        // 潜影贝混乱技能
        SkillParameterTemplate shulkerInventoryChaos = new SkillParameterTemplate("shulker_inventory_chaos", "潜影贝混乱");
        shulkerInventoryChaos.addParameter("chaos_chance", "混乱概率", "子弹命中时触发混乱的概率", 0.6, 0.2, 1.0, "%");
        shulkerInventoryChaos.addParameter("chaos_intensity", "混乱强度", "物品栏混乱的强度", 3, 1, 9, "级");
        skillParameterTemplates.put("shulker_inventory_chaos", shulkerInventoryChaos);

        // 潜影贝隐身技能
        SkillParameterTemplate shulkerInvisibility = new SkillParameterTemplate("shulker_invisibility", "潜影贝隐身");
        shulkerInvisibility.addParameter("invisibility_duration", "隐身持续时间", "隐身状态持续时间", 120, 60, 600, "tick");
        shulkerInvisibility.addParameter("invisibility_interval", "隐身间隔", "隐身能力的冷却时间", 300, 150, 1800, "tick");
        skillParameterTemplates.put("shulker_invisibility", shulkerInvisibility);

        // 雪球弹幕技能
        SkillParameterTemplate snowballBarrage = new SkillParameterTemplate("snowball_barrage", "雪球弹幕");
        snowballBarrage.addParameter("snowball_count", "雪球数量", "每次弹幕的雪球数量", 8, 3, 20, "个");
        snowballBarrage.addParameter("snowball_damage", "雪球伤害", "每个雪球造成的伤害", 2.0, 0.5, 8.0, "点");
        snowballBarrage.addParameter("barrage_interval", "弹幕间隔", "雪球弹幕的间隔", 100, 50, 500, "tick");
        skillParameterTemplates.put("snowball_barrage", snowballBarrage);

        // 雪人冰冻光环技能
        SkillParameterTemplate snowFreezeAura = new SkillParameterTemplate("snow_freeze_aura", "雪人冰冻光环");
        snowFreezeAura.addParameter("freeze_range", "冰冻范围", "雪人冰冻光环的作用范围", 7.0, 3.0, 20.0, "格");
        snowFreezeAura.addParameter("freeze_slowness", "冰冻缓慢等级", "冰冻光环的缓慢等级", 3, 1, 9, "级");
        snowFreezeAura.addParameter("freeze_interval", "冰冻间隔", "冰冻光环的刷新间隔", 30, 10, 100, "tick");
        skillParameterTemplates.put("snow_freeze_aura", snowFreezeAura);

        // IDC18变异铁傀儡 - 草方块伸展攻击技能
        SkillParameterTemplate grassBlockAttack = new SkillParameterTemplate("grass_block_attack", "草方块伸展攻击");
        grassBlockAttack.addParameter("grass_damage", "草方块伤害", "草方块攻击造成的伤害", 44.0, 20.0, 80.0, "点");
        grassBlockAttack.addParameter("grass_range", "攻击范围", "草方块攻击的最大范围", 20.0, 10.0, 40.0, "格");
        grassBlockAttack.addParameter("grass_speed", "草方块速度", "草方块延伸的速度", 1.0, 0.5, 3.0, "格/tick");
        grassBlockAttack.addParameter("slowness_duration", "缓慢持续时间", "命中后禁止移动的时间", 20, 10, 60, "tick");
        grassBlockAttack.addParameter("attack_interval", "攻击间隔", "草方块攻击的冷却时间", 100, 60, 300, "tick");
        skillParameterTemplates.put("grass_block_attack", grassBlockAttack);

        // IDC18变异铁傀儡 - 声波弹攻击技能
        SkillParameterTemplate sonicAttack = new SkillParameterTemplate("sonic_attack", "声波弹攻击");
        sonicAttack.addParameter("sonic_damage", "声波弹伤害", "声波弹造成的伤害", 8.0, 4.0, 20.0, "点");
        sonicAttack.addParameter("sonic_range", "攻击范围", "声波弹攻击的最大范围", 15.0, 8.0, 30.0, "格");
        sonicAttack.addParameter("sonic_speed", "声波弹速度", "声波弹的飞行速度", 1.5, 0.8, 3.0, "倍");
        sonicAttack.addParameter("sonic_interval", "攻击间隔", "声波弹攻击的冷却时间", 80, 40, 200, "tick");
        skillParameterTemplates.put("sonic_attack", sonicAttack);

        // IDC18变异铁傀儡 - 主动追踪玩家技能
        SkillParameterTemplate playerTracking = new SkillParameterTemplate("player_tracking", "主动追踪玩家");
        playerTracking.addParameter("tracking_range", "追踪范围", "主动追踪玩家的范围", 20.0, 10.0, 50.0, "格");
        playerTracking.addParameter("tracking_speed", "追踪速度", "追踪时的移动速度", 1.2, 0.8, 2.0, "倍");
        playerTracking.addParameter("tracking_interval", "追踪间隔", "追踪检测的间隔时间", 20, 10, 60, "tick");
        skillParameterTemplates.put("player_tracking", playerTracking);

        // IDC19变异僵尸Max - 天气控制技能
        SkillParameterTemplate weatherControl = new SkillParameterTemplate("weather_control", "天气控制");
        weatherControl.addParameter("weather_interval", "天气控制间隔", "控制天气的冷却时间", 600, 300, 1800, "tick");
        weatherControl.addParameter("storm_duration", "风暴持续时间", "雷雨天气的持续时间", 400, 200, 1200, "tick");
        weatherControl.addParameter("lightning_chance", "闪电概率", "天气控制时的闪电概率", 0.8, 0.3, 1.0, "%");
        skillParameterTemplates.put("weather_control", weatherControl);

        // IDC19变异僵尸Max - 粒子攻击技能
        SkillParameterTemplate particleAttack = new SkillParameterTemplate("particle_attack", "粒子攻击");
        particleAttack.addParameter("particle_damage", "粒子伤害", "粒子攻击造成的伤害", 6.0, 3.0, 15.0, "点");
        particleAttack.addParameter("particle_range", "攻击范围", "粒子攻击的最大范围", 12.0, 6.0, 25.0, "格");
        particleAttack.addParameter("particle_interval", "攻击间隔", "粒子攻击的冷却时间", 80, 40, 200, "tick");
        particleAttack.addParameter("particle_count", "粒子数量", "每次攻击的粒子数量", 8, 4, 20, "个");
        skillParameterTemplates.put("particle_attack", particleAttack);

        // IDC19变异僵尸Max - 三叉戟攻击技能
        SkillParameterTemplate tridentAttack = new SkillParameterTemplate("trident_attack", "三叉戟攻击");
        tridentAttack.addParameter("trident_damage", "三叉戟伤害", "三叉戟攻击造成的伤害", 10.0, 5.0, 25.0, "点");
        tridentAttack.addParameter("trident_range", "攻击范围", "三叉戟攻击的最大范围", 20.0, 10.0, 40.0, "格");
        tridentAttack.addParameter("trident_interval", "攻击间隔", "三叉戟攻击的冷却时间", 100, 50, 300, "tick");
        tridentAttack.addParameter("loyalty_enabled", "忠诚回归", "三叉戟是否自动回归", true, null, null, "");
        skillParameterTemplates.put("trident_attack", tridentAttack);

        // IDC19变异僵尸Max - 闪电攻击技能
        SkillParameterTemplate lightningAttack = new SkillParameterTemplate("lightning_attack", "闪电攻击");
        lightningAttack.addParameter("lightning_damage", "闪电伤害", "闪电攻击造成的伤害", 12.0, 6.0, 30.0, "点");
        lightningAttack.addParameter("lightning_range", "攻击范围", "闪电攻击的最大范围", 15.0, 8.0, 30.0, "格");
        lightningAttack.addParameter("lightning_interval", "攻击间隔", "闪电攻击的冷却时间", 120, 60, 400, "tick");
        lightningAttack.addParameter("chain_lightning", "连锁闪电", "是否启用连锁闪电效果", false, null, null, "");
        skillParameterTemplates.put("lightning_attack", lightningAttack);

        // IDC19变异僵尸Max - 隐身能力技能
        SkillParameterTemplate invisibilitySkill = new SkillParameterTemplate("invisibility_skill", "隐身能力");
        invisibilitySkill.addParameter("invisibility_duration", "隐身持续时间", "隐身状态的持续时间", 100, 50, 300, "tick");
        invisibilitySkill.addParameter("invisibility_interval", "隐身间隔", "隐身能力的冷却时间", 300, 150, 900, "tick");
        invisibilitySkill.addParameter("invisibility_chance", "隐身概率", "触发隐身的概率", 0.7, 0.3, 1.0, "%");
        skillParameterTemplates.put("invisibility_skill", invisibilitySkill);

        // 移除传送能力技能配置 - 避免与其他传送技能重复
        // SkillParameterTemplate teleportSkill = new SkillParameterTemplate("teleport_skill", "传送能力");
        // teleportSkill.addParameter("teleport_range", "传送范围", "传送的最大范围", 15.0, 8.0, 30.0, "格");
        // teleportSkill.addParameter("teleport_interval", "传送间隔", "传送能力的冷却时间", 200, 100, 600, "tick");
        // teleportSkill.addParameter("teleport_behind", "背后传送", "是否传送到玩家背后", true, null, null, "");
        // skillParameterTemplates.put("teleport_skill", teleportSkill);

        // IDC20灵魂坚守者 - 主动追踪玩家技能
        SkillParameterTemplate playerTrackingIDC20 = new SkillParameterTemplate("player_tracking_idc20", "主动追踪玩家");
        playerTrackingIDC20.addParameter("tracking_range", "追踪范围", "主动追踪玩家的范围", 20.0, 10.0, 50.0, "格");
        playerTrackingIDC20.addParameter("tracking_speed", "追踪速度", "追踪时的移动速度", 1.3, 0.8, 2.5, "倍");
        playerTrackingIDC20.addParameter("tracking_interval", "追踪间隔", "追踪检测的间隔时间", 5, 3, 20, "tick");
        skillParameterTemplates.put("player_tracking_idc20", playerTrackingIDC20);

        // IDC20灵魂坚守者 - 声波弹攻击技能（与IDC18区分）
        SkillParameterTemplate sonicAttackIDC20 = new SkillParameterTemplate("sonic_attack_idc20", "声波弹攻击");
        sonicAttackIDC20.addParameter("sonic_damage", "声波弹伤害", "声波弹造成的伤害", 12.0, 6.0, 30.0, "点");
        sonicAttackIDC20.addParameter("sonic_range", "攻击范围", "声波弹攻击的最大范围", 18.0, 10.0, 35.0, "格");
        sonicAttackIDC20.addParameter("sonic_speed", "声波弹速度", "声波弹的飞行速度", 2.0, 1.0, 4.0, "倍");
        sonicAttackIDC20.addParameter("sonic_interval", "攻击间隔", "声波弹攻击的冷却时间", 60, 30, 200, "tick");
        skillParameterTemplates.put("sonic_attack_idc20", sonicAttackIDC20);

        // IDC20灵魂坚守者 - 黑曜石方块攻击技能
        SkillParameterTemplate obsidianAttack = new SkillParameterTemplate("obsidian_attack", "黑曜石方块攻击");
        obsidianAttack.addParameter("obsidian_damage", "黑曜石伤害", "黑曜石攻击造成的伤害", 15.0, 8.0, 35.0, "点");
        obsidianAttack.addParameter("obsidian_range", "攻击范围", "黑曜石攻击的最大范围", 20.0, 10.0, 40.0, "格");
        obsidianAttack.addParameter("obsidian_speed", "黑曜石速度", "黑曜石延伸的速度", 1.2, 0.6, 2.5, "格/tick");
        obsidianAttack.addParameter("slowness_duration", "缓慢持续时间", "命中后禁止移动的时间", 40, 20, 100, "tick");
        obsidianAttack.addParameter("obsidian_interval", "攻击间隔", "黑曜石攻击的冷却时间", 100, 60, 300, "tick");
        skillParameterTemplates.put("obsidian_attack", obsidianAttack);

        // IDC20灵魂坚守者 - 击飞黑曜石柱技能
        SkillParameterTemplate knockupPillar = new SkillParameterTemplate("knockup_pillar", "击飞黑曜石柱");
        knockupPillar.addParameter("knockup_strength", "击飞强度", "向上击飞的力度", 1.5, 0.8, 3.0, "倍");
        knockupPillar.addParameter("pillar_damage", "柱子伤害", "击飞时造成的伤害", 20.0, 10.0, 50.0, "点");
        knockupPillar.addParameter("pillar_height", "柱子高度", "黑曜石柱的高度", 5, 3, 10, "格");
        knockupPillar.addParameter("knockup_range", "攻击范围", "击飞攻击的最大范围", 15.0, 8.0, 30.0, "格");
        knockupPillar.addParameter("knockup_interval", "攻击间隔", "击飞攻击的冷却时间", 150, 80, 400, "tick");
        skillParameterTemplates.put("knockup_pillar", knockupPillar);

        // 移除重复的黑曜石攻击和击飞黑曜石柱技能定义，已在IDC20部分定义
        // SkillParameterTemplate obsidianAttack = new SkillParameterTemplate("obsidian_attack", "黑曜石攻击");
        // obsidianAttack.addParameter("obsidian_damage", "黑曜石伤害", "黑曜石方块造成的伤害", 10.0, 4.0, 30.0, "点");
        // obsidianAttack.addParameter("block_count", "方块数量", "每次攻击的黑曜石方块数量", 3, 1, 10, "个");
        // obsidianAttack.addParameter("attack_interval", "攻击间隔", "黑曜石攻击的间隔", 120, 60, 600, "tick");
        // skillParameterTemplates.put("obsidian_attack", obsidianAttack);

        // SkillParameterTemplate knockupPillar = new SkillParameterTemplate("knockup_pillar", "击飞黑曜石柱");
        // knockupPillar.addParameter("knockup_force", "击飞力度", "向上击飞的力度", 3.0, 1.0, 8.0, "倍");
        // knockupPillar.addParameter("pillar_height", "柱子高度", "黑曜石柱的高度", 5, 2, 15, "格");
        // knockupPillar.addParameter("pillar_damage", "柱子伤害", "黑曜石柱造成的伤害", 8.0, 3.0, 25.0, "点");
        // knockupPillar.addParameter("skill_interval", "技能间隔", "击飞黑曜石柱的冷却时间", 200, 100, 1200, "tick");
        // skillParameterTemplates.put("knockup_pillar", knockupPillar);

        // 移除错误的凋零光环技能配置（原版IDC21没有凋零光环）
        // SkillParameterTemplate witherAura = new SkillParameterTemplate("wither_aura", "凋零光环");
        // skillParameterTemplates.put("wither_aura", witherAura);

        // 凋零头颅攻击技能
        SkillParameterTemplate skullAttack = new SkillParameterTemplate("skull_attack", "凋零头颅攻击");
        skullAttack.addParameter("skull_damage", "头颅伤害", "凋零头颅造成的伤害", 12.0, 5.0, 35.0, "点");
        skullAttack.addParameter("skull_speed", "头颅速度", "凋零头颅的飞行速度", 2.0, 0.8, 5.0, "倍");
        skullAttack.addParameter("skull_count", "头颅数量", "每次攻击的头颅数量", 3, 1, 8, "个");
        skullAttack.addParameter("attack_interval", "攻击间隔", "凋零头颅攻击的间隔", 100, 50, 500, "tick");
        skillParameterTemplates.put("skull_attack", skullAttack);

        // 下界栅栏攻击技能
        SkillParameterTemplate fenceAttack = new SkillParameterTemplate("fence_attack", "下界栅栏攻击");
        fenceAttack.addParameter("fence_damage", "栅栏伤害", "下界栅栏造成的伤害", 6.0, 2.0, 20.0, "点");
        fenceAttack.addParameter("fence_count", "栅栏数量", "每次攻击的栅栏数量", 5, 2, 15, "个");
        fenceAttack.addParameter("fence_duration", "栅栏持续时间", "下界栅栏存在的时间", 200, 100, 1200, "tick");
        fenceAttack.addParameter("fence_slowness_level", "减速等级", "栅栏攻击的减速等级", 2, 1, 5, "级");
        fenceAttack.addParameter("fence_slowness_duration", "减速持续时间", "减速效果的持续时间", 60, 20, 200, "tick");
        fenceAttack.addParameter("fence_ray_length", "射线长度", "栅栏射线的长度", 15, 5, 30, "格");
        fenceAttack.addParameter("fence_restore_delay", "恢复延迟", "栅栏恢复的延迟时间", 40, 20, 200, "tick");
        fenceAttack.addParameter("attack_interval", "攻击间隔", "下界栅栏攻击的间隔", 150, 80, 800, "tick");
        skillParameterTemplates.put("fence_attack", fenceAttack);

        // 王者黑曜石攻击技能
        SkillParameterTemplate kingObsidianAttack = new SkillParameterTemplate("king_obsidian_attack", "王者黑曜石攻击");
        kingObsidianAttack.addParameter("obsidian_damage", "黑曜石伤害", "王者黑曜石块造成的伤害", 15.0, 6.0, 40.0, "点");
        kingObsidianAttack.addParameter("block_count", "方块数量", "每次攻击的黑曜石方块数量", 5, 2, 15, "个");
        kingObsidianAttack.addParameter("explosion_power", "爆炸威力", "黑曜石块的爆炸威力", 2.0, 1.0, 6.0, "级");
        kingObsidianAttack.addParameter("attack_interval", "攻击间隔", "王者黑曜石攻击的间隔", 100, 50, 500, "tick");
        skillParameterTemplates.put("king_obsidian_attack", kingObsidianAttack);

        // 黑曜石柱攻击技能
        SkillParameterTemplate pillarAttack = new SkillParameterTemplate("pillar_attack", "黑曜石柱攻击");
        pillarAttack.addParameter("pillar_damage", "柱子伤害", "黑曜石柱造成的伤害", 12.0, 5.0, 35.0, "点");
        pillarAttack.addParameter("pillar_height", "柱子高度", "黑曜石柱的高度", 8, 3, 20, "格");
        pillarAttack.addParameter("pillar_count", "柱子数量", "每次攻击的柱子数量", 3, 1, 8, "个");
        pillarAttack.addParameter("attack_interval", "攻击间隔", "黑曜石柱攻击的间隔", 120, 60, 600, "tick");
        skillParameterTemplates.put("pillar_attack", pillarAttack);

        // 末影龙飞行技能
        SkillParameterTemplate dragonFlight = new SkillParameterTemplate("dragon_flight", "末影龙飞行");
        dragonFlight.addParameter("flight_speed", "飞行速度", "末影龙飞行的速度", 0.8, 0.3, 2.0, "倍");
        dragonFlight.addParameter("flight_height", "飞行高度", "末影龙飞行的高度", 15.0, 8.0, 50.0, "格");
        dragonFlight.addParameter("dive_damage", "俯冲伤害", "俯冲攻击造成的伤害", 20.0, 8.0, 50.0, "点");
        skillParameterTemplates.put("dragon_flight", dragonFlight);

        // 次元控制技能
        SkillParameterTemplate dimensionControl = new SkillParameterTemplate("dimension_control", "次元控制");
        dimensionControl.addParameter("portal_count", "传送门数量", "次元控制创造的传送门数量", 2, 1, 5, "个");
        dimensionControl.addParameter("portal_duration", "传送门持续时间", "传送门存在的时间", 300, 150, 1800, "tick");
        dimensionControl.addParameter("teleport_damage", "传送伤害", "通过传送门造成的伤害", 10.0, 4.0, 30.0, "点");
        dimensionControl.addParameter("control_interval", "控制间隔", "次元控制的冷却时间", 600, 300, 3600, "tick");
        skillParameterTemplates.put("dimension_control", dimensionControl);

        // 终极力量技能
        SkillParameterTemplate ultimatePower = new SkillParameterTemplate("ultimate_power", "终极力量");
        ultimatePower.addParameter("power_multiplier", "力量倍数", "终极力量的全属性倍数", 5.0, 2.0, 15.0, "倍");
        ultimatePower.addParameter("power_duration", "力量持续时间", "终极力量的持续时间", 400, 200, 2400, "tick");
        ultimatePower.addParameter("power_interval", "力量间隔", "终极力量的冷却时间", 1200, 600, 7200, "tick");
        ultimatePower.addParameter("invulnerability", "无敌时间", "终极力量激活时的无敌时间", 100, 40, 400, "tick");
        skillParameterTemplates.put("ultimate_power", ultimatePower);
    }

    /**
     * 打开技能配置编辑器
     */
    public void openSkillConfigEditor(Player player, String monsterId, String skillId) {
        logger.info("开始打开技能配置编辑器 - 玩家: " + player.getName() + ", 怪物ID: " + monsterId + ", 技能ID: " + skillId);

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            logger.info("IDZ怪物配置不存在: " + monsterId);
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }

        // 转换技能ID - 移除前缀以匹配模板
        String templateId = convertSkillIdToTemplateId(skillId);
        logger.info("技能ID转换: " + skillId + " -> " + templateId);

        SkillParameterTemplate template = skillParameterTemplates.get(templateId);
        if (template == null) {
            logger.info("技能模板不存在 - 原始ID: " + skillId + ", 模板ID: " + templateId);
            logger.info("可用的技能模板: " + skillParameterTemplates.keySet());
            player.sendMessage(ChatColor.RED + "技能模板不存在: " + skillId + " (模板ID: " + templateId + ")");
            player.sendMessage(ChatColor.GRAY + "该技能暂不支持参数配置");
            return;
        }

        editingMonster.put(player, monsterId);
        editingSkill.put(player, skillId); // 保存原始技能ID

        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE + " - " + template.skillName);

        // 填充背景
        fillBackground(gui);

        // 设置技能参数编辑按钮（按当前技能ID读取参数）
        setupParameterButtons(gui, config, template, skillId);

        // 设置控制按钮
        setupControlButtons(gui);

        player.openInventory(gui);

        logger.info("为玩家 " + player.getName() + " 打开技能配置编辑器: " + skillId);
    }

    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.CYAN_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);

        // 填充边框
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, background);
            gui.setItem(i + 45, background);
        }
        for (int i = 0; i < 54; i += 9) {
            gui.setItem(i, background);
            gui.setItem(i + 8, background);
        }
    }

    /**
     * 设置参数编辑按钮
     */
    private void setupParameterButtons(Inventory gui, IDZMonsterConfig config, SkillParameterTemplate template, String currentSkillId) {
        // 1) 优先使用新结构：找到该技能的 SkillConfig，并取其参数
        Map<String, Object> skillParams = new HashMap<>();
        SkillConfig sc = config.getSkillConfig(currentSkillId);
        if (sc != null) {
            skillParams.putAll(sc.getParameters());
        } else {
            // 2) 兼容旧结构：从聚合映射中过滤出当前技能前缀参数
            Map<String, Object> legacy = config.getSkillParameters();
            String prefix = currentSkillId + ".";
            for (Map.Entry<String, Object> e : legacy.entrySet()) {
                if (e.getKey().startsWith(prefix)) {
                    skillParams.put(e.getKey().substring(prefix.length()), e.getValue());
                }
            }
        }

        int slot = 10;
        for (Map.Entry<String, ParameterInfo> entry : template.parameters.entrySet()) {
            String paramKey = entry.getKey();
            ParameterInfo paramInfo = entry.getValue();

            // 获取当前参数值
            Object currentValue = skillParams.getOrDefault(paramKey, paramInfo.defaultValue);

            // 创建参数编辑按钮
            ItemStack paramButton = createParameterButton(paramKey, paramInfo, currentValue);
            gui.setItem(slot, paramButton);

            slot++;
            if (slot % 9 == 8) slot += 2; // 跳过边框
            if (slot >= 44) break; // 避免超出界面
        }
    }

    /**
     * 创建参数编辑按钮
     */
    private ItemStack createParameterButton(String paramKey, ParameterInfo paramInfo, Object currentValue) {
        Material material = getParameterMaterial(paramKey);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName("§e" + paramInfo.name);

        List<String> lore = new ArrayList<>();
        lore.add("§7" + paramInfo.description);
        lore.add("§7当前值: §a" + formatValue(currentValue, paramInfo.unit));
        lore.add("§7默认值: §7" + formatValue(paramInfo.defaultValue, paramInfo.unit));

        // 显示统一的自定义范围
        if (currentValue instanceof Integer || paramInfo.defaultValue instanceof Integer) {
            lore.add("§7范围: §e-1§7(永久) 或 §e0§7 - §e无限制");
        } else if (currentValue instanceof Double || paramInfo.defaultValue instanceof Double) {
            lore.add("§7范围: §e-1.0§7(永久) 或 §e0.0§7 - §e无限制");
        }

        lore.add("");
        lore.add("§e点击修改此参数");

        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * 获取参数对应的材质
     */
    private Material getParameterMaterial(String paramKey) {
        switch (paramKey.toLowerCase()) {
            case "poison_level":
            case "poison_duration":
            case "poison_chance":
                return Material.SPIDER_EYE;
            case "summon_count":
            case "summon_range":
            case "summon_interval":
            case "monster_id":
                return Material.ZOMBIE_SPAWN_EGG;
            case "arrow_damage":
            case "arrow_speed":
            case "attack_range":
                return Material.ARROW;
            case "lightning_damage":
            case "lightning_range":
            case "lightning_interval":
                return Material.LIGHTNING_ROD;
            case "flame_damage":
            case "flame_interval":
            case "flame_range":
                return Material.FIRE_CHARGE;
            case "slowness_level":
            case "slowness_duration":
            case "freeze_chance":
                return Material.ICE;
            case "teleport_range":
            case "teleport_interval":
            case "teleport_damage":
                return Material.ENDER_PEARL;
            case "aura_damage":
            case "aura_range":
            case "aura_interval":
                return Material.BEACON;
            case "time_slow_duration":
            case "time_slow_interval":
            case "slow_multiplier":
                return Material.CLOCK;
            default:
                return Material.PAPER;
        }
    }

    /**
     * 格式化数值显示
     */
    private String formatValue(Object value, String unit) {
        if (value instanceof Double) {
            return String.format("%.1f%s", (Double) value, unit);
        } else if (value instanceof Integer) {
            return value + unit;
        } else if (value instanceof Boolean) {
            return (Boolean) value ? "§a启用" : "§c禁用";
        } else {
            return value.toString() + unit;
        }
    }

    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回技能选择",
            Arrays.asList("§7返回技能选择界面"));
        gui.setItem(49, backButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存配置",
            Arrays.asList("§7保存当前技能配置"));
        gui.setItem(53, saveButton);
    }

    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        // 添加调试信息
        logger.info("SkillConfigGUI点击事件 - 玩家: " + player.getName() + ", 标题: '" + title + "', 槽位: " + event.getRawSlot());

        if (!title.startsWith(GUI_TITLE) && !title.startsWith("§6输入")) {
            logger.info("标题不匹配，忽略事件");
            return;
        }

        event.setCancelled(true);
        logger.info("事件已取消，开始处理");

        int slot = event.getRawSlot();
        String monsterId = editingMonster.get(player);
        String skillId = editingSkill.get(player);

        if (monsterId == null || skillId == null) {
            return;
        }

        if (title.startsWith("§6输入")) {
            // 处理铁砧输入界面
            logger.info("处理铁砧输入界面点击");
            handleAnvilInputClick(player, slot, title);
        } else if (slot == 49) { // 返回按钮
            logger.info("点击返回按钮");
            returnToSkillSelector(player, monsterId);
        } else if (slot == 53) { // 保存按钮
            logger.info("点击保存按钮");
            saveSkillConfig(player, monsterId);
        } else if (slot >= 10 && slot <= 43 && slot % 9 != 0 && slot % 9 != 8) {
            // 参数编辑区域
            logger.info("点击参数编辑区域 - 槽位: " + slot);
            handleParameterEdit(player, slot, monsterId, skillId);
        } else {
            logger.info("点击位置不在处理范围内 - 槽位: " + slot);
        }
    }

    /**
     * 处理参数编辑
     */
    private void handleParameterEdit(Player player, int slot, String monsterId, String skillId) {
        logger.info("开始处理参数编辑 - 技能ID: " + skillId);

        // 转换技能ID
        String templateId = convertSkillIdToTemplateId(skillId);
        logger.info("技能ID转换: " + skillId + " -> " + templateId);

        SkillParameterTemplate template = skillParameterTemplates.get(templateId);
        if (template == null) {
            logger.info("技能模板不存在: " + templateId);
            return;
        }

        // 计算参数索引
        int row = (slot - 10) / 9;
        int col = (slot - 10) % 9;
        logger.info("参数槽位计算 - 槽位: " + slot + ", 行: " + row + ", 列: " + col);

        // 检查是否在有效的参数区域（槽位10-16是第一行参数）
        if (slot % 9 == 0 || slot % 9 == 8) {
            logger.info("点击边框位置，忽略");
            return; // 边框位置
        }

        // 重新计算参数索引，槽位10开始就是参数
        int paramIndex = row * 7 + col;
        String[] paramKeys = template.parameters.keySet().toArray(new String[0]);
        logger.info("参数索引计算: " + paramIndex + ", 可用参数数量: " + paramKeys.length);

        if (paramIndex >= paramKeys.length) {
            logger.info("参数索引超出范围，忽略");
            return;
        }

        String paramKey = paramKeys[paramIndex];
        ParameterInfo paramInfo = template.parameters.get(paramKey);
        logger.info("选择的参数: " + paramKey + " (" + paramInfo.name + ")");

        // 对死亡召唤的怪物选择参数，打开选择器GUI
        if ("monster_id".equalsIgnoreCase(paramKey)) {
            logger.info("打开怪物选择器GUI用于参数: " + paramKey);
            monsterIdPickerGUI.open(player, monsterId, skillId, paramKey);
            return;
        }

        // 请求用户输入
        requestParameterInput(player, paramKey, paramInfo, monsterId, skillId);
    }

    /**
     * 请求参数输入 - 使用铁砧界面
     */
    private void requestParameterInput(Player player, String paramKey, ParameterInfo paramInfo, String monsterId, String skillId) {
        logger.info("请求参数输入 - 参数: " + paramKey + " (" + paramInfo.name + ")");

        waitingForInput.put(player, player.getName());
        inputType.put(player, paramKey);

        logger.info("已设置玩家等待输入状态 - 玩家: " + player.getName() + ", 参数: " + paramKey);
        logger.info("当前等待输入的玩家: " + waitingForInput.keySet());

        player.closeInventory();
        logger.info("已关闭当前GUI，使用聊天输入");

        // 使用聊天输入
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "=== 参数配置 ===");
        player.sendMessage(ChatColor.YELLOW + "请输入新的 " + ChatColor.AQUA + paramInfo.name + ChatColor.YELLOW + ":");

        // 获取当前配置的值
        Object currentValue = getCurrentParameterValue(monsterId, skillId, paramKey, paramInfo.defaultValue);
        player.sendMessage(ChatColor.GRAY + "当前值: " + ChatColor.GREEN + formatValue(currentValue, paramInfo.unit));

        // 显示统一的自定义范围
        if (paramInfo.defaultValue instanceof Integer) {
            player.sendMessage(ChatColor.GRAY + "有效范围: " + ChatColor.YELLOW + "-1" + ChatColor.GRAY + "(永久) 或 " +
                             ChatColor.YELLOW + "0" + ChatColor.GRAY + " - " + ChatColor.YELLOW + "无限制");
        } else if (paramInfo.defaultValue instanceof Double) {
            player.sendMessage(ChatColor.GRAY + "有效范围: " + ChatColor.YELLOW + "-1.0" + ChatColor.GRAY + "(永久) 或 " +
                             ChatColor.YELLOW + "0.0" + ChatColor.GRAY + " - " + ChatColor.YELLOW + "无限制");
        } else if (paramInfo.defaultValue instanceof Boolean) {
            player.sendMessage(ChatColor.GRAY + "有效输入: " + ChatColor.YELLOW + "true" + ChatColor.GRAY + " | " + ChatColor.YELLOW + "false");
        }

        player.sendMessage(ChatColor.GRAY + "输入 " + ChatColor.RED + "'cancel'" + ChatColor.GRAY + " 取消修改");
        player.sendMessage(ChatColor.GOLD + "================");
        player.sendMessage("");
    }

    /**
     * 打开铁砧输入界面
     */
    private void openAnvilInputGUI(Player player, String paramKey, ParameterInfo paramInfo) {
        logger.info("开始创建真实铁砧输入界面 - 参数: " + paramInfo.name);

        // 使用模拟铁砧界面（9个槽位，前3个模拟铁砧）
        try {
            // 创建模拟铁砧界面
            Inventory anvilGUI = Bukkit.createInventory(null, 9, "§6输入 " + paramInfo.name);
            logger.info("模拟铁砧界面已创建，标题: §6输入 " + paramInfo.name);

        // 设置输入物品（纸张，显示当前值）
        logger.info("开始创建输入物品");
        ItemStack inputItem = new ItemStack(Material.PAPER);
        logger.info("纸张物品已创建");

        ItemMeta inputMeta = inputItem.getItemMeta();
        logger.info("获取物品元数据");

        try {
            String displayName = "§e" + formatValue(paramInfo.defaultValue, paramInfo.unit);
            logger.info("设置显示名称: " + displayName);
            inputMeta.setDisplayName(displayName);

            List<String> inputLore = new ArrayList<>();
            inputLore.add("§7参数: §e" + paramInfo.name);
            inputLore.add("§7当前值: §a" + formatValue(paramInfo.defaultValue, paramInfo.unit));
            if (paramInfo.minValue != null && paramInfo.maxValue != null) {
                inputLore.add("§7范围: §e" + formatValue(paramInfo.minValue, paramInfo.unit) +
                             " §7- §e" + formatValue(paramInfo.maxValue, paramInfo.unit));
            }
            inputLore.add("");
            inputLore.add("§7请在铁砧界面重命名此物品");
            inputLore.add("§7输入新的数值，然后点击结果");
            logger.info("设置物品描述");
            inputMeta.setLore(inputLore);
            inputItem.setItemMeta(inputMeta);
            logger.info("输入物品创建完成");
        } catch (Exception e) {
            logger.severe("创建输入物品时出错: " + e.getMessage());
            e.printStackTrace();
            return;
        }

        // 设置到铁砧的第一个输入槽位
        anvilGUI.setItem(0, inputItem);

        // 在铁砧的结果槽位设置一个提示物品
        ItemStack resultItem = new ItemStack(Material.PAPER);
        ItemMeta resultMeta = resultItem.getItemMeta();
        resultMeta.setDisplayName("§e" + formatValue(paramInfo.defaultValue, paramInfo.unit));
        resultMeta.setLore(Arrays.asList(
            "§7重命名左侧物品来输入新数值",
            "§7然后点击这里确认修改"
        ));
        resultItem.setItemMeta(resultMeta);
        anvilGUI.setItem(2, resultItem); // 铁砧的结果槽位

            logger.info("准备打开真实铁砧界面给玩家: " + player.getName());
            player.openInventory(anvilGUI);
            logger.info("真实铁砧界面已打开");

            player.sendMessage(ChatColor.YELLOW + "请在铁砧界面重命名左侧物品来输入新的 " + paramInfo.name);
            player.sendMessage(ChatColor.GRAY + "当前值: " + formatValue(paramInfo.defaultValue, paramInfo.unit));
            if (paramInfo.minValue != null && paramInfo.maxValue != null) {
                player.sendMessage(ChatColor.GRAY + "有效范围: " + formatValue(paramInfo.minValue, paramInfo.unit) +
                                 " - " + formatValue(paramInfo.maxValue, paramInfo.unit));
            }
            player.sendMessage(ChatColor.GRAY + "重命名后点击右侧结果确认修改");

        } catch (Exception e) {
            logger.severe("创建真实铁砧界面时出错: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "无法打开铁砧输入界面，请稍后重试");
        }
    }

    /**
     * 处理铁砧输入界面点击
     */
    private void handleAnvilInputClick(Player player, int slot, String title) {
        if (slot == 2) { // 铁砧的结果槽位
            // 获取重命名后的物品
            ItemStack resultItem = player.getOpenInventory().getItem(0);
            if (resultItem != null && resultItem.hasItemMeta() && resultItem.getItemMeta().hasDisplayName()) {
                String newValue = resultItem.getItemMeta().getDisplayName();
                // 移除颜色代码
                newValue = ChatColor.stripColor(newValue);

                String paramKey = inputType.get(player);
                String monsterId = editingMonster.get(player);
                String skillId = editingSkill.get(player);

                if (paramKey != null && monsterId != null && skillId != null) {
                    processAnvilParameterInput(player, paramKey, newValue, monsterId, skillId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "请先重命名物品来输入新的数值！");
            }
        }
    }

    /**
     * 处理铁砧参数输入
     */
    private void processAnvilParameterInput(Player player, String paramKey, String input, String monsterId, String skillId) {
        String templateId = convertSkillIdToTemplateId(skillId);
        SkillParameterTemplate template = skillParameterTemplates.get(templateId);
        if (template == null) return;

        ParameterInfo paramInfo = template.parameters.get(paramKey);
        if (paramInfo == null) return;

        try {
            Object newValue = parseInputValue(input, paramInfo);

            // 验证数值范围
            if (!validateParameterValue(newValue, paramInfo)) {
                player.sendMessage(ChatColor.RED + "数值超出有效范围！");
                player.sendMessage(ChatColor.GRAY + "有效范围: " + formatValue(paramInfo.minValue, paramInfo.unit) +
                                 " - " + formatValue(paramInfo.maxValue, paramInfo.unit));
                return;
            }

            // 更新配置 - 使用新的SkillConfig结构
            IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config != null) {
                // 获取技能配置
                SkillConfig skillConfig = config.getSkillConfig(skillId);
                if (skillConfig != null) {
                    // 直接更新SkillConfig中的参数
                    skillConfig.setParameter(paramKey, newValue);
                    idzManager.updateMonsterConfig(monsterId, config);

                    player.sendMessage(ChatColor.GREEN + paramInfo.name + " 已设置为: " + formatValue(newValue, paramInfo.unit));

                    // 清理输入状态
                    waitingForInput.remove(player);
                    inputType.remove(player);

                    // 重新打开技能配置界面
                    Bukkit.getScheduler().runTask(plugin, () -> openSkillConfigEditor(player, monsterId, skillId));
                } else {
                    player.sendMessage(ChatColor.RED + "技能配置不存在！");
                }
            }

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "输入格式错误！请输入有效的数值。");
            player.sendMessage(ChatColor.GRAY + "示例: " + formatValue(paramInfo.defaultValue, paramInfo.unit));
        }
    }

    /**
     * 处理聊天输入（保留作为备用）
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();

        logger.info("聊天事件触发 - 玩家: " + player.getName() + ", 消息: " + event.getMessage());
        logger.info("当前等待输入的玩家数量: " + waitingForInput.size());
        logger.info("玩家是否在等待输入: " + waitingForInput.containsKey(player));

        if (!waitingForInput.containsKey(player)) {
            logger.info("玩家不在等待输入状态，忽略聊天");
            return;
        }

        logger.info("开始处理玩家聊天输入");

        event.setCancelled(true);

        String input = event.getMessage().trim();
        String paramKey = inputType.get(player);
        String monsterId = editingMonster.get(player);
        String skillId = editingSkill.get(player);

        // 清理输入状态
        waitingForInput.remove(player);
        inputType.remove(player);

        if ("cancel".equalsIgnoreCase(input)) {
            player.sendMessage(ChatColor.GRAY + "已取消修改");
            // 重新打开GUI
            Bukkit.getScheduler().runTask(plugin, () -> openSkillConfigEditor(player, monsterId, skillId));
            return;
        }

        // 处理参数输入
        processParameterInput(player, paramKey, input, monsterId, skillId);
    }

    /**
     * 处理参数输入
     */
    private void processParameterInput(Player player, String paramKey, String input, String monsterId, String skillId) {
        logger.info("开始处理参数输入 - 玩家: " + player.getName() + ", 参数: " + paramKey + ", 输入: " + input);
        logger.info("原始技能ID: " + skillId);

        // 转换技能ID（只对带ID前缀的技能进行转换）
        String baseSkillId = skillId;
        if (skillId.startsWith("id") && skillId.contains("_")) {
            String[] parts = skillId.split("_", 2);
            if (parts.length > 1) {
                baseSkillId = parts[1];
                logger.info("技能ID转换: " + skillId + " -> " + baseSkillId);
            }
        } else if (skillId.startsWith("idc") && skillId.contains("_")) {
            String[] parts = skillId.split("_", 2);
            if (parts.length > 1) {
                baseSkillId = parts[1];
                logger.info("技能ID转换: " + skillId + " -> " + baseSkillId);
            }
        } else {
            logger.info("技能ID无需转换: " + skillId);
        }

        SkillParameterTemplate template = skillParameterTemplates.get(baseSkillId);
        if (template == null) {
            logger.warning("找不到技能模板: " + baseSkillId + " (原始ID: " + skillId + ")");
            logger.info("可用的技能模板: " + skillParameterTemplates.keySet());
            return;
        }
        logger.info("找到技能模板: " + baseSkillId);

        ParameterInfo paramInfo = template.parameters.get(paramKey);
        if (paramInfo == null) {
            logger.warning("找不到参数信息: " + paramKey + " 在技能: " + baseSkillId);
            logger.info("该技能可用的参数: " + template.parameters.keySet());
            return;
        }

        try {
            logger.info("开始解析输入值: " + input);
            Object newValue = parseInputValue(input, paramInfo);
            logger.info("解析结果: " + newValue + " (类型: " + newValue.getClass().getSimpleName() + ")");

            // 验证数值范围
            logger.info("开始验证数值范围");
            if (!validateParameterValue(newValue, paramInfo)) {
                logger.info("数值验证失败");
                player.sendMessage(ChatColor.RED + "数值超出有效范围！");
                player.sendMessage(ChatColor.GRAY + "有效范围: " + formatValue(paramInfo.minValue, paramInfo.unit) +
                                 " - " + formatValue(paramInfo.maxValue, paramInfo.unit));
                Bukkit.getScheduler().runTask(plugin, () -> openSkillConfigEditor(player, monsterId, skillId));
                return;
            }
            logger.info("数值验证通过");

            // 更新配置 - 使用新的SkillConfig结构
            logger.info("开始更新配置");
            IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config != null) {
                // 获取技能配置
                SkillConfig skillConfig = config.getSkillConfig(skillId);
                if (skillConfig != null) {
                    // 直接更新SkillConfig中的参数
                    skillConfig.setParameter(paramKey, newValue);
                    idzManager.updateMonsterConfig(monsterId, config);

                    logger.info("配置更新成功 - 技能: " + skillId + ", 参数: " + paramKey + ", 值: " + newValue);
                    player.sendMessage(ChatColor.GREEN + paramInfo.name + " 已设置为: " + formatValue(newValue, paramInfo.unit));
                } else {
                    logger.warning("找不到技能配置: " + skillId);
                    player.sendMessage(ChatColor.RED + "技能配置不存在！");
                }
            } else {
                logger.warning("找不到怪物配置: " + monsterId);
            }

        } catch (Exception e) {
            logger.severe("处理参数输入时出错: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "输入格式错误！请输入有效的数值。");
            player.sendMessage(ChatColor.GRAY + "示例: " + formatValue(paramInfo.defaultValue, paramInfo.unit));
        }

        // 重新打开GUI
        logger.info("准备重新打开技能配置界面");
        Bukkit.getScheduler().runTask(plugin, () -> openSkillConfigEditor(player, monsterId, skillId));
    }

    /**
     * 解析输入值
     */
    private Object parseInputValue(String input, ParameterInfo paramInfo) {
        if (paramInfo.defaultValue instanceof Double) {
            return Double.parseDouble(input);
        } else if (paramInfo.defaultValue instanceof Integer) {
            return Integer.parseInt(input);
        } else if (paramInfo.defaultValue instanceof Boolean) {
            return Boolean.parseBoolean(input) || "true".equalsIgnoreCase(input) || "1".equals(input);
        } else {
            return input;
        }
    }

    /**
     * 验证参数值 - 使用动态范围验证
     */
    private boolean validateParameterValue(Object value, ParameterInfo paramInfo) {
        // 对于自定义配置，使用更宽松的验证规则
        if (value instanceof Double) {
            double val = (Double) value;

            // 支持-1作为特殊值（表示无限/永久）
            if (val == -1.0) {
                return true;
            }

            // 动态范围：0到Double.MAX_VALUE
            return val >= 0.0 && val <= Double.MAX_VALUE;

        } else if (value instanceof Integer) {
            int val = (Integer) value;

            // 支持-1作为特殊值（表示无限/永久）
            if (val == -1) {
                return true;
            }

            // 动态范围：0到Integer.MAX_VALUE
            return val >= 0 && val <= Integer.MAX_VALUE;
        }

        // 其他类型（如字符串、布尔值）直接通过
        return true;
    }

    /**
     * 获取当前参数值
     */
    private Object getCurrentParameterValue(String monsterId, String skillId, String paramKey, Object defaultValue) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            return defaultValue;
        }

        // 从SkillConfig中获取参数值
        SkillConfig skillConfig = config.getSkillConfig(skillId);
        if (skillConfig != null) {
            return skillConfig.getParameter(paramKey, defaultValue);
        }

        return defaultValue;
    }

    /**
     * 获取技能的默认参数
     */
    public Map<String, Object> getSkillDefaultParameters(String skillId) {
        System.out.println("[DEBUG] getSkillDefaultParameters called with skillId: " + skillId);
        logger.info("开始获取技能默认参数 - 原始技能ID: " + skillId);

        // 转换技能ID
        String baseSkillId = skillId;
        if (skillId.startsWith("id") && skillId.contains("_")) {
            String[] parts = skillId.split("_", 2);
            if (parts.length > 1) {
                baseSkillId = parts[1];
                logger.info("ID系列技能转换: " + skillId + " -> " + baseSkillId);
            }
        } else if (skillId.startsWith("idc") && skillId.contains("_")) {
            String[] parts = skillId.split("_", 2);
            if (parts.length > 1) {
                baseSkillId = parts[1];
                logger.info("IDC系列技能转换: " + skillId + " -> " + baseSkillId);
            }
        } else {
            logger.info("技能ID无需转换: " + skillId);
        }

        logger.info("查找技能模板: " + baseSkillId);
        logger.info("可用的技能模板: " + skillParameterTemplates.keySet());

        SkillParameterTemplate template = skillParameterTemplates.get(baseSkillId);
        if (template == null) {
            logger.warning("找不到技能模板: " + baseSkillId + " (原始ID: " + skillId + ")");
            logger.warning("可用模板数量: " + skillParameterTemplates.size());
            return new HashMap<>();
        }

        logger.info("找到技能模板: " + baseSkillId + ", 参数数量: " + template.parameters.size());

        Map<String, Object> defaultParams = new HashMap<>();
        for (Map.Entry<String, ParameterInfo> entry : template.parameters.entrySet()) {
            String paramKey = entry.getKey();
            ParameterInfo paramInfo = entry.getValue();
            defaultParams.put(paramKey, paramInfo.defaultValue);
            logger.info("添加默认参数: " + paramKey + " = " + paramInfo.defaultValue);
        }

        logger.info("获取技能 " + skillId + " 的默认参数完成: " + defaultParams.size() + " 个");
        logger.info("默认参数详情: " + defaultParams);
        return defaultParams;
    }

    /**
     * 返回技能选择器
     */
    private void returnToSkillSelector(Player player, String monsterId) {
        logger.info("返回技能选择器 - 玩家: " + player.getName() + ", 怪物ID: " + monsterId);

        editingMonster.remove(player);
        editingSkill.remove(player);
        player.closeInventory();

        // 重新打开技能选择界面
        Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                logger.info("准备重新打开技能选择界面");
                idzManager.openGUIEditor(player, monsterId);
                logger.info("主编辑界面已重新打开");
            } catch (Exception e) {
                logger.severe("重新打开主编辑界面时出错: " + e.getMessage());
                e.printStackTrace();
                player.sendMessage(ChatColor.RED + "返回主界面时出错，请重新打开");
            }
        });

        player.sendMessage(ChatColor.YELLOW + "返回技能选择器...");
    }

    /**
     * 保存技能配置
     */
    private void saveSkillConfig(Player player, String monsterId) {
        editingMonster.remove(player);
        editingSkill.remove(player);
        player.closeInventory();
        player.sendMessage(ChatColor.GREEN + "技能配置已保存！");
    }

    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        editingSkill.remove(player);
        waitingForInput.remove(player);
        inputType.remove(player);
    }
}
