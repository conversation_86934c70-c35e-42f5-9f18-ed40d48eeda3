package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.util.*;
import java.util.logging.Logger;

/**
 * 药水效果编辑器GUI（IDZ）
 * - 显示当前药水效果
 * - 添加 / 编辑 / 移除 / 清空
 * - 通过聊天输入参数
 */
public class PotionEffectsEditorGUI implements Listener {

    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager idzManager;

    private static final String GUI_TITLE = "§6药水效果编辑器";
    private static final int GUI_SIZE = 54;

    // 槽位
    private static final int LIST_START = 9;   // 9-35 区域展示当前效果
    private static final int LIST_END = 35;
    private static final int ADD_SLOT = 38;
    private static final int CLEAR_SLOT = 39;
    private static final int BACK_SLOT = 49;
    private static final int SAVE_SLOT = 53;

    // 状态追踪
    private final Map<Player, String> editingMonster = new HashMap<>();
    private final Map<Player, String> waitingAction = new HashMap<>(); // add 或 edit
    private final Map<Player, PotionEffectType> editingEffectType = new HashMap<>();

    public PotionEffectsEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    public void openPotionEditor(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        editingMonster.put(player, monsterId);

        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE + " - " + config.getDisplayName());
        fillBackground(gui);
        setupEffectList(gui, config);
        setupControlButtons(gui, config);
        player.openInventory(gui);

        logger.info("为玩家 " + player.getName() + " 打开药水效果编辑器: " + monsterId);
    }

    private void fillBackground(Inventory gui) {
        ItemStack bg = new ItemStack(Material.MAGENTA_STAINED_GLASS_PANE);
        ItemMeta meta = bg.getItemMeta();
        meta.setDisplayName(" ");
        bg.setItemMeta(meta);
        for (int i = 0; i < 9; i++) { gui.setItem(i, bg); gui.setItem(i + 45, bg); }
        for (int i = 0; i < 54; i += 9) { gui.setItem(i, bg); gui.setItem(i + 8, bg); }
    }

    private void setupEffectList(Inventory gui, IDZMonsterConfig config) {
        List<PotionEffect> effects = config.getPotionEffects();
        int slot = LIST_START;
        for (PotionEffect e : effects) {
            if (slot > LIST_END) break;
            gui.setItem(slot, createEffectItem(e));
            slot++;
        }
    }

    private ItemStack createEffectItem(PotionEffect effect) {
        ItemStack item = new ItemStack(Material.POTION);
        ItemMeta meta = item.getItemMeta();
        String name = "§b" + effect.getType().getName();
        List<String> lore = Arrays.asList(
                "§7持续: §e" + effect.getDuration() + " ticks",
                "§7等级: §e" + effect.getAmplifier(),
                "§7环境: §e" + effect.isAmbient(),
                "§7粒子: §e" + effect.hasParticles(),
                "",
                "§e左键: 编辑  §c右键: 移除"
        );
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private void setupControlButtons(Inventory gui, IDZMonsterConfig config) {
        // 添加效果
        ItemStack addBtn = createButton(Material.LIME_DYE, "§a添加效果",
                Arrays.asList(
                        "§7输入格式:",
                        "§7类型 持续tick 等级 [ambient] [particles]",
                        "§7示例: §eSPEED 600 1 true true",
                        "",
                        "§e点击后在聊天栏输入"
                ));
        gui.setItem(ADD_SLOT, addBtn);

        // 清空
        ItemStack clearBtn = createButton(Material.RED_DYE, "§c清空所有",
                Arrays.asList("§7移除所有药水效果"));
        gui.setItem(CLEAR_SLOT, clearBtn);

        // 返回
        ItemStack backBtn = createButton(Material.ARROW, "§e← 返回",
                Arrays.asList("§7返回主编辑界面"));
        gui.setItem(BACK_SLOT, backBtn);

        // 保存
        ItemStack saveBtn = createButton(Material.EMERALD, "§a✓ 保存",
                Arrays.asList("§7保存药水效果配置"));
        gui.setItem(SAVE_SLOT, saveBtn);

        // 顶部信息
        ItemStack info = createButton(Material.GLASS_BOTTLE, "§6当前效果数量: " + config.getPotionEffects().size(),
                Arrays.asList("§7支持所有 Bukkit PotionEffectType"));
        gui.setItem(4, info);
    }

    private ItemStack createButton(Material mat, String name, List<String> lore) {
        ItemStack item = new ItemStack(mat);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    // 工具方法：判断字符串是否为整数
    private boolean isInteger(String s) {
        if (s == null) return false;
        try {
            Integer.parseInt(s);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @EventHandler
        // 允许被其他GUI类误监听到点击事件，但我们仅在标题匹配本GUI时处理

    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        if (title == null || !title.startsWith(GUI_TITLE)) return;
        event.setCancelled(true);

        String monsterId = editingMonster.get(player);
        if (monsterId == null) return;
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        int slot = event.getRawSlot();
        if (slot == BACK_SLOT) {
            returnToMain(player, monsterId);
            return;
        }
        if (slot == SAVE_SLOT) {
            // 显式保存到配置文件
            boolean ok = idzManager.updateMonsterConfig(monsterId, config);
            if (ok) {
                player.sendMessage(ChatColor.GREEN + "药水效果配置已保存！");
            } else {
                player.sendMessage(ChatColor.RED + "保存失败，请查看后台日志");
            }
            player.closeInventory();
            editingMonster.remove(player);
            return;
        }
        if (slot == ADD_SLOT) {
            waitingAction.put(player, "add");
            editingEffectType.remove(player);
            player.closeInventory();
            player.sendMessage(ChatColor.YELLOW + "请输入: 类型 持续tick 等级 [ambient] [particles]");
            return;
        }
        if (slot == CLEAR_SLOT) {
            config.clearPotionEffects();
            // 立即保存
            idzManager.updateMonsterConfig(monsterId, config);
            reopen(player, monsterId);
            player.sendMessage(ChatColor.GREEN + "已清空所有药水效果");
            return;
        }

        // 点击列表中的效果
        if (slot >= LIST_START && slot <= LIST_END) {
            ItemStack clicked = event.getCurrentItem();
            if (clicked == null || !clicked.hasItemMeta() || clicked.getItemMeta().getDisplayName() == null) return;
            String name = ChatColor.stripColor(clicked.getItemMeta().getDisplayName());
            PotionEffectType type = PotionEffectType.getByName(name);
            if (type == null) return;

            if (event.isRightClick()) {
                // 移除此效果
                boolean removed = config.removePotionEffect(type);
                if (removed) {
                    player.sendMessage(ChatColor.GREEN + "已移除效果: " + type.getName());
                    reopen(player, monsterId);
                }
            } else {
                // 编辑此效果
                if (logger != null) logger.info("PotionEffectsEditorGUI 等待输入: edit(" + type.getName() + "); 玩家=" + player.getName());

                waitingAction.put(player, "edit");
                editingEffectType.put(player, type);
                player.closeInventory();
                player.sendMessage(ChatColor.YELLOW + "正在编辑 " + type.getName() + "，请输入新参数: 类型 持续tick 等级 [ambient] [particles]");
            }
        }
    }
        // 聊天监听：同理，只有在我们维护的 waitingAction 状态存在时才处理


    @EventHandler
    public void onChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        if (!waitingAction.containsKey(player)) return;
        event.setCancelled(true);

        String action = waitingAction.remove(player);
        String monsterId = editingMonster.get(player);
        if (monsterId == null) return;
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        String msg = event.getMessage().trim();
        String[] parts = msg.split("\\s+");

        boolean isEdit = "edit".equals(action);
        PotionEffectType fixedType = isEdit ? editingEffectType.get(player) : null;
        PotionEffectType type;
        int startIdx; // 持续时间起始索引

        if (isEdit) {
            if (fixedType == null) {
                player.sendMessage(ChatColor.RED + "内部状态丢失：未找到正在编辑的效果类型");
                reopenSync(player, monsterId);
                return;
            }
            type = fixedType;
            // 允许用户输入时可带上类型或不带类型：如果第一个不是整数，认为它是冗余类型文本
            startIdx = (parts.length >= 1 && !isInteger(parts[0])) ? 1 : 0;
            if (parts.length - startIdx < 2) {
                player.sendMessage(ChatColor.RED + "格式错误！应为: [TYPE] duration amplifier [ambient] [particles]");
                reopenSync(player, monsterId);
                return;
            }
        } else {
            if (parts.length < 3) {
                player.sendMessage(ChatColor.RED + "格式错误！应为: TYPE duration amplifier [ambient] [particles]");
                reopenSync(player, monsterId);
                return;
            }
            type = PotionEffectType.getByName(parts[0].toUpperCase());
            if (type == null) {
                player.sendMessage(ChatColor.RED + "未知的效果类型: " + parts[0]);
                reopenSync(player, monsterId);
                return;
            }
            startIdx = 1; // 跳过类型
        }

        try {
            int duration = Integer.parseInt(parts[startIdx]);
            int amplifier = Integer.parseInt(parts[startIdx + 1]);
            boolean ambient = parts.length >= startIdx + 3 ? Boolean.parseBoolean(parts[startIdx + 2]) : false;
            boolean particles = parts.length >= startIdx + 4 ? Boolean.parseBoolean(parts[startIdx + 3]) : true;

            PotionEffect effect = new PotionEffect(type, duration, amplifier, ambient, particles);
            config.addOrUpdatePotionEffect(effect);
            editingEffectType.remove(player);
            player.sendMessage(ChatColor.GREEN + ("add".equals(action) ? "已添加效果: " : "已更新效果: ") + type.getName());
        } catch (NumberFormatException ex) {
            player.sendMessage(ChatColor.RED + "数字解析失败，请检查持续时间和等级");
        }
        reopenSync(player, monsterId);
    }

    @EventHandler
    public void onClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        if (title != null && title.startsWith(GUI_TITLE)) {
            // 延迟清理，避免在切换GUI或等待聊天输入时误清理
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // 如果玩家正在等待聊天输入（添加/编辑），不要清理状态
                if (waitingAction.containsKey(player)) {
                    if (logger != null) logger.info("PotionEffectsEditorGUI: 正在等待聊天输入，不清理状态 玩家=" + player.getName());
                    return;
                }
                String newTitle = player.getOpenInventory().getTitle();
                if (!newTitle.contains("IDZ") && !newTitle.startsWith(GUI_TITLE)) {
                    if (logger != null) logger.info("PotionEffectsEditorGUI: 关闭并清理状态 玩家=" + player.getName());
                    cleanupPlayer(player);
                }
            }, 1L);
        }
    }

    private void reopen(Player player, String monsterId) {
        Bukkit.getScheduler().runTask(plugin, () -> openPotionEditor(player, monsterId));
    }

    private void reopenSync(Player player, String monsterId) {
        Bukkit.getScheduler().runTask(plugin, () -> openPotionEditor(player, monsterId));
    }

    private void returnToMain(Player player, String monsterId) {
        editingMonster.remove(player);
        player.closeInventory();
        Bukkit.getScheduler().runTaskLater(plugin, () -> idzManager.getGuiManager().openMainEditor(player, monsterId), 1L);
    }

    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        waitingAction.remove(player);
        editingEffectType.remove(player);
    }
}

