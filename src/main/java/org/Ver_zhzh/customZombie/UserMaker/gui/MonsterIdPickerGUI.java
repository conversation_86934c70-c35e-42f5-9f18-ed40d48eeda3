package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.io.File;
import java.util.*;

/**
 * 怪物ID分页选择器GUI：支持 ID / IDC / IDZ 三个系列
 * - 顶部做系列切换与快捷项
 * - 中间做分页展示
 * - 底部翻页与返回
 */
public class MonsterIdPickerGUI implements Listener {
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final SkillConfigGUI parent;

    private static final String TITLE = "§6选择召唤怪物 (ID/IDC/IDZ)";

    // 记录玩家当前选择上下文
    private final Map<java.util.UUID, PickerContext> contexts = new HashMap<>();

    private enum Series { ID, IDC, IDZ }

    private static class PickerContext {
        final String monsterId;
        final String skillId;
        final String paramKey;
        Series series;
        int page;
        PickerContext(String monsterId, String skillId, String paramKey, Series series, int page) {
            this.monsterId = monsterId;
            this.skillId = skillId;
            this.paramKey = paramKey;
            this.series = series;
            this.page = page;
        }
    }

    public MonsterIdPickerGUI(Plugin plugin, IDZMonsterManager idzManager, SkillConfigGUI parent) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.parent = parent;
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    public void open(Player player, String monsterId, String skillId, String paramKey) {
        Inventory gui = Bukkit.createInventory(null, 54, TITLE);

        // 顶部标签与快捷项
        gui.setItem(0, tabButton("ID"));
        gui.setItem(1, tabButton("IDC"));
        gui.setItem(2, tabButton("IDZ"));
        gui.setItem(4, quickButton("速度僵尸", "twin_spawn_zombie"));
        gui.setItem(8, manualButton());

        // 默认展示 IDZ 列表第1页（你建议IDZ动态为主）
        fillList(gui, getIdzEntries(), 1);

        player.openInventory(gui);
        // 使用本GUI自己的上下文记录，避免访问父类私有字段
        contexts.put(player.getUniqueId(), new PickerContext(monsterId, skillId, paramKey, Series.IDZ, 1));
    }

    private ItemStack tabButton(String name) {
        ItemStack item = new ItemStack(Material.BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§e系列: " + name);
        meta.setLore(Arrays.asList("§7点击切换系列"));
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack quickButton(String name, String id) {
        ItemStack item = new ItemStack(Material.SUGAR);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§a快捷: " + name);
        meta.setLore(Arrays.asList("§7选择: " + id));
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack manualButton() {
        ItemStack item = new ItemStack(Material.PAPER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§b手动输入怪物ID");
        meta.setLore(Arrays.asList("§7支持: idX / idcX / idz_xxx"));
        item.setItemMeta(meta);
        return item;
    }

    // 读取 ID 清单（限制为 id1..id22）并返回显示条目“名称(id)”
    private List<String> getIdEntries() {
        Map<String, String> idNameMap = new LinkedHashMap<>();
        try {
            File file = new File(plugin.getDataFolder(), "zombie.yml");
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(file);
            if (cfg.isConfigurationSection("user_custom_overrides.specific_overrides")) {
                for (String key : cfg.getConfigurationSection("user_custom_overrides.specific_overrides").getKeys(false)) {
                    if (key.matches("id(\\d+)$")) {
                        int n = Integer.parseInt(key.substring(2));
                        if (n >= 1 && n <= 22) { // 上限 22
                            String name = cfg.getString("user_custom_overrides.specific_overrides." + key + ".custom_name_override", key);
                            idNameMap.put(key, ChatColor.stripColor(name));
                        }
                    }
                }
            }
        } catch (Exception ignored) {}
        List<String> result = new ArrayList<>();
        for (Map.Entry<String, String> e : idNameMap.entrySet()) {
            result.add(e.getValue() + "(id)" + "::" + e.getKey()); // 显示文本::实际ID
        }
        return result;
    }

    // 读取 IDC 清单（固定 idc1..idc22）并返回“名称(idc)”
    private List<String> getIdcEntries() {
        Map<String, String> idcNameMap = new LinkedHashMap<>();
        try {
            File file = new File(plugin.getDataFolder(), "entity.yml");
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(file);
            if (cfg.isConfigurationSection("user_custom_overrides.specific_overrides")) {
                for (String key : cfg.getConfigurationSection("user_custom_overrides.specific_overrides").getKeys(false)) {
                    if (key.matches("idc(\\d+)$")) {
                        int n = Integer.parseInt(key.substring(3));
                        if (n >= 1 && n <= 22) { // 固定最大 22
                            String name = cfg.getString("user_custom_overrides.specific_overrides." + key + ".custom_name_override", key);
                            idcNameMap.put(key, ChatColor.stripColor(name));
                        }
                    }
                }
            }
        } catch (Exception ignored) {}
        List<String> result = new ArrayList<>();
        for (Map.Entry<String, String> e : idcNameMap.entrySet()) {
            result.add(e.getValue() + "(idc)" + "::" + e.getKey());
        }
        return result;
    }

    // 读取 IDZ 清单（动态，显示“IDZ名称(idz)”；名称来源：优先使用 entity.yml 的同名 idc 名称作为显示）
    private List<String> getIdzEntries() {
        List<String> result = new ArrayList<>();
        Map<String, String> idcNames = new HashMap<>();
        try {
            File entityFile = new File(plugin.getDataFolder(), "entity.yml");
            FileConfiguration entityCfg = YamlConfiguration.loadConfiguration(entityFile);
            if (entityCfg.isConfigurationSection("user_custom_overrides.specific_overrides")) {
                for (String key : entityCfg.getConfigurationSection("user_custom_overrides.specific_overrides").getKeys(false)) {
                    if (key.matches("idc(\\d+)$")) {
                        String name = entityCfg.getString("user_custom_overrides.specific_overrides." + key + ".custom_name_override", key);
                        idcNames.put(key, ChatColor.stripColor(name));
                    }
                }
            }
        } catch (Exception ignored) {}
        try {
            Set<String> ids = idzManager.getAllMonsterIds();
            for (String idz : ids) {
                // 试图解析出对应的 idcN（若命名风格相近），否则使用 idz 自身
                String display = idz;
                // 示例策略：若 idz 末尾有数字且在 1..22，取 idcN 的名字
                java.util.regex.Matcher m = java.util.regex.Pattern.compile(".*?(\\d+)$").matcher(idz);
                if (m.matches()) {
                    int n = Integer.parseInt(m.group(1));
                    String idcKey = "idc" + n;
                    if (idcNames.containsKey(idcKey)) {
                        display = idcNames.get(idcKey);
                    }
                }
                result.add(display + "(idz)" + "::" + idz);
            }
        } catch (Exception ignored) {}
        return result;
    }

    private void fillList(Inventory gui, List<String> entries, int page) {
        // 填充中间 10..43 的槽位，条目格式：显示文本::实际ID
        int start = (page - 1) * 28;
        int end = Math.min(entries.size(), start + 28);
        int slot = 10;
        for (int i = start; i < end; i++) {
            String entry = entries.get(i);
            String[] parts = entry.split("::", 2);
            String displayText = parts[0];
            String realId = (parts.length > 1 ? parts[1] : displayText);
            ItemStack item = new ItemStack(Material.ZOMBIE_HEAD);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§f" + displayText);
            meta.setLore(Arrays.asList("§7点击选择: " + realId));
            item.setItemMeta(meta);
            gui.setItem(slot, item);
            slot++;
            if (slot % 9 == 8) slot += 2;
            if (slot >= 44) break;
        }

        // 翻页控制
        gui.setItem(45, navButton("上一页"));
        gui.setItem(53, navButton("下一页"));
        gui.setItem(49, backButton());
        gui.setItem(51, saveButton());
    }

    private ItemStack navButton(String name) {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§e" + name);
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack backButton() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§c返回");
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack saveButton() {
        ItemStack item = new ItemStack(Material.EMERALD);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§a保存");
        item.setItemMeta(meta);
        return item;
    }

    @EventHandler
    public void onClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        if (!TITLE.equals(event.getView().getTitle())) return;
        event.setCancelled(true);

        int slot = event.getRawSlot();
        PickerContext ctx = contexts.get(player.getUniqueId());
        if (ctx == null) return;
        String monsterId = ctx.monsterId;
        String skillId = ctx.skillId;
        String paramKey = ctx.paramKey;

        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || !clicked.hasItemMeta() || !clicked.getItemMeta().hasDisplayName()) return;
        String name = ChatColor.stripColor(clicked.getItemMeta().getDisplayName());

        if (name.startsWith("系列:")) {
            // 切换系列并重置到第1页
            Inventory gui = event.getInventory();
            if (name.contains("IDZ")) {
                ctx.series = Series.IDZ; ctx.page = 1; fillList(gui, getIdzEntries(), 1);
            } else if (name.contains("IDC")) {
                ctx.series = Series.IDC; ctx.page = 1; fillList(gui, getIdcEntries(), 1);
            } else {
                ctx.series = Series.ID; ctx.page = 1; fillList(gui, getIdEntries(), 1);
            }
            return;
        }

        if (name.equals("上一页")) {
            ctx.page = Math.max(1, ctx.page - 1);
            List<String> entries = ctx.series == Series.IDZ ? getIdzEntries() : (ctx.series == Series.IDC ? getIdcEntries() : getIdEntries());
            fillList(event.getInventory(), entries, ctx.page);
            return;
        }
        if (name.equals("下一页")) {
            ctx.page = ctx.page + 1;
            List<String> entries = ctx.series == Series.IDZ ? getIdzEntries() : (ctx.series == Series.IDC ? getIdcEntries() : getIdEntries());
            fillList(event.getInventory(), entries, ctx.page);
            return;
        }
        if (name.equals("返回")) {
            player.closeInventory();
            Bukkit.getScheduler().runTask(plugin, () -> parent.openSkillConfigEditor(player, monsterId, skillId));
            return;
        }
        if (name.equals("保存")) {
            player.closeInventory();
            Bukkit.getScheduler().runTask(plugin, () -> parent.openSkillConfigEditor(player, monsterId, skillId));
            return;
        }

        if (name.startsWith("快捷:")) {
            // 速度僵尸
            setParamAndBack(player, monsterId, skillId, paramKey, "twin_spawn_zombie");
            return;
        }

        if (name.equals("手动输入怪物ID")) {
            player.closeInventory();
            // 切换回父GUI聊天输入模式
            parent.openSkillConfigEditor(player, monsterId, skillId);
            player.sendMessage(ChatColor.YELLOW + "请输入怪物ID（idX / idcX / idz_xxx）：");
            player.sendMessage(ChatColor.GRAY + "输入 cancel 取消");
            return;
        }

        // 列表项：从显示文本解析实际ID（显示文本::实际ID）
        if (clicked.getType() == Material.ZOMBIE_HEAD) {
            String display = ChatColor.stripColor(clicked.getItemMeta().getDisplayName()).trim();
            // 从当页条目中找到对应ID
            List<String> entries = ctx.series == Series.IDZ ? getIdzEntries() : (ctx.series == Series.IDC ? getIdcEntries() : getIdEntries());
            int start = (ctx.page - 1) * 28;
            int end = Math.min(entries.size(), start + 28);
            for (int i = start; i < end; i++) {
                String e = entries.get(i);
                String[] parts = e.split("::", 2);
                String disp = parts[0];
                String realId = (parts.length > 1 ? parts[1] : disp);
                if (disp.equals(display)) {
                    setParamAndBack(player, monsterId, skillId, paramKey, realId);
                    return;
                }
            }
        }
    }

    private void setParamAndBack(Player player, String monsterId, String skillId, String paramKey, String value) {
        try {
            // 更新到技能配置
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config != null) {
                org.Ver_zhzh.customZombie.UserMaker.SkillConfig skillConfig = config.getSkillConfig(skillId);
                if (skillConfig != null) {
                    skillConfig.setParameter(paramKey, value);
                    idzManager.updateMonsterConfig(monsterId, config);
                    player.sendMessage(ChatColor.GREEN + "已选择召唤怪物: " + value);
                }
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "保存选择失败: " + e.getMessage());
        }
        Bukkit.getScheduler().runTask(plugin, () -> parent.openSkillConfigEditor(player, monsterId, skillId));
    }
}

