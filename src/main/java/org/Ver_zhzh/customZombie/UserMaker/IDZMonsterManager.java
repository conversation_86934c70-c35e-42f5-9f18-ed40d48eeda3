package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;
import org.bukkit.metadata.FixedMetadataValue;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;

/**
 * IDZ自定义怪物管理器
 * 负责IDZ怪物的创建、删除、生成和管理
 *
 * <AUTHOR>
 * @version 1.0
 */
public class IDZMonsterManager {

    private final Plugin plugin;
    private final Logger logger;

    // GUI管理器
    private IDZMonsterGUI guiManager;

    // 怪物生成器
    private IDZMonsterSpawner monsterSpawner;

    // 配置文件管理
    private File configFile;
    private FileConfiguration config;

    // IDZ怪物配置缓存
    private final Map<String, IDZMonsterConfig> monsterConfigs;

    // 已生成的IDZ怪物实体追踪
    private final Map<String, Set<LivingEntity>> spawnedMonsters;

    // 调试模式
    private boolean debugMode;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public IDZMonsterManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.monsterConfigs = new HashMap<>();
        this.spawnedMonsters = new HashMap<>();
        this.debugMode = false;

        // 初始化配置文件
        initializeConfig();

        // 加载现有配置
        loadAllConfigs();

        // 初始化GUI管理器
        this.guiManager = new IDZMonsterGUI(plugin, this);

        // 初始化怪物生成器
        this.monsterSpawner = new IDZMonsterSpawner(plugin, this);

        logger.info("IDZ怪物管理器初始化完成");
    }

    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        configFile = new File(plugin.getDataFolder(), "idz_monsters.yml");

        // 如果配置文件不存在，创建默认配置
        if (!configFile.exists()) {
            try {
                configFile.getParentFile().mkdirs();
                configFile.createNewFile();

                // 创建默认配置内容
                config = YamlConfiguration.loadConfiguration(configFile);
                config.set("version", "1.0");
                config.set("debug_mode", false);
                config.set("monsters", new HashMap<String, Object>());

                // 添加配置说明
                config.setComments("version", Arrays.asList(
                    "IDZ自定义怪物配置文件",
                    "版本: 1.0",
                    "此文件存储用户创建的所有IDZ系列怪物配置"
                ));

                saveConfig();
                logger.info("创建IDZ怪物配置文件: " + configFile.getPath());

            } catch (IOException e) {
                logger.severe("无法创建IDZ怪物配置文件: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            config = YamlConfiguration.loadConfiguration(configFile);
        }

        // 加载调试模式设置
        debugMode = config.getBoolean("debug_mode", false);
    }

    /**
     * 加载所有IDZ怪物配置
     */
    private void loadAllConfigs() {
        if (config.getConfigurationSection("monsters") == null) {
            if (debugMode) {
                logger.info("没有找到IDZ怪物配置，跳过加载");
            }
            return;
        }

        Set<String> monsterIds = config.getConfigurationSection("monsters").getKeys(false);
        int loadedCount = 0;

        for (String monsterId : monsterIds) {
            try {
                IDZMonsterConfig monsterConfig = IDZMonsterConfig.fromConfigurationSection(
                    config.getConfigurationSection("monsters." + monsterId)
                );

                if (monsterConfig != null) {
                    monsterConfigs.put(monsterId, monsterConfig);
                    loadedCount++;

                    if (debugMode) {
                        logger.info("加载IDZ怪物配置: " + monsterId + " - " + monsterConfig.getDisplayName());
                    }
                }

            } catch (Exception e) {
                logger.warning("加载IDZ怪物配置失败: " + monsterId + " - " + e.getMessage());
                if (debugMode) {
                    e.printStackTrace();
                }
            }
        }

        logger.info("成功加载 " + loadedCount + " 个IDZ怪物配置");
    }

    /**
     * 创建新的IDZ怪物配置
     *
     * @param monsterId 怪物ID
     * @param displayName 显示名称
     * @return 是否创建成功
     */
    public boolean createMonster(String monsterId, String displayName) {
        if (monsterId == null || monsterId.trim().isEmpty()) {
            logger.warning("怪物ID不能为空");
            return false;
        }

        // 检查ID是否已存在
        if (monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物ID已存在: " + monsterId);
            return false;
        }

        // 验证ID格式（必须以idz开头）
        if (!monsterId.toLowerCase().startsWith("idz")) {
            logger.warning("IDZ怪物ID必须以'idz'开头: " + monsterId);
            return false;
        }

        try {
            // 创建默认配置
            IDZMonsterConfig newConfig = new IDZMonsterConfig(monsterId, displayName);

            // 添加一些默认的测试装备和粒子效果
            addDefaultTestConfiguration(newConfig);

            // 添加到缓存
            monsterConfigs.put(monsterId, newConfig);

            // 保存到配置文件
            saveMonsterConfig(monsterId, newConfig);

            // 验证配置是否正确保存
            IDZMonsterConfig savedConfig = getMonsterConfig(monsterId);
            if (savedConfig != null) {
                logger.info("配置验证 - 主手: " + (savedConfig.getMainHand() != null ? savedConfig.getMainHand().getType() : "null") +
                           ", 粒子: " + (savedConfig.getParticleTypes() != null ? savedConfig.getParticleTypes().size() + "种" : "null"));
            }

            logger.info("成功创建IDZ怪物: " + monsterId + " - " + displayName);
            return true;

        } catch (Exception e) {
            logger.severe("创建IDZ怪物失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除IDZ怪物配置
     *
     * @param monsterId 怪物ID
     * @return 是否删除成功
     */
    public boolean deleteMonster(String monsterId) {
        if (!monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物不存在: " + monsterId);
            return false;
        }

        try {
            // 清理已生成的怪物实体
            cleanupSpawnedMonsters(monsterId);

            // 从缓存中移除
            IDZMonsterConfig removedConfig = monsterConfigs.remove(monsterId);

            // 从配置文件中移除
            config.set("monsters." + monsterId, null);
            saveConfig();

            logger.info("成功删除IDZ怪物: " + monsterId + " - " + removedConfig.getDisplayName());
            return true;

        } catch (Exception e) {
            logger.severe("删除IDZ怪物失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取IDZ怪物配置
     *
     * @param monsterId 怪物ID
     * @return 怪物配置，如果不存在返回null
     */
    public IDZMonsterConfig getMonsterConfig(String monsterId) {
        return monsterConfigs.get(monsterId);
    }

    /**
     * 获取所有IDZ怪物ID列表
     *
     * @return 怪物ID列表
     */
    public Set<String> getAllMonsterIds() {
        return new HashSet<>(monsterConfigs.keySet());
    }

    /**
     * 根据名称或ID解析怪物ID。
     * 优先顺序：
     * 1) 如果输入本身就是有效的怪物ID(如 idz1234)，直接返回
     * 2) 按显示名称精确匹配（先区分大小写，再不区分大小写）
     * 3) 未找到返回null
     */
    public String resolveMonsterId(String nameOrId) {
        if (nameOrId == null) return null;
        String input = nameOrId.trim();
        // 1) 直接ID
        if (monsterConfigs.containsKey(input)) {
            return input;
        }
        // 2) 显示名精确匹配（区分大小写）
        for (Map.Entry<String, IDZMonsterConfig> e : monsterConfigs.entrySet()) {
            IDZMonsterConfig c = e.getValue();
            if (c != null && input.equals(c.getDisplayName())) {
                return e.getKey();
            }
        }
        // 2b) 显示名不区分大小写
        for (Map.Entry<String, IDZMonsterConfig> e : monsterConfigs.entrySet()) {
            IDZMonsterConfig c = e.getValue();
            if (c != null && input.equalsIgnoreCase(c.getDisplayName())) {
                return e.getKey();
            }
        }
        return null;
    }


    /**
     * 获取所有IDZ怪物配置
     *
     * @return 怪物配置映射
     */
    public Map<String, IDZMonsterConfig> getAllMonsterConfigs() {
        return new HashMap<>(monsterConfigs);
    }

    /**
     * 检查IDZ怪物是否存在
     *
     * @param monsterId 怪物ID
     * @return 是否存在
     */
    public boolean monsterExists(String monsterId) {
        return monsterConfigs.containsKey(monsterId);
    }

    /**
     * 更新IDZ怪物配置
     *
     * @param monsterId 怪物ID
     * @param config 新的配置
     * @return 是否更新成功
     */
    public boolean updateMonsterConfig(String monsterId, IDZMonsterConfig config) {
        if (!monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物不存在: " + monsterId);
            return false;
        }

        try {
            // 更新缓存
            monsterConfigs.put(monsterId, config);

            // 保存到配置文件
            saveMonsterConfig(monsterId, config);

            if (debugMode) {
                logger.info("更新IDZ怪物配置: " + monsterId);
            }
            return true;

        } catch (Exception e) {
            logger.severe("更新IDZ怪物配置失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 保存怪物配置到文件
     *
     * @param monsterId 怪物ID
     * @param monsterConfig 怪物配置
     */
    private void saveMonsterConfig(String monsterId, IDZMonsterConfig monsterConfig) {
        // 为避免直接 set(ConfigurationSection) 存在的兼容性问题，改用 createSection(path, map) 写入
        String path = "monsters." + monsterId;
        try {
            // 先清理旧节，防止已存在时 createSection 抛出异常或产生残留键
            config.set(path, null);
            java.util.Map<String, Object> values = monsterConfig.toConfigurationSection().getValues(true);
            config.createSection(path, values);
        } catch (Exception e) {
            // 退路：逐项写入
            org.bukkit.configuration.ConfigurationSection section = config.createSection(path);
            java.util.Map<String, Object> values = monsterConfig.toConfigurationSection().getValues(true);
            for (java.util.Map.Entry<String, Object> en : values.entrySet()) {
                section.set(en.getKey(), en.getValue());
            }
        }
        saveConfig();
    }

    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            logger.severe("保存IDZ怪物配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成IDZ怪物
     *
     * @param location 生成位置
     * @param monsterId 怪物ID
     * @return 生成的怪物实体，失败时返回null
     */
    public LivingEntity spawnMonster(org.bukkit.Location location, String monsterId) {
        if (location == null || monsterId == null) {
            logger.warning("生成IDZ怪物失败: 位置或怪物ID为null");
            return null;
        }

        // 检查怪物配置是否存在
        if (!monsterConfigs.containsKey(monsterId)) {
            logger.warning("未找到IDZ怪物配置: " + monsterId);
            return null;
        }

        try {
            // 使用生成器生成怪物
            LivingEntity entity = monsterSpawner.spawnMonster(location, monsterId);

            if (entity != null) {
                // 添加到追踪列表
                spawnedMonsters.computeIfAbsent(monsterId, k -> new HashSet<>()).add(entity);

                logger.info("成功生成IDZ怪物: " + monsterId + " 在位置: " + formatLocation(location));
                return entity;
            } else {
                logger.warning("IDZ怪物生成失败: " + monsterId);
                return null;
            }

        } catch (Exception e) {
            logger.severe("生成IDZ怪物时发生异常: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 批量生成IDZ怪物
     *
     * @param location 生成位置
     * @param monsterId 怪物ID
     * @param count 生成数量
     * @return 成功生成的怪物数量
     */
    public int spawnMultipleMonsters(org.bukkit.Location location, String monsterId, int count) {
        if (count <= 0) {
            return 0;
        }

        int successCount = 0;
        for (int i = 0; i < count; i++) {
            // 计算随机偏移位置
            org.bukkit.Location spawnLoc = location.clone().add(
                (Math.random() - 0.5) * 4, // X轴随机偏移 -2到2格
                0,
                (Math.random() - 0.5) * 4  // Z轴随机偏移 -2到2格
            );

            // 确保生成位置安全
            spawnLoc = findSafeLocation(spawnLoc);

            if (spawnMonster(spawnLoc, monsterId) != null) {
                successCount++;
            }

            // 添加小延迟避免服务器卡顿
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        logger.info("批量生成IDZ怪物完成: " + monsterId + " - 成功生成 " + successCount + "/" + count + " 个");
        return successCount;
    }

    /**
     * 寻找安全的生成位置
     */
    private org.bukkit.Location findSafeLocation(org.bukkit.Location location) {
        org.bukkit.Location safeLoc = location.clone();

        // 确保不在固体方块内
        while (safeLoc.getBlock().getType().isSolid() && safeLoc.getY() < 256) {
            safeLoc.setY(safeLoc.getY() + 1);
        }

        // 确保脚下有支撑
        while (!safeLoc.clone().subtract(0, 1, 0).getBlock().getType().isSolid() && safeLoc.getY() > 0) {
            safeLoc.setY(safeLoc.getY() - 1);
        }

        return safeLoc;
    }

    /**
     * 获取指定怪物的已生成实体数量
     *
     * @param monsterId 怪物ID
     * @return 已生成的实体数量
     */
    public int getSpawnedMonsterCount(String monsterId) {
        Set<LivingEntity> entities = spawnedMonsters.get(monsterId);
        if (entities == null) {
            return 0;
        }

        // 清理已死亡的实体
        entities.removeIf(entity -> entity == null || entity.isDead());
        return entities.size();
    }

    /**
     * 获取所有已生成的IDZ怪物总数
     *
     * @return 总数量
     */
    public int getTotalSpawnedMonsterCount() {
        int total = 0;
        for (String monsterId : spawnedMonsters.keySet()) {
            total += getSpawnedMonsterCount(monsterId);
        }
        return total;
    }

    /**
     * 清理指定怪物的所有已生成实体
     *
     * @param monsterId 怪物ID
     */
    private void cleanupSpawnedMonsters(String monsterId) {
        Set<LivingEntity> entities = spawnedMonsters.get(monsterId);
        if (entities != null) {
            int removedCount = 0;
            for (LivingEntity entity : new HashSet<>(entities)) {
                if (entity != null && !entity.isDead()) {
                    entity.remove();
                    removedCount++;
                }
            }
            entities.clear();

            if (debugMode && removedCount > 0) {
                logger.info("清理IDZ怪物实体: " + monsterId + " - " + removedCount + "个");
            }
        }
    }

    /**
     * 清理所有已生成的IDZ怪物
     */
    public void cleanupAllSpawnedMonsters() {
        logger.info("开始清理所有IDZ怪物实体...");

        int totalRemoved = 0;
        for (String monsterId : new HashSet<>(spawnedMonsters.keySet())) {
            int beforeCount = getSpawnedMonsterCount(monsterId);
            cleanupSpawnedMonsters(monsterId);
            totalRemoved += beforeCount;
        }

        logger.info("清理完成，共移除 " + totalRemoved + " 个IDZ怪物实体");
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(org.bukkit.Location location) {
        return String.format("世界:%s, X:%.1f, Y:%.1f, Z:%.1f",
            location.getWorld().getName(), location.getX(), location.getY(), location.getZ());
    }

    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        logger.info("重新加载IDZ怪物配置...");

        // 清理现有配置
        monsterConfigs.clear();

        // 重新初始化配置文件
        initializeConfig();

        // 重新加载配置
        loadAllConfigs();

        logger.info("IDZ怪物配置重新加载完成");
    }

    /**
     * 设置调试模式
     *
     * @param debug 是否启用调试模式
     */
    public void setDebugMode(boolean debug) {
        this.debugMode = debug;
        config.set("debug_mode", debug);
        saveConfig();

        logger.info("IDZ怪物管理器调试模式: " + (debug ? "启用" : "禁用"));
    }

    /**
     * 获取调试模式状态
     *
     * @return 是否启用调试模式
     */
    public boolean isDebugMode() {
        return debugMode;
    }

    /**
     * 打开GUI编辑器
     *
     * @param player 玩家
     * @param monsterId 怪物ID
     */
    public void openGUIEditor(org.bukkit.entity.Player player, String monsterId) {
        if (guiManager != null) {
            guiManager.openMainEditor(player, monsterId);
        } else {
            player.sendMessage(org.bukkit.ChatColor.RED + "GUI管理器未初始化！");
        }
    }

    /**
     * 获取GUI管理器
     *
     * @return GUI管理器实例
     */
    public IDZMonsterGUI getGuiManager() {
        return guiManager;
    }

    /**
     * 为现有怪物添加测试配置 (公共方法，用于调试)
     */
    public boolean addTestConfigurationToExisting(String monsterId) {
        try {
            IDZMonsterConfig config = getMonsterConfig(monsterId);
            if (config == null) {
                logger.warning("未找到怪物配置: " + monsterId);
                return false;
            }

            logger.info("为现有怪物添加测试配置: " + monsterId);
            addDefaultTestConfiguration(config);

            // 保存更新后的配置
            saveMonsterConfig(monsterId, config);

            // 验证保存结果
            IDZMonsterConfig savedConfig = getMonsterConfig(monsterId);
            if (savedConfig != null) {
                logger.info("测试配置验证 - 主手: " + (savedConfig.getMainHand() != null ? savedConfig.getMainHand().getType() : "null") +
                           ", 粒子: " + (savedConfig.getParticleTypes() != null ? savedConfig.getParticleTypes().size() + "种" : "null"));
            }

            return true;
        } catch (Exception e) {
            logger.severe("为现有怪物添加测试配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 添加默认测试配置
     * 为新创建的怪物添加一些基础的装备和粒子效果用于测试
     */
    private void addDefaultTestConfiguration(IDZMonsterConfig config) {
        try {
            logger.info("为新怪物添加默认测试配置: " + config.getMonsterId());

            // 添加基础装备
            config.setMainHand(new org.bukkit.inventory.ItemStack(org.bukkit.Material.IRON_SWORD));
            config.setHelmet(new org.bukkit.inventory.ItemStack(org.bukkit.Material.IRON_HELMET));
            config.setChestplate(new org.bukkit.inventory.ItemStack(org.bukkit.Material.IRON_CHESTPLATE));

            // 添加基础粒子效果
            java.util.List<String> particleTypes = new java.util.ArrayList<>();
            particleTypes.add("FLAME");
            particleTypes.add("ENCHANTED_HIT");
            config.setParticleTypes(particleTypes);
            config.setParticleCount(5);
            config.setParticleInterval(40); // 2秒间隔

            // 设置一些增强属性
            config.setHealth(100.0);
            config.setAttackDamage(8.0);
            config.setMovementSpeed(0.3);

            logger.info("默认测试配置添加完成 - 装备: 铁剑+铁甲, 粒子: 火焰+附魔");

        } catch (Exception e) {
            logger.warning("添加默认测试配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 关闭管理器，清理资源
     */
    public void shutdown() {
        logger.info("关闭IDZ怪物管理器...");

        // 清理GUI管理器
        if (guiManager != null) {
            guiManager.shutdown();
        }

        // 清理所有已生成的怪物实体
        for (String monsterId : spawnedMonsters.keySet()) {
            cleanupSpawnedMonsters(monsterId);
        }

        // 保存配置
        saveConfig();

        logger.info("IDZ怪物管理器已关闭");
    }
}
