package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.*;
import org.bukkit.entity.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * IDZSkillExecutor的扩展方法
 * 包含剩余的技能处理器实现
 */
public class IDZSkillExecutorExtensions {

    private final Plugin plugin;
    private final Logger logger;

    public IDZSkillExecutorExtensions(Plugin plugin, Logger logger) {
        this.plugin = plugin;
        this.logger = logger;
    }

    /**
     * 自身增益类技能：给怪物本体添加药水效果
     */
    public Integer handleBuffSelf(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("增益技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        String skillName = cfg.getSkillName();
        String lower = skillName == null ? "" : skillName.toLowerCase();

        // 支持多种参数名
        int duration = toInt(cfg.getParameter("buff_duration",
                      cfg.getParameter("effect_duration",
                      cfg.getParameter("duration", 600)))); // 30秒

        int amplifier = toInt(cfg.getParameter("buff_amplifier",
                       cfg.getParameter("effect_amplifier",
                       cfg.getParameter("amplifier", 1))));

        logger.info(String.format("挂载技能[%s]: duration=%d, amplifier=%d (从%d个参数中读取)",
                   skillName, duration, amplifier, allParams.size()));

        // 根据技能名称应用对应的药水效果
        PotionEffectType effectType = getBuffEffectType(skillName);
        if (effectType != null) {
            caster.addPotionEffect(new PotionEffect(effectType, duration, amplifier));
        }

        // 特化：暴击光环 — 在脚底显示暴击粒子圈，并且在范围内周期性造成伤害；命中时的暴击倍率由事件监听负责
        if (lower.contains("critical")) {
            int interval = Math.max(1, toInt(
                    cfg.getParameter("crit_interval",
                    cfg.getParameter("aura_interval",
                    cfg.getParameter("effect_interval",
                    cfg.getParameter("interval", 20))))));
            double range = Math.max(0.5, toDouble(
                    cfg.getParameter("crit_range",
                    cfg.getParameter("aura_range",
                    cfg.getParameter("range", 2.5)))));
            double damage = Math.max(0.0, toDouble(
                    cfg.getParameter("crit_damage",
                    cfg.getParameter("aura_damage",
                    cfg.getParameter("damage", 0.0)))));
            double critChance = Math.max(0.0, Math.min(1.0, toDouble(
                    cfg.getParameter("crit_chance",
                    cfg.getParameter("critical_chance", 0.25)))));
            double critMultiplier = Math.max(1.0, toDouble(
                    cfg.getParameter("crit_multiplier",
                    cfg.getParameter("critical_multiplier", 1.5))));
            boolean showParticles = Boolean.parseBoolean(String.valueOf(cfg.getParameter("show_particles", true)));

            logger.info(String.format("挂载技能[%s-暴击光环]: interval=%d, range=%.2f, damage=%.2f, critChance=%.2f, critMul=%.2f, particles=%s (参数=%d)",
                    skillName, interval, range, damage, critChance, critMultiplier, showParticles, allParams.size()));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                Location c = caster.getLocation();
                World w = caster.getWorld();
                int points = 24;
                double y = c.getY() + 0.05;
                if (showParticles) {
                    for (int i = 0; i < points; i++) {
                        double ang = 2 * Math.PI * i / points;
                        double x = c.getX() + Math.cos(ang) * range;
                        double z = c.getZ() + Math.sin(ang) * range;
                        w.spawnParticle(org.bukkit.Particle.CRIT, new Location(w, x, y, z), 1, 0, 0, 0, 0);
                    }
                }
                // 周期性对范围内玩家造成伤害
                if (damage > 0.0) {
                    for (Player p : w.getPlayers()) {
                        if (!p.isOnline() || p.isDead()) continue;
                        if (p.getLocation().distanceSquared(c) <= range * range) {
                            p.damage(damage, caster);
                        }
                    }
                }
            }, interval, interval);

            // 将暴击参数保存到元数据，供事件监听读取（命中时倍率判定）
            try {
                caster.setMetadata("idz_critical_aura_interval", new FixedMetadataValue(plugin, interval));
                caster.setMetadata("idz_critical_aura_range", new FixedMetadataValue(plugin, range));
                caster.setMetadata("idz_crit_chance", new FixedMetadataValue(plugin, critChance));
                caster.setMetadata("idz_crit_multiplier", new FixedMetadataValue(plugin, critMultiplier));
            } catch (Exception ignored) {}
            return task.getTaskId();
        }

        return null; // 不需要定时任务（除非是暴击光环）
    }

    /**
     * 范围增益/减益类技能：对范围内玩家添加效果
     */
    public Integer handleAreaBuffDebuff(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("范围增益技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        String rawName = cfg.getSkillName();
        String lower = rawName == null ? "" : rawName.toLowerCase();

        // 特化：范围debuff（支持具体的反胃/缓慢等级与持续时间）
        if (lower.contains("area_debuff")) {
            int interval = toInt(cfg.getParameter("effect_interval", cfg.getParameter("interval", 40)));
            double range = toDouble(cfg.getParameter("debuff_range", cfg.getParameter("range", 10.0)));
            int duration = toInt(cfg.getParameter("debuff_duration", cfg.getParameter("duration", 100)));
            int nauseaLvl = Math.max(0, toInt(cfg.getParameter("nausea_level", 1)));
            int slowLvl = Math.max(0, toInt(cfg.getParameter("slowness_level", 1)));
            logger.info(String.format("挂载技能[%s-范围debuff]: interval=%d, range=%.2f, duration=%d, nausea=%d, slowness=%d (参数=%d)",
                    rawName, interval, range, duration, nauseaLvl, slowLvl, allParams.size()));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                World world = caster.getWorld();
                Location center = caster.getLocation();
                for (Player p : world.getPlayers()) {
                    if (!p.isOnline() || p.isDead()) continue;
                    if (p.getLocation().distanceSquared(center) <= range * range) {
                        p.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, Math.max(1, duration), nauseaLvl));
                        p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Math.max(1, duration), slowLvl));
                    }
                }
            }, Math.max(1, interval), Math.max(1, interval));
            return task.getTaskId();
        }

        // 特化：范围buff（光环buff）— 读取 GUI 参数，对“敌对生物/玩家”施加效果，并在地面显示范围粒子
        if (lower.contains("area_buff") || lower.contains("aura_buff")) {
            int interval = toInt(cfg.getParameter("effect_interval", cfg.getParameter("buff_interval", cfg.getParameter("interval", 40))));
            double range = toDouble(cfg.getParameter("buff_range", cfg.getParameter("aura_range", cfg.getParameter("range", 10.0))));
            int duration = Math.max(1, toInt(cfg.getParameter("buff_duration", cfg.getParameter("duration", interval + 20))));
            // 支持读取力量与速度等级（或统一的buff_amplifier）
            int amplifier = Math.max(0, toInt(cfg.getParameter("buff_amplifier", 1)));
            int strengthLvl = Math.max(amplifier, toInt(cfg.getParameter("strength_level", 1)) - 1);
            int speedLvl = Math.max(amplifier - 1, toInt(cfg.getParameter("speed_level", 1)) - 1);
            boolean affectPlayers = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_players", true)));
            boolean affectMobs = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_mobs", true)));
            boolean showParticles = Boolean.parseBoolean(String.valueOf(cfg.getParameter("show_particles", true)));

            logger.info(String.format("挂载技能[%s-光环buff]: interval=%d, range=%.2f, strength=%d, speed=%d, duration=%d, particles=%s (参数=%d)",
                    rawName, interval, range, strengthLvl, speedLvl, duration, showParticles, allParams.size()));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                Location center = caster.getLocation();
                World world = caster.getWorld();

                // 显示脚底范围粒子（圈）
                if (showParticles) {
                    int points = 36;
                    for (int i = 0; i < points; i++) {
                        double angle = (2 * Math.PI / points) * i;
                        double x = center.getX() + Math.cos(angle) * range;
                        double z = center.getZ() + Math.sin(angle) * range;
                        world.spawnParticle(Particle.CRIT, new Location(world, x, center.getY() + 0.05, z), 1, 0, 0, 0, 0);
                    }
                }

                // 施加效果到范围内目标
                for (Entity e : world.getNearbyEntities(center, range, range, range)) {
                    if (!(e instanceof LivingEntity)) continue;
                    if (e.getUniqueId().equals(caster.getUniqueId())) continue;

                    if (e instanceof Player) {
                        if (!affectPlayers) continue;
                        Player p = (Player) e;
                        if (!p.isOnline() || p.isDead()) continue;
                        if (strengthLvl >= 0) p.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, duration, strengthLvl));
                        if (speedLvl >= 0) p.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, duration, speedLvl));
                    } else {
                        if (!affectMobs) continue;
                        LivingEntity le = (LivingEntity) e;
                        if (strengthLvl >= 0) le.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, duration, strengthLvl));
                        if (speedLvl >= 0) le.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, duration, speedLvl));
                    }
                }
            }, Math.max(1, interval), Math.max(1, interval));
            return task.getTaskId();
        }

        // 专用：全局Debuff（支持自定义负面效果），覆盖默认处理
        if (lower.contains("global_debuff")) {
            int interval = Math.max(1, toInt(cfg.getParameter("debuff_interval", cfg.getParameter("interval", 400))));
            int duration = Math.max(1, toInt(cfg.getParameter("debuff_duration", cfg.getParameter("duration", 200))));
            int weakness = Math.max(0, toInt(cfg.getParameter("weakness_level", 1)));
            int slowness = Math.max(0, toInt(cfg.getParameter("slowness_level", 0)));
            int poison = Math.max(0, toInt(cfg.getParameter("poison_level", 0)));
            int wither = Math.max(0, toInt(cfg.getParameter("wither_level", 0)));
            // 可选：逗号分隔效果列表，例如 "WEAKNESS:1,SLOWNESS:2"
            String extra = String.valueOf(cfg.getParameter("effects", ""));

            logger.info(String.format("挂载技能[%s-全局debuff]: interval=%d, duration=%d, weakness=%d, slowness=%d, poison=%d, wither=%d, extra=%s",
                    rawName, interval, duration, weakness, slowness, poison, wither, extra));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                for (Player p : Bukkit.getOnlinePlayers()) {
                    if (!p.isOnline() || p.isDead()) continue;
                    if (weakness > 0) p.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, duration, weakness));
                    if (slowness > 0) p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, slowness));
                    if (poison > 0) p.addPotionEffect(new PotionEffect(PotionEffectType.POISON, duration, poison));
                    if (wither > 0) p.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, duration, wither));
                    // 解析额外效果
                    if (extra != null && !extra.trim().isEmpty()) {
                        String[] parts = extra.split(",");
                        for (String part : parts) {
                            String[] kv = part.trim().split(":");
                            if (kv.length >= 1) {
                                try {
                                    PotionEffectType type = PotionEffectType.getByName(kv[0].trim().toUpperCase());
                                    int amp = (kv.length >= 2) ? Math.max(0, Integer.parseInt(kv[1].trim())) : 0;
                                    if (type != null) p.addPotionEffect(new PotionEffect(type, duration, amp));
                                } catch (Exception ignored) {}
                            }
                        }
                    }
                }
            }, interval, interval);
            return task.getTaskId();
        }

        // 默认：通用范围buff/debuff处理
        int interval = toInt(cfg.getParameter("buff_interval",
                      cfg.getParameter("aura_interval",
                      cfg.getParameter("interval", 100))));

        double range = toDouble(cfg.getParameter("buff_range",
                       cfg.getParameter("aura_range",
                       cfg.getParameter("range", 10.0))));

        int duration = toInt(cfg.getParameter("buff_duration",
                      cfg.getParameter("effect_duration",
                      cfg.getParameter("duration", 100))));

        int amplifier = toInt(cfg.getParameter("buff_amplifier",
                       cfg.getParameter("effect_amplifier",
                       cfg.getParameter("amplifier", 1))));

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, duration=%d, amplifier=%d (从%d个参数中读取)",
                   rawName, interval, range, duration, amplifier, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            World world = caster.getWorld();
            Location center = caster.getLocation();

            for (Player p : world.getPlayers()) {
                if (!p.isOnline() || p.isDead()) continue;
                if (p.getLocation().distanceSquared(center) <= range * range) {
                    PotionEffectType effectType = getAreaEffectType(rawName);
                    if (effectType != null) {
                        p.addPotionEffect(new PotionEffect(effectType, duration, amplifier));
                    }
                }
            }
        }, Math.max(1, interval), Math.max(1, interval));
        return task.getTaskId();
    }

    /**
     * 陷阱控制类技能：地面网、柱、击飞等
     */
    public Integer handleTrapControl(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        logger.info("陷阱技能参数读取 - " + cfg.getSkillName() + ": " + allParams);

        String skillName = cfg.getSkillName();

        // 根据技能名称读取对应的参数（为蛛网陷阱优先读取 trap_interval/web_range）
        int interval = toInt(cfg.getParameter("trap_interval",
                      cfg.getParameter("attack_interval",
                      cfg.getParameter("interval",
                      cfg.getParameter("obsidian_interval", 120)))));

        double range = toDouble(cfg.getParameter("web_range",
                       cfg.getParameter("range",
                       cfg.getParameter("obsidian_range", 8.0))));

        // 读取技能特定参数
        int blockCount = toInt(cfg.getParameter("block_count", 5));
        double explosionPower = toDouble(cfg.getParameter("explosion_power", 2.0));
        double damage = toDouble(cfg.getParameter("obsidian_damage",
                        cfg.getParameter("damage", 5.0)));

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, blockCount=%d, explosionPower=%.2f, damage=%.2f (从%d个参数中读取)",
                   skillName, interval, range, blockCount, explosionPower, damage, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                // 特殊处理fence_attack，需要caster参数
                if (skillName.contains("fence")) {
                    performFenceAttackWithCaster(caster, target, cfg);
                } else {
                    applyTrapEffect(target, skillName, cfg);
                }
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 位移类技能：冲刺、瞬移、飞行
     */
    public Integer handleDashTeleport(LivingEntity caster, SkillConfig cfg) {
        // 兼容多种参数名（优先读取 flight_* 以适配马匹飞行）
        Object intervalParam = cfg.getParameter("flight_interval",
                cfg.getParameter("teleport_interval",
                cfg.getParameter("skill_interval",
                cfg.getParameter("interval", 200))));
        int interval = toInt(intervalParam);
        Object rangeParam = cfg.getParameter("flight_range",
                cfg.getParameter("teleport_range",
                cfg.getParameter("range", 10.0)));
        double range = toDouble(rangeParam);

        double damage = toDouble(cfg.getParameter("teleport_damage", 0.0));
        String skillName = cfg.getSkillName();

        // 飞行类技能建议更高刷新频率，保证运动平滑
        if (skillName != null && skillName.toLowerCase().contains("flight")) {
            interval = Math.min(interval, 10);
        }

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f", skillName, interval, range, damage));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                applyMovementEffect(caster, target, skillName, cfg);
                if (damage > 0) {
                    target.damage(damage, caster);
                }
            }
        }, Math.max(1, interval), Math.max(1, interval));
        return task.getTaskId();
    }

    /**
     * 命中效果类技能：攻击时触发的效果（需要事件监听）
     */
    public Integer handleOnHitEffects(LivingEntity caster, SkillConfig cfg) {
        String skillName = cfg.getSkillName();
        logger.info(String.format("挂载技能[%s]: 事件触发型技能已注册", skillName));

        // 设置元数据标记，供事件监听器使用
        caster.setMetadata("idz_onhit_" + skillName, new FixedMetadataValue(plugin, cfg));
        return null; // 不需要定时任务
    }

    /**
     * 机枪类技能：IDC14风格的连续射击
     */
    public Integer handleMachineGun(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("机枪技能参数读取 - " + skillName + ": " + allParams);

        // 读取机枪参数
        int interval = toInt(cfg.getParameter("machine_gun_interval",
                      cfg.getParameter("gun_interval",
                      cfg.getParameter("interval", 20)))); // 默认1秒

        double range = toDouble(cfg.getParameter("machine_gun_range",
                       cfg.getParameter("gun_range",
                       cfg.getParameter("range", 30.0)))); // 默认30格

        int bulletCount = toInt(cfg.getParameter("bullet_count",
                         cfg.getParameter("gun_bullets",
                         cfg.getParameter("count", 8)))); // 默认8发子弹

        double bulletDamage = toDouble(cfg.getParameter("bullet_damage",
                             cfg.getParameter("gun_damage",
                             cfg.getParameter("damage", 3.0)))); // 默认3点伤害

        double spreadAngle = toDouble(cfg.getParameter("spread_angle",
                            cfg.getParameter("gun_spread",
                            cfg.getParameter("spread", 15.0)))); // 默认15度散布

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, bullets=%d, damage=%.2f, spread=%.2f (从%d个参数中读取)",
                   skillName, interval, range, bulletCount, bulletDamage, spreadAngle, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                performMachineGunAttack(caster, target, bulletCount, bulletDamage, spreadAngle);
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 烟雾粒子类技能：IDC14风格的烟雾粒子环
     */
    public Integer handleSmokeParticles(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("烟雾技能参数读取 - " + skillName + ": " + allParams);

        // 读取烟雾参数
        int interval = toInt(cfg.getParameter("smoke_interval",
                      cfg.getParameter("particle_interval",
                      cfg.getParameter("interval", 10)))); // 默认0.5秒

        double range = toDouble(cfg.getParameter("smoke_range",
                       cfg.getParameter("particle_range",
                       cfg.getParameter("range", 8.0)))); // 默认8格

        int blindnessDuration = toInt(cfg.getParameter("blindness_duration",
                               cfg.getParameter("smoke_duration",
                               cfg.getParameter("duration", 100)))); // 默认5秒

        int blindnessLevel = toInt(cfg.getParameter("blindness_level",
                            cfg.getParameter("smoke_level",
                            cfg.getParameter("level", 1)))); // 默认1级

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, duration=%d, level=%d (从%d个参数中读取)",
                   skillName, interval, range, blindnessDuration, blindnessLevel, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performSmokeParticleEffect(caster, range, blindnessDuration, blindnessLevel);
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 冰霜光环类技能：IDC13风格的冰霜粒子环
     */
    public Integer handleFrostAura(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("冰霜光环技能参数读取 - " + skillName + ": " + allParams);

        // 读取冰霜光环参数
        int interval = toInt(cfg.getParameter("frost_interval",
                      cfg.getParameter("aura_interval",
                      cfg.getParameter("interval", 40)))); // 默认2秒

        double range = toDouble(cfg.getParameter("frost_range",
                       cfg.getParameter("aura_range",
                       cfg.getParameter("range", 6.0)))); // 默认6格

        int slownessDuration = toInt(cfg.getParameter("slowness_duration",
                              cfg.getParameter("frost_duration",
                              cfg.getParameter("duration", 100)))); // 默认5秒

        int slownessLevel = toInt(cfg.getParameter("slowness_level",
                           cfg.getParameter("frost_level",
                           cfg.getParameter("level", 2)))); // 默认2级

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, duration=%d, level=%d (从%d个参数中读取)",
                   skillName, interval, range, slownessDuration, slownessLevel, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performFrostAuraEffect(caster, range, slownessDuration, slownessLevel);
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 冲刺攻击类技能：IDC14风格的冲刺攻击
     */
    public Integer handleDashAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("冲刺攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取冲刺攻击参数
        int interval = toInt(cfg.getParameter("dash_interval",
                      cfg.getParameter("attack_interval",
                      cfg.getParameter("interval", 150)))); // 默认7.5秒

        double range = toDouble(cfg.getParameter("dash_range",
                       cfg.getParameter("attack_range",
                       cfg.getParameter("range", 8.0)))); // 默认8格

        double damage = toDouble(cfg.getParameter("dash_damage",
                        cfg.getParameter("attack_damage",
                        cfg.getParameter("damage", 6.0)))); // 默认6点伤害

        double speed = toDouble(cfg.getParameter("dash_speed",
                       cfg.getParameter("movement_speed",
                       cfg.getParameter("speed", 2.0)))); // 默认2倍速度

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, speed=%.2f (从%d个参数中读取)",
                   skillName, interval, range, damage, speed, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                performDashAttack(caster, target, damage, speed);
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 红色粒子球类技能：IDC15风格的红色粒子环
     */
    public Integer handleRedParticleBall(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("红色粒子球技能参数读取 - " + skillName + ": " + allParams);

        // 读取红色粒子球参数
        int interval = toInt(cfg.getParameter("particle_interval",
                      cfg.getParameter("ball_interval",
                      cfg.getParameter("interval", 20)))); // 默认1秒

        double radius = toDouble(cfg.getParameter("particle_radius",
                        cfg.getParameter("ball_radius",
                        cfg.getParameter("radius", 2.0)))); // 默认2格半径

        int particleCount = toInt(cfg.getParameter("particle_count",
                           cfg.getParameter("ball_count",
                           cfg.getParameter("count", 16)))); // 默认16个粒子点

        logger.info(String.format("挂载技能[%s]: interval=%d, radius=%.2f, count=%d (从%d个参数中读取)",
                   skillName, interval, radius, particleCount, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performRedParticleBallEffect(caster, radius, particleCount);
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 潜影贝子弹类技能：IDC16风格的三连射潜影贝子弹
     */
    public Integer handleShulkerBullet(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("潜影贝子弹技能参数读取 - " + skillName + ": " + allParams);

        // 读取潜影贝子弹参数
        int interval = toInt(cfg.getParameter("bullet_attack_interval",
                      cfg.getParameter("attack_interval",
                      cfg.getParameter("interval", 120)))); // 默认6秒

        double range = toDouble(cfg.getParameter("attack_range",
                       cfg.getParameter("bullet_range",
                       cfg.getParameter("range", 15.0)))); // 默认15格

        int bulletCount = toInt(cfg.getParameter("bullet_count",
                         cfg.getParameter("count", 3))); // 默认3发子弹

        double bulletDamage = toDouble(cfg.getParameter("bullet_damage",
                             cfg.getParameter("damage", 4.0))); // 默认4点伤害

        int levitationDuration = toInt(cfg.getParameter("levitation_duration",
                                cfg.getParameter("float_duration", 100))); // 默认5秒漂浮

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, count=%d, damage=%.2f, levitation=%d (从%d个参数中读取)",
                   skillName, interval, range, bulletCount, bulletDamage, levitationDuration, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                performShulkerBulletAttack(caster, target, bulletCount, bulletDamage, levitationDuration);
            }
        }, interval, interval);
        return task.getTaskId();
    }

    /**
     * 潜影贝混乱类技能：IDC16风格的受伤混乱物品栏
     */
    public Integer handleShulkerInventoryChaos(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("潜影贝混乱技能参数读取 - " + skillName + ": " + allParams);

        // 读取潜影贝混乱参数
        double healthThreshold = toDouble(cfg.getParameter("health_threshold",
                                 cfg.getParameter("damage_threshold", 0.7))); // 默认70%血量

        double shuffleRange = toDouble(cfg.getParameter("shuffle_range",
                              cfg.getParameter("chaos_range",
                              cfg.getParameter("range", 8.0)))); // 默认8格

        long shuffleCooldown = (long)(toDouble(cfg.getParameter("shuffle_cooldown",
                              cfg.getParameter("chaos_cooldown", 10000)))); // 默认10秒冷却

        int damageCheckInterval = toInt(cfg.getParameter("damage_check_interval",
                                 cfg.getParameter("check_interval", 20))); // 默认1秒检查一次

        logger.info(String.format("挂载技能[%s]: threshold=%.2f, range=%.2f, cooldown=%dms, checkInterval=%d (从%d个参数中读取)",
                   skillName, healthThreshold, shuffleRange, shuffleCooldown, damageCheckInterval, allParams.size()));

        // 设置元数据用于受伤检测
        caster.setMetadata("shulker_chaos_threshold", new org.bukkit.metadata.FixedMetadataValue(plugin, healthThreshold));
        caster.setMetadata("shulker_chaos_range", new org.bukkit.metadata.FixedMetadataValue(plugin, shuffleRange));
        caster.setMetadata("shulker_chaos_cooldown", new org.bukkit.metadata.FixedMetadataValue(plugin, shuffleCooldown));
        caster.setMetadata("shulker_chaos_last_time", new org.bukkit.metadata.FixedMetadataValue(plugin, 0L));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            checkAndPerformShulkerChaos(caster);
        }, damageCheckInterval, damageCheckInterval);
        return task.getTaskId();
    }

    /**
     * 潜影贝隐身类技能：IDC16/IDC19风格的周期性隐身
     */
    public Integer handleShulkerInvisibility(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("潜影贝隐身技能参数读取 - " + skillName + ": " + allParams);

        // 读取隐身参数
        int invisibilityDuration = toInt(cfg.getParameter("invisibility_duration",
                                  cfg.getParameter("stealth_duration",
                                  cfg.getParameter("duration", 60)))); // 默认3秒隐身

        int invisibilityInterval = toInt(cfg.getParameter("invisibility_interval",
                                  cfg.getParameter("stealth_interval",
                                  cfg.getParameter("interval", 200)))); // 默认10秒间隔

        double invisibilityChance = toDouble(cfg.getParameter("invisibility_chance",
                                    cfg.getParameter("stealth_chance",
                                    cfg.getParameter("chance", 0.8)))); // 默认80%概率

        logger.info(String.format("挂载技能[%s]: duration=%d, interval=%d, chance=%.2f (从%d个参数中读取)",
                   skillName, invisibilityDuration, invisibilityInterval, invisibilityChance, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            if (Math.random() <= invisibilityChance) {
                performShulkerInvisibility(caster, invisibilityDuration);
            }
        }, invisibilityInterval, invisibilityInterval);
        return task.getTaskId();
    }

    /**
     * 雪球弹幕类技能：IDC17风格的连发雪球攻击
     */
    public Integer handleSnowballBarrage(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("雪球弹幕技能参数读取 - " + skillName + ": " + allParams);

        // 读取雪球弹幕参数
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("barrage_interval",
                            cfg.getParameter("interval", 100)))); // 默认5秒间隔

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("barrage_range",
                            cfg.getParameter("range", 15.0)))); // 默认15格

        int snowballCount = toInt(cfg.getParameter("snowball_count",
                           cfg.getParameter("barrage_count",
                           cfg.getParameter("count", 5)))); // 默认5个雪球

        int snowballInterval = toInt(cfg.getParameter("snowball_interval",
                              cfg.getParameter("shot_interval", 5))); // 默认5tick间隔

        double spreadFactor = toDouble(cfg.getParameter("spread_factor",
                             cfg.getParameter("barrage_spread",
                             cfg.getParameter("spread", 0.3)))); // 默认0.3散布

        double snowballSpeed = toDouble(cfg.getParameter("snowball_speed",
                              cfg.getParameter("projectile_speed",
                              cfg.getParameter("speed", 1.5)))); // 默认1.5倍速度

        double snowballDamage = toDouble(cfg.getParameter("snowball_damage",
                               cfg.getParameter("barrage_damage",
                               cfg.getParameter("damage", 3.0)))); // 默认3点伤害

        double knockbackStrength = toDouble(cfg.getParameter("knockback_strength",
                                   cfg.getParameter("knockback", 0.5))); // 默认0.5倍击退

        boolean poisonEnabled = cfg.getParameter("poison_enabled", false).toString().equalsIgnoreCase("true") ||
                               cfg.getParameter("poison_snowball", false).toString().equalsIgnoreCase("true");

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, count=%d, shotInterval=%d, spread=%.2f, speed=%.2f, damage=%.2f, knockback=%.2f, poison=%b (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, snowballCount, snowballInterval, spreadFactor, snowballSpeed, snowballDamage, knockbackStrength, poisonEnabled, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performSnowballBarrage(caster, target, snowballCount, snowballInterval, spreadFactor, snowballSpeed, snowballDamage, knockbackStrength, poisonEnabled);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 雪人冰冻光环类技能：IDC17风格的冰冻光环
     */
    public Integer handleSnowFreezeAura(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("雪人冰冻光环技能参数读取 - " + skillName + ": " + allParams);

        // 读取冰冻光环参数
        int freezeInterval = toInt(cfg.getParameter("freeze_interval",
                            cfg.getParameter("aura_interval",
                            cfg.getParameter("interval", 120)))); // 默认6秒间隔

        double freezeRange = toDouble(cfg.getParameter("freeze_range",
                            cfg.getParameter("aura_range",
                            cfg.getParameter("range", 8.0)))); // 默认8格

        int freezeDuration = toInt(cfg.getParameter("freeze_duration",
                            cfg.getParameter("effect_duration",
                            cfg.getParameter("duration", 100)))); // 默认5秒持续

        int slownessLevel = toInt(cfg.getParameter("slowness_level",
                           cfg.getParameter("freeze_level",
                           cfg.getParameter("level", 2)))); // 默认2级缓慢

        int miningFatigueLevel = toInt(cfg.getParameter("mining_fatigue_level",
                                cfg.getParameter("fatigue_level", 1))); // 默认1级挖掘疲劳

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, duration=%d, slowness=%d, fatigue=%d (从%d个参数中读取)",
                   skillName, freezeInterval, freezeRange, freezeDuration, slownessLevel, miningFatigueLevel, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performSnowFreezeAura(caster, freezeRange, freezeDuration, slownessLevel, miningFatigueLevel);
        }, freezeInterval, freezeInterval);
        return task.getTaskId();
    }

    /**
     * 草方块伸展攻击类技能：IDC18风格的草方块延伸攻击
     */
    public Integer handleGrassBlockAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("草方块伸展攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取草方块攻击参数
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("grass_interval", 100))); // 默认5秒间隔

        double attackRange = toDouble(cfg.getParameter("grass_range",
                            cfg.getParameter("attack_range", 20.0))); // 默认20格

        double grassDamage = toDouble(cfg.getParameter("grass_damage",
                            cfg.getParameter("damage", 44.0))); // 默认44点伤害

        double grassSpeed = toDouble(cfg.getParameter("grass_speed",
                           cfg.getParameter("speed", 1.0))); // 默认1.0格/tick

        int slownessDuration = toInt(cfg.getParameter("slowness_duration",
                              cfg.getParameter("freeze_duration", 20))); // 默认1秒禁止移动

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, speed=%.2f, slowness=%d (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, grassDamage, grassSpeed, slownessDuration, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performGrassBlockAttack(caster, target, grassDamage, grassSpeed, slownessDuration);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 主动追踪玩家类技能：IDC18风格的智能追踪
     */
    public Integer handlePlayerTracking(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("主动追踪玩家技能参数读取 - " + skillName + ": " + allParams);

        // 读取追踪参数
        int trackingInterval = toInt(cfg.getParameter("tracking_interval",
                              cfg.getParameter("interval", 20))); // 默认1秒间隔

        double trackingRange = toDouble(cfg.getParameter("tracking_range",
                              cfg.getParameter("range", 20.0))); // 默认20格

        double trackingSpeed = toDouble(cfg.getParameter("tracking_speed",
                              cfg.getParameter("speed", 1.2))); // 默认1.2倍速度

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, speed=%.2f (从%d个参数中读取)",
                   skillName, trackingInterval, trackingRange, trackingSpeed, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performPlayerTracking(caster, trackingRange, trackingSpeed);
        }, trackingInterval, trackingInterval);
        return task.getTaskId();
    }

    /**
     * 声波弹攻击类技能：IDC18风格的声波弹攻击
     */
    public Integer handleSonicAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("声波弹攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取声波弹攻击参数
        int attackInterval = toInt(cfg.getParameter("sonic_interval",
                            cfg.getParameter("attack_interval", 80))); // 默认4秒间隔

        double attackRange = toDouble(cfg.getParameter("sonic_range",
                            cfg.getParameter("attack_range", 15.0))); // 默认15格

        double sonicDamage = toDouble(cfg.getParameter("sonic_damage",
                            cfg.getParameter("damage", 8.0))); // 默认8点伤害

        double sonicSpeed = toDouble(cfg.getParameter("sonic_speed",
                           cfg.getParameter("speed", 1.5))); // 默认1.5倍速度

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, speed=%.2f (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, sonicDamage, sonicSpeed, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performSonicAttack(caster, target, sonicDamage, sonicSpeed);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 天气控制类技能：IDC19风格的天气控制
     */
    public Integer handleWeatherControl(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("天气控制技能参数读取 - " + skillName + ": " + allParams);

        // 读取天气控制参数
        int weatherInterval = toInt(cfg.getParameter("weather_interval",
                             cfg.getParameter("interval", 600))); // 默认30秒间隔

        int stormDuration = toInt(cfg.getParameter("storm_duration",
                           cfg.getParameter("duration", 400))); // 默认20秒持续

        double lightningChance = toDouble(cfg.getParameter("lightning_chance",
                                cfg.getParameter("chance", 0.8))); // 默认80%概率

        logger.info(String.format("挂载技能[%s]: interval=%d, duration=%d, chance=%.2f (从%d个参数中读取)",
                   skillName, weatherInterval, stormDuration, lightningChance, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performWeatherControl(caster, stormDuration, lightningChance);
        }, weatherInterval, weatherInterval);
        return task.getTaskId();
    }

    /**
     * 三叉戟攻击类技能：IDC19风格的三叉戟攻击
     */
    public Integer handleTridentAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("三叉戟攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取三叉戟攻击参数
        int attackInterval = toInt(cfg.getParameter("trident_interval",
                            cfg.getParameter("attack_interval", 100))); // 默认5秒间隔

        double attackRange = toDouble(cfg.getParameter("trident_range",
                            cfg.getParameter("attack_range", 20.0))); // 默认20格

        double tridentDamage = toDouble(cfg.getParameter("trident_damage",
                              cfg.getParameter("damage", 10.0))); // 默认10点伤害

        boolean loyaltyEnabled = cfg.getParameter("loyalty_enabled", true).toString().equalsIgnoreCase("true");

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, loyalty=%b (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, tridentDamage, loyaltyEnabled, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performTridentAttack(caster, target, tridentDamage, loyaltyEnabled);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 粒子攻击类技能：IDC19风格的粒子攻击
     */
    public Integer handleParticleAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("粒子攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取粒子攻击参数
        int attackInterval = toInt(cfg.getParameter("particle_interval",
                            cfg.getParameter("attack_interval", 80))); // 默认4秒间隔

        double attackRange = toDouble(cfg.getParameter("particle_range",
                            cfg.getParameter("attack_range", 12.0))); // 默认12格

        double particleDamage = toDouble(cfg.getParameter("particle_damage",
                               cfg.getParameter("damage", 6.0))); // 默认6点伤害

        int particleCount = toInt(cfg.getParameter("particle_count",
                           cfg.getParameter("count", 8))); // 默认8个粒子

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, count=%d (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, particleDamage, particleCount, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performParticleAttack(caster, target, particleDamage, particleCount);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 闪电攻击类技能：IDC19风格的闪电攻击
     */
    public Integer handleLightningAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("闪电攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取闪电攻击参数
        int attackInterval = toInt(cfg.getParameter("lightning_interval",
                            cfg.getParameter("attack_interval", 120))); // 默认6秒间隔

        double attackRange = toDouble(cfg.getParameter("lightning_range",
                            cfg.getParameter("attack_range", 15.0))); // 默认15格

        double lightningDamage = toDouble(cfg.getParameter("lightning_damage",
                                cfg.getParameter("damage", 12.0))); // 默认12点伤害

        boolean chainLightning = cfg.getParameter("chain_lightning", false).toString().equalsIgnoreCase("true");

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, chain=%b (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, lightningDamage, chainLightning, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performLightningAttack(caster, target, lightningDamage, chainLightning);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 隐身能力类技能：IDC19风格的周期性隐身
     */
    public Integer handleInvisibilitySkill(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("隐身能力技能参数读取 - " + skillName + ": " + allParams);

        // 读取隐身参数
        int invisibilityInterval = toInt(cfg.getParameter("invisibility_interval",
                                  cfg.getParameter("interval", 300))); // 默认15秒间隔

        int invisibilityDuration = toInt(cfg.getParameter("invisibility_duration",
                                  cfg.getParameter("duration", 100))); // 默认5秒隐身

        double invisibilityChance = toDouble(cfg.getParameter("invisibility_chance",
                                    cfg.getParameter("chance", 0.7))); // 默认70%概率

        logger.info(String.format("挂载技能[%s]: interval=%d, duration=%d, chance=%.2f (从%d个参数中读取)",
                   skillName, invisibilityInterval, invisibilityDuration, invisibilityChance, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            if (Math.random() <= invisibilityChance) {
                performInvisibilitySkill(caster, invisibilityDuration);
            }
        }, invisibilityInterval, invisibilityInterval);
        return task.getTaskId();
    }

    /**
     * 黑曜石方块攻击类技能：IDC20风格的黑曜石延伸攻击
     */
    public Integer handleObsidianAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("黑曜石方块攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取黑曜石攻击参数
        int attackInterval = toInt(cfg.getParameter("obsidian_interval",
                            cfg.getParameter("attack_interval", 100))); // 默认5秒间隔

        double attackRange = toDouble(cfg.getParameter("obsidian_range",
                            cfg.getParameter("attack_range", 20.0))); // 默认20格

        double obsidianDamage = toDouble(cfg.getParameter("obsidian_damage",
                               cfg.getParameter("damage", 15.0))); // 默认15点伤害

        double obsidianSpeed = toDouble(cfg.getParameter("obsidian_speed",
                              cfg.getParameter("speed", 1.2))); // 默认1.2格/tick

        int slownessDuration = toInt(cfg.getParameter("slowness_duration",
                              cfg.getParameter("freeze_duration", 40))); // 默认2秒禁止移动

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, speed=%.2f, slowness=%d (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, obsidianDamage, obsidianSpeed, slownessDuration, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performObsidianAttack(caster, target, obsidianDamage, obsidianSpeed, slownessDuration);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 击飞黑曜石柱类技能：IDC20风格的击飞+黑曜石柱
     */
    public Integer handleKnockupPillar(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("击飞黑曜石柱技能参数读取 - " + skillName + ": " + allParams);

        // 读取击飞黑曜石柱参数
        int attackInterval = toInt(cfg.getParameter("knockup_interval",
                            cfg.getParameter("attack_interval", 150))); // 默认7.5秒间隔

        double attackRange = toDouble(cfg.getParameter("knockup_range",
                            cfg.getParameter("attack_range", 15.0))); // 默认15格

        double knockupStrength = toDouble(cfg.getParameter("knockup_strength",
                                cfg.getParameter("strength", 1.5))); // 默认1.5倍击飞

        double pillarDamage = toDouble(cfg.getParameter("pillar_damage",
                             cfg.getParameter("damage", 20.0))); // 默认20点伤害

        int pillarHeight = toInt(cfg.getParameter("pillar_height",
                          cfg.getParameter("height", 5))); // 默认5格高

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, knockup=%.2f, damage=%.2f, height=%d (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, knockupStrength, pillarDamage, pillarHeight, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performKnockupPillar(caster, target, knockupStrength, pillarDamage, pillarHeight);
            }
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 特殊效果类技能：库存混乱、时间控制等
     */
    public Integer handleSpecialEffects(LivingEntity caster, SkillConfig cfg) {
        String skillName = cfg.getSkillName();
        String lower = skillName == null ? "" : skillName.toLowerCase();

        // 专用：冻结能力（周期性对范围内实体施加缓慢与挖掘疲劳）
        if (lower.contains("freeze_ability") || skillName.contains("冻结能力")) {
            int interval = Math.max(1, toInt(cfg.getParameter("freeze_interval", cfg.getParameter("interval", 200))));
            double range = Math.max(0.1, toDouble(cfg.getParameter("freeze_range", cfg.getParameter("range", 5.0))));
            int duration = Math.max(1, toInt(cfg.getParameter("freeze_duration", cfg.getParameter("duration", 100))));
            int slowLvl = Math.max(0, toInt(cfg.getParameter("slowness_level", 2)));
            int miningLvl = Math.max(0, toInt(cfg.getParameter("mining_level", 1)));
            boolean affectPlayers = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_players", true)));
            boolean affectMobs = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_mobs", true)));
            int entityLimit = Math.max(0, toInt(cfg.getParameter("entity_limit", 0)));

            logger.info(String.format("挂载技能[%s-冻结能力]: interval=%d, range=%.2f, duration=%d, slow=%d, mining=%d", skillName, interval, range, duration, slowLvl, miningLvl));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                int applied = 0;
                for (Entity e : caster.getWorld().getNearbyEntities(caster.getLocation(), range, range, range)) {
                    if (!(e instanceof LivingEntity)) continue;
                    if (e.getUniqueId().equals(caster.getUniqueId())) continue;
                    if (entityLimit > 0 && applied >= entityLimit) break;

                    if (e instanceof Player) {
                        if (!affectPlayers) continue;
                        Player p = (Player) e;
                        if (!p.isOnline() || p.isDead()) continue;
                        p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, slowLvl));
                        p.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, duration, miningLvl));
                        applied++;
                    } else {
                        if (!affectMobs) continue;
                        LivingEntity le = (LivingEntity) e;
                        le.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, slowLvl));
                        le.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, duration, miningLvl));
                        applied++;
                    }
                }
            }, 1L, interval);
            return task.getTaskId();
        }

        // IDC8: DNA螺旋魔法（粒子+击中判定）
        if (lower.contains("dna_spiral") || skillName.contains("DNA螺旋魔法")) {
            int interval = Math.max(1, toInt(cfg.getParameter("spiral_interval", cfg.getParameter("attack_interval", 120))));
            double damage = Math.max(0.0, toDouble(cfg.getParameter("spiral_damage", cfg.getParameter("damage", 8.0))));
            double range = Math.max(1.0, toDouble(cfg.getParameter("spiral_range", cfg.getParameter("range", 25.0))));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                // 查找最近玩家
                Player target = findNearestPlayer(caster, range);
                if (target == null) return;

                final Location startLoc = caster.getLocation().add(0, 1.0, 0);
                final Location targetLoc = target.getLocation().add(0, 1.0, 0);
                final org.bukkit.util.Vector direction = targetLoc.toVector().subtract(startLoc.toVector()).normalize();

                new BukkitRunnable() {
                    private Location currentLoc = startLoc.clone();
                    private int ticks = 0;
                    private final int maxTicks = 80; // 最多飞行4秒
                    private double spiralAngle = 0;

                    @Override
                    public void run() {
                        if (!isAlive(caster) || ticks > maxTicks || currentLoc.getBlock().getType().isSolid()) {
                            this.cancel();
                            return;
                        }

                        // 移动
                        currentLoc.add(direction.clone().multiply(0.4));

                        // DNA双螺旋粒子
                        spiralAngle += 0.5;
                        for (int i = 0; i < 2; i++) {
                            double angle = spiralAngle + (i * Math.PI);
                            double offsetX = Math.cos(angle) * 0.5;
                            double offsetZ = Math.sin(angle) * 0.5;
                            Location particleLoc = currentLoc.clone().add(offsetX, 0, offsetZ);
                            try {
                                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.WITCH, particleLoc, 1, 0, 0, 0, 0);
                                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, currentLoc, 1, 0, 0, 0, 0);
                            } catch (Throwable ignored) {}
                        }

                        // 命中检测
                        for (org.bukkit.entity.Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                            if (entity instanceof Player) {
                                Player player = (Player) entity;
                                if (player.isDead() || !player.isOnline()) continue;
                                // 造成魔法伤害
                                player.damage(damage, caster);
                                // 视觉+音效
                                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, currentLoc, 3, 1, 1, 1, 0);
                                currentLoc.getWorld().playSound(currentLoc, org.bukkit.Sound.ENTITY_EVOKER_CAST_SPELL, 1.0f, 1.0f);
                                this.cancel();
                                return;
                            }
                        }
                        ticks++;
                    }
                }.runTaskTimer(plugin, 0, 1);
            }, interval, interval);
            return task.getTaskId();
        }

        // 专用：迷雾生成（粒子版，而非失明）
        if (lower.contains("fog_generation")) {
            int interval = Math.max(1, toInt(cfg.getParameter("fog_interval", cfg.getParameter("interval", 10))));
            double range = Math.max(0.1, toDouble(cfg.getParameter("fog_range", cfg.getParameter("particle_range", 5.0))));
            double height = Math.max(0.1, toDouble(cfg.getParameter("fog_height", cfg.getParameter("particle_height", cfg.getParameter("vertical_range", 3.0)))));
            int count = Math.max(1, toInt(cfg.getParameter("fog_particle_count", cfg.getParameter("particle_count", 80))));
            int duration = Math.max(0, toInt(cfg.getParameter("fog_duration", cfg.getParameter("duration", 200))));
            org.bukkit.Particle type = org.bukkit.Particle.CLOUD;

            logger.info(String.format("挂载技能[%s-迷雾生成]: interval=%d, range=%.2f, height=%.2f, count=%d, duration=%d", skillName, interval, range, height, count, duration));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                org.bukkit.Location loc = caster.getLocation();
                caster.getWorld().spawnParticle(type, loc, count, range, height, range, 0.02);
            }, 1L, interval);

            if (duration > 0) {
                Bukkit.getScheduler().runTaskLater(plugin, () -> task.cancel(), duration);
            }
            return task.getTaskId();
        }
        // 火焰光环（使用真实火焰粒子），并支持参数读取
        if (lower.contains("flame_ring") || (skillName != null && skillName.contains("火焰光环"))) {
            int interval = Math.max(1, toInt(cfg.getParameter("flame_interval", cfg.getParameter("damage_interval", cfg.getParameter("aura_interval", cfg.getParameter("interval", 20))))));
            double range = Math.max(0.1, toDouble(cfg.getParameter("flame_range", cfg.getParameter("aura_range", cfg.getParameter("range", 3.0)))));
            double damage = Math.max(0.0, toDouble(cfg.getParameter("flame_damage", cfg.getParameter("aura_damage", cfg.getParameter("damage", 2.0)))));
            int burnTicks = Math.max(0, toInt(cfg.getParameter("burn_ticks", cfg.getParameter("flame_burn_ticks", cfg.getParameter("burn_duration", 60)))));
            int particleCount = Math.max(1, toInt(cfg.getParameter("flame_particle_count", 24)));
            double ringHeight = Math.max(0.0, toDouble(cfg.getParameter("flame_ring_height", 0.4)));
            double ringThickness = Math.max(0.0, toDouble(cfg.getParameter("flame_ring_thickness", 0.1)));

            logger.info(String.format("挂载技能[%s-火焰光环]: interval=%d, range=%.2f, damage=%.2f, burnTicks=%d, particles=%d, height=%.2f, thick=%.2f",
                    skillName, interval, range, damage, burnTicks, particleCount, ringHeight, ringThickness));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                Location c = caster.getLocation();
                World w = caster.getWorld();

                // 绘制环绕火焰粒子（圆环）
                int segments = Math.max(12, particleCount);
                for (int i = 0; i < segments; i++) {
                    double ang = 2 * Math.PI * i / segments;
                    double x = Math.cos(ang) * range;
                    double z = Math.sin(ang) * range;
                    Location pLoc = c.clone().add(x, ringHeight, z);
                    try {
                        w.spawnParticle(org.bukkit.Particle.FLAME, pLoc, 1, ringThickness, ringThickness, ringThickness, 0.01);
                        // 少量烟雾更贴近火焰效果
                        if ((i % 3) == 0) w.spawnParticle(org.bukkit.Particle.SMOKE, pLoc, 1, 0.02, 0.02, 0.02, 0.0);
                    } catch (Throwable ignored) {}
                }

                // 伤害并点燃范围内玩家
                for (Player p : w.getPlayers()) {
                    if (p.isDead() || !p.isOnline()) continue;
                    if (p.getWorld() != w) continue;
                    if (p.getLocation().distanceSquared(c) <= range * range) {
                        if (damage > 0) p.damage(damage, caster);
                        if (burnTicks > 0) p.setFireTicks(Math.max(burnTicks, p.getFireTicks()));
                    }
                }
            }, 1L, interval);
            return task.getTaskId();
        }

        int interval = toInt(cfg.getParameter("time_interval", cfg.getParameter("time_slow_interval", cfg.getParameter("interval", 300))));
        double range = toDouble(cfg.getParameter("time_range", cfg.getParameter("control_range", cfg.getParameter("range", 10.0))));

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f", skillName, interval, range));

        // IDC8: 圈式内收缩尖牙攻击（在特殊效果处理器中实现为主动技）
        if (lower.contains("fang_attack") || skillName.contains("尖牙攻击")) {
            int atkInterval = Math.max(1, toInt(cfg.getParameter("attack_interval", interval)));
            int outerCount = Math.max(1, toInt(cfg.getParameter("fang_count", 16))); // 外圈数量
            double outerRadius = Math.max(0.5, toDouble(cfg.getParameter("fang_radius", 4.0)));

            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (!isAlive(caster)) return;
                Player target = findNearestPlayer(caster, range);
                if (target == null) return;

                final org.bukkit.entity.Evoker owner = (caster instanceof org.bukkit.entity.Evoker) ? (org.bukkit.entity.Evoker) caster : null;
                final Location playerLoc = target.getLocation();

                // 外圈
                createFangCircle(playerLoc, owner, outerCount, outerRadius);
                // 中圈（数量减少、半径缩小）
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (!isAlive(caster)) return;
                    createFangCircle(playerLoc, owner, Math.max(1, outerCount - 4), Math.max(0.5, outerRadius - 1.5));
                    // 内圈
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        if (!isAlive(caster)) return;
                        createFangCircle(playerLoc, owner, Math.max(1, outerCount - 8), Math.max(0.5, outerRadius - 3.0));
                        // 中心
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            if (!isAlive(caster)) return;
                            createCenterFang(playerLoc, owner);
                        }, 5L);
                    }, 10L);
                }, 10L);
            }, atkInterval, atkInterval);
            return task.getTaskId();
        }
        // 尝试读取 GUI 模板中的 slow_multiplier（0.1~0.9，表示减速倍数），并折算为药水等级（可选）
        double slowMultiplier = toDouble(cfg.getParameter("slow_multiplier", 0.0));


        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;

            // 特化：时间控制 —— 指定范围内的所有生物减速，但施法者不受影响
            if (lower.contains("time_control") || skillName.contains("时间控制") || skillName.contains("時間控制")) {
                int duration = Math.max(1, toInt(cfg.getParameter("time_duration", cfg.getParameter("time_slow_duration", cfg.getParameter("duration", interval)))));
                int amplifier = Math.max(0, toInt(cfg.getParameter("time_amplifier", cfg.getParameter("slowness_level", cfg.getParameter("amplifier", 2)))));
                // 如果提供了 slow_multiplier（0.1~0.9），优先用它推导药水等级（1=20%~40%，2=50%~60%，3=70%~80%）
                if (slowMultiplier > 0.0) {
                    if (slowMultiplier <= 0.4) amplifier = Math.max(amplifier, 1);
                    else if (slowMultiplier <= 0.6) amplifier = Math.max(amplifier, 2);
                    else amplifier = Math.max(amplifier, 3);
                }
                boolean affectPlayers = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_players", true)));
                boolean affectMobs = Boolean.parseBoolean(String.valueOf(cfg.getParameter("affect_mobs", true)));
                int entityLimit = Math.max(0, toInt(cfg.getParameter("entity_limit", 0))); // 0 表示不限

                int applied = 0;
                for (Entity e : caster.getWorld().getNearbyEntities(caster.getLocation(), range, range, range)) {
                    if (!(e instanceof LivingEntity)) continue;
                    if (e.getUniqueId().equals(caster.getUniqueId())) continue; // 自身不受影响
                    if (entityLimit > 0 && applied >= entityLimit) break;

                    if (e instanceof Player) {
                        if (!affectPlayers) continue;
                        Player p = (Player) e;
                        if (!p.isOnline() || p.isDead()) continue;
                        p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, amplifier));
                        applied++;
                    } else {
                        if (!affectMobs) continue;
                        LivingEntity le = (LivingEntity) e;
                        le.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, amplifier));
                        applied++;
                    }
                }
                return; // 本tick已处理
            }

            // 其他特殊效果默认对最近玩家生效
            Player target = findNearestPlayer(caster, range);
            if (target != null) {
                applySpecialEffect(target, skillName, cfg);
            }
        }, interval, interval);
        return task.getTaskId();
    }

    // =========== IDC21凋零头颅攻击处理器 ===========

    /**
     * IDC21凋零头颅攻击处理器 - 复制原版performWitherLordSkullAttack逻辑
     */
    public Integer handleSkullAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("凋零头颅攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（复制原版参数名）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("skull_attack_interval",
                            cfg.getParameter("interval", 100)))); // 默认5秒

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("range", 30.0))); // 默认30格

        double skullDamage = toDouble(cfg.getParameter("skull_damage",
                            cfg.getParameter("damage", 12.0))); // 默认12点伤害

        double speedMultiplier = toDouble(cfg.getParameter("skull_speed",
                                cfg.getParameter("skull_speed_multiplier",
                                cfg.getParameter("speed_multiplier", 2.0)))); // 默认2倍速度

        int skullCount = toInt(cfg.getParameter("skull_count",
                        cfg.getParameter("count", 3))); // 默认3个头颅

        logger.info(String.format("挂载技能[%s]: interval=%d, range=%.2f, damage=%.2f, speed=%.2f, count=%d (从%d个参数中读取)",
                   skillName, attackInterval, attackRange, skullDamage, speedMultiplier, skullCount, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            performWitherSkullAttack(caster, attackRange, skullDamage, speedMultiplier, skullCount);
        }, attackInterval, attackInterval);
        return task.getTaskId();
    }

    /**
     * 执行凋零头颅攻击 - 复制原版UserCustomEntity.performWitherLordSkullAttack逻辑
     */
    private void performWitherSkullAttack(LivingEntity caster, double attackRange, double skullDamage,
                                         double speedMultiplier, int skullCount) {
        Location location = caster.getLocation();

        // 查找攻击范围内的所有玩家
        List<Player> targets = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    targets.add(player);
                }
            }
        }

        if (targets.isEmpty()) return;

        // 随机选择目标玩家
        Player target = targets.get((int) (Math.random() * targets.size()));

        // 播放攻击音效
        location.getWorld().playSound(location, Sound.ENTITY_WITHER_SHOOT, 1.0f, 1.0f);

        // 发射多个凋零头颅（蓝色头颅，高速）
        for (int i = 0; i < skullCount; i++) {
            // 添加随机偏移，避免头颅重叠
            Location targetLoc = target.getLocation().add(
                (Math.random() - 0.5) * 2,
                Math.random() * 2,
                (Math.random() - 0.5) * 2
            );

            WitherSkull skull = location.getWorld().spawn(location.add(0, 1, 0), WitherSkull.class);
            skull.setCharged(true); // 蓝色头颅
            skull.setShooter(caster);

            // 计算方向向量并设置高速度
            Vector direction = targetLoc.toVector().subtract(location.toVector()).normalize();
            skull.setVelocity(direction.multiply(speedMultiplier));

            // 设置自定义伤害（通过metadata）
            skull.setMetadata("custom_damage", new FixedMetadataValue(plugin, skullDamage));

            logger.info(String.format("IDC21凋零头颅攻击 - 目标: %s, 伤害: %.1f, 速度: %.1fx",
                       target.getName(), skullDamage, speedMultiplier));
        }
    }

    // =========== IDC22异变之王专门技能处理器 ===========

    /**
     * IDC22末影水晶攻击处理器 - 复制原版performMutationKingCrystalAttack逻辑
     */
    public Integer handleCrystalAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22末影水晶攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位，与其他IDZ技能一致）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("crystal_interval",
                            cfg.getParameter("interval", 300)))); // 默认300tick=15秒

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("range", 50.0))); // 默认50格

        double crystalDamage = toDouble(cfg.getParameter("crystal_damage", 15.0)); // 默认15点伤害
        double crystalHealAmount = toDouble(cfg.getParameter("crystal_heal_amount", 50.0)); // 默认50点治疗
        int crystalOrbitalCount = toInt(cfg.getParameter("crystal_orbital_count", 3)); // 默认3个环绕水晶

        logger.info(String.format("挂载IDC22技能[末影水晶攻击]: interval=%d tick, range=%.2f, damage=%.2f, heal=%.2f, count=%d (从%d个参数中读取)",
                   attackInterval, attackRange, crystalDamage, crystalHealAmount, crystalOrbitalCount, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performMutationKingCrystalAttack(caster, target, crystalDamage, crystalHealAmount, crystalOrbitalCount, cfg);
            }
        }, attackInterval, attackInterval); // 直接使用tick，不转换
        return task.getTaskId();
    }

    /**
     * IDC22龙息攻击处理器 - 复制原版performMutationKingBreathAttack逻辑
     */
    public Integer handleBreathAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22龙息攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("breath_interval",
                            cfg.getParameter("interval", 200)))); // 默认200tick=10秒

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("breath_range",
                            cfg.getParameter("range", 30.0)))); // 默认30格

        double breathDamage = toDouble(cfg.getParameter("breath_damage", 12.0)); // 默认12点伤害

        logger.info(String.format("挂载IDC22技能[龙息攻击]: interval=%d tick, range=%.2f, damage=%.2f (从%d个参数中读取)",
                   attackInterval, attackRange, breathDamage, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performMutationKingBreathAttack(caster, target, breathDamage);
            }
        }, attackInterval, attackInterval); // 直接使用tick
        return task.getTaskId();
    }

    /**
     * IDC22黑曜石块攻击处理器 - 复制原版performMutationKingShootObsidianBlocks逻辑
     */
    public Integer handleObsidianBlocksAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22黑曜石块攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("obsidian_blocks_interval",
                            cfg.getParameter("interval", 160)))); // 默认160tick=8秒

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("range", 50.0))); // 默认50格

        double blocksDamage = toDouble(cfg.getParameter("obsidian_blocks_damage", 12.0)); // 默认12点伤害
        int blocksWaves = toInt(cfg.getParameter("obsidian_blocks_waves", 5)); // 默认5波
        int blocksPerWave = toInt(cfg.getParameter("obsidian_blocks_per_wave", 7)); // 默认每波7个

        logger.info(String.format("挂载IDC22技能[黑曜石块攻击]: interval=%d tick, range=%.2f, damage=%.2f, waves=%d, per_wave=%d (从%d个参数中读取)",
                   attackInterval, attackRange, blocksDamage, blocksWaves, blocksPerWave, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performMutationKingShootObsidianBlocks(caster, target, blocksDamage, blocksWaves, blocksPerWave);
            }
        }, attackInterval, attackInterval); // 直接使用tick
        return task.getTaskId();
    }

    /**
     * IDC22黑曜石柱攻击处理器 - 复制原版performMutationKingObsidianPillarAttack逻辑
     */
    public Integer handleObsidianPillarAttack(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22黑曜石柱攻击技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("obsidian_pillar_interval",
                            cfg.getParameter("interval", 100)))); // 默认100tick=5秒

        double attackRange = toDouble(cfg.getParameter("attack_range",
                            cfg.getParameter("range", 50.0))); // 默认50格

        double pillarDamage = toDouble(cfg.getParameter("obsidian_pillar_damage", 18.0)); // 默认18点伤害
        int pillarCount = toInt(cfg.getParameter("obsidian_pillar_count", 3)); // 默认3个柱子

        logger.info(String.format("挂载IDC22技能[黑曜石柱攻击]: interval=%d tick, range=%.2f, damage=%.2f, count=%d (从%d个参数中读取)",
                   attackInterval, attackRange, pillarDamage, pillarCount, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, attackRange);
            if (target != null) {
                performMutationKingObsidianPillarAttack(caster, target, pillarDamage, pillarCount);
            }
        }, attackInterval, attackInterval); // 直接使用tick
        return task.getTaskId();
    }

    /**
     * IDC22末影粒子减速场处理器 - 复制原版performMutationKingEnderField逻辑
     */
    public Integer handleEnderField(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22末影粒子减速场技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("ender_field_interval",
                            cfg.getParameter("interval", 400)))); // 默认400tick=20秒

        double fieldRadius = toDouble(cfg.getParameter("ender_field_radius", 10.0)); // 默认10格半径
        int fieldDuration = toInt(cfg.getParameter("ender_field_duration", 200)); // 默认200tick=10秒

        logger.info(String.format("挂载IDC22技能[末影粒子减速场]: interval=%d tick, radius=%.2f, duration=%d tick (从%d个参数中读取)",
                   attackInterval, fieldRadius, fieldDuration, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, 50.0);
            if (target != null) {
                performMutationKingEnderField(caster, target, fieldRadius, fieldDuration);
            }
        }, attackInterval, attackInterval); // 直接使用tick
        return task.getTaskId();
    }

    /**
     * IDC22召唤变异生物处理器 - 复制原版performMutationKingSummon逻辑
     */
    public Integer handleSummonMutants(LivingEntity caster, SkillConfig cfg) {
        // 详细的参数读取调试
        Map<String, Object> allParams = cfg.getParameters();
        String skillName = cfg.getSkillName();
        logger.info("IDC22召唤变异生物技能参数读取 - " + skillName + ": " + allParams);

        // 读取技能参数（使用tick单位）
        int attackInterval = toInt(cfg.getParameter("attack_interval",
                            cfg.getParameter("summon_interval",
                            cfg.getParameter("interval", 80)))); // 默认80tick=4秒

        double summonRange = toDouble(cfg.getParameter("summon_range", 30.0)); // 默认30格范围

        logger.info(String.format("挂载IDC22技能[召唤变异生物]: interval=%d tick, range=%.2f (从%d个参数中读取)",
                   attackInterval, summonRange, allParams.size()));

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!isAlive(caster)) return;
            Player target = findNearestPlayer(caster, summonRange);
            if (target != null) {
                performMutationKingSummon(caster, target);
            }
        }, attackInterval, attackInterval); // 直接使用tick
        return task.getTaskId();
    }

    // =========== IDC22技能具体实现方法 ===========

    /**
     * 执行末影水晶攻击 - 修复版本（支持原版粒子攻击和烈焰弹模式）
     */
    private void performMutationKingCrystalAttack(LivingEntity caster, Player target,
                                                 double crystalDamage, double crystalHealAmount, int crystalOrbitalCount, SkillConfig cfg) {
        Location casterLoc = caster.getLocation();
        World world = casterLoc.getWorld();

        // 播放音效
        world.playSound(casterLoc, Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.0f, 1.0f);

        // 生成末影水晶并发射攻击
        for (int i = 0; i < crystalOrbitalCount; i++) {
            // 在caster周围生成末影水晶
            double angle = (2 * Math.PI * i) / crystalOrbitalCount;
            double x = casterLoc.getX() + 3.0 * Math.cos(angle);
            double z = casterLoc.getZ() + 3.0 * Math.sin(angle);
            Location crystalLoc = new Location(world, x, casterLoc.getY() + 2, z);

            // 生成末影水晶
            EnderCrystal crystal = world.spawn(crystalLoc, EnderCrystal.class);
            crystal.setShowingBottom(false);

            // 禁用末影水晶的爆炸特性
            crystal.setInvulnerable(true); // 设置为无敌，防止被攻击
            crystal.setMetadata("idc22_crystal", new FixedMetadataValue(plugin, true)); // 标记为IDC22水晶
            crystal.setMetadata("no_explosion", new FixedMetadataValue(plugin, true)); // 标记禁止爆炸

            // 读取攻击模式参数（从技能配置中读取）
            String attackModeStr = cfg.getParameter("crystal_attack_mode", "true").toString();
            boolean useParticleAttack = Boolean.parseBoolean(attackModeStr); // 默认使用原版粒子攻击

            if (useParticleAttack) {
                // 原版粒子攻击模式
                performCrystalParticleAttack(crystal, target, crystalDamage);
            } else {
                // 烈焰弹攻击模式（保留当前设计）
                Vector direction = target.getLocation().subtract(crystalLoc).toVector().normalize();
                Fireball fireball = world.spawn(crystalLoc, Fireball.class);
                fireball.setDirection(direction);
                fireball.setYield(0); // 不破坏方块
                fireball.setShooter(caster);
                fireball.setMetadata("custom_damage", new FixedMetadataValue(plugin, crystalDamage));
            }

            // 延迟移除水晶
            Bukkit.getScheduler().runTaskLater(plugin, crystal::remove, 100L);
        }

        // 治疗caster
        if (caster instanceof LivingEntity) {
            LivingEntity livingCaster = (LivingEntity) caster;
            double newHealth = Math.min(livingCaster.getMaxHealth(),
                                       livingCaster.getHealth() + crystalHealAmount);
            livingCaster.setHealth(newHealth);

            // 治疗粒子效果
            world.spawnParticle(Particle.HEART, casterLoc.add(0, 2, 0), 10, 1.0, 1.0, 1.0, 0.1);
        }

        logger.info(String.format("IDC22末影水晶攻击执行 - 目标: %s, 伤害: %.1f, 治疗: %.1f, 水晶数: %d",
                   target.getName(), crystalDamage, crystalHealAmount, crystalOrbitalCount));
    }

    /**
     * 执行水晶粒子攻击 - 原版模式
     */
    private void performCrystalParticleAttack(EnderCrystal crystal, Player target, double damage) {
        Location crystalLoc = crystal.getLocation();
        Location targetLoc = target.getLocation();
        World world = crystalLoc.getWorld();

        // 创建粒子束攻击
        Vector direction = targetLoc.subtract(crystalLoc).toVector().normalize();
        double distance = crystalLoc.distance(targetLoc);

        // 沿路径生成粒子
        for (double d = 0; d < distance; d += 0.5) {
            Location particleLoc = crystalLoc.clone().add(direction.clone().multiply(d));
            world.spawnParticle(Particle.END_ROD, particleLoc, 2, 0.1, 0.1, 0.1, 0.02);
            world.spawnParticle(Particle.DRAGON_BREATH, particleLoc, 1, 0.05, 0.05, 0.05, 0.01);
        }

        // 对目标造成伤害
        target.damage(damage, crystal);

        // 击中效果
        world.spawnParticle(Particle.FLAME, targetLoc, 10, 0.5, 0.5, 0.5, 0.1);
        world.playSound(targetLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
    }

    /**
     * 执行龙息攻击 - 简化实现
     */
    private void performMutationKingBreathAttack(LivingEntity caster, Player target, double breathDamage) {
        Location casterLoc = caster.getLocation();
        Location targetLoc = target.getLocation();
        World world = casterLoc.getWorld();

        // 播放龙息音效
        world.playSound(casterLoc, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 1.0f);

        // 创建龙息云
        AreaEffectCloud breathCloud = world.spawn(targetLoc, AreaEffectCloud.class);
        breathCloud.setRadius(5.0f);
        breathCloud.setDuration(100); // 5秒
        breathCloud.setRadiusOnUse(0);
        breathCloud.setRadiusPerTick(0.1f);
        breathCloud.setParticle(Particle.DRAGON_BREATH);

        // 添加伤害效果（使用瞬间伤害）
        breathCloud.addCustomEffect(new PotionEffect(PotionEffectType.INSTANT_DAMAGE, 1,
                                   (int) Math.max(0, breathDamage / 6 - 1)), false);
        breathCloud.addCustomEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1), false);

        // 设置来源
        breathCloud.setSource(caster);

        // 生成龙息粒子路径
        Vector direction = targetLoc.subtract(casterLoc).toVector().normalize();
        for (int i = 0; i < 20; i++) {
            Location particleLoc = casterLoc.clone().add(direction.clone().multiply(i));
            world.spawnParticle(Particle.DRAGON_BREATH, particleLoc, 5, 0.5, 0.5, 0.5, 0.02);
        }

        logger.info(String.format("IDC22龙息攻击执行 - 目标: %s, 伤害: %.1f", target.getName(), breathDamage));
    }

    /**
     * 执行黑曜石块攻击 - 修复版本（添加伤害、击退和自动清理）
     */
    private void performMutationKingShootObsidianBlocks(LivingEntity caster, Player target,
                                                        double blocksDamage, int blocksWaves, int blocksPerWave) {
        Location casterLoc = caster.getLocation();
        World world = casterLoc.getWorld();
        final List<Location> placedBlocks = new ArrayList<>();

        // 播放音效
        world.playSound(casterLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.5f);

        // 发射多波黑曜石方块
        new BukkitRunnable() {
            int currentWave = 0;

            @Override
            public void run() {
                if (currentWave >= blocksWaves || !isAlive(caster)) {
                    cancel();
                    return;
                }

                // 发射一波黑曜石方块
                for (int i = 0; i < blocksPerWave; i++) {
                    // 添加随机偏移
                    Location targetLoc = target.getLocation().add(
                        (Math.random() - 0.5) * 6,
                        Math.random() * 3,
                        (Math.random() - 0.5) * 6
                    );

                    // 创建下落方块
                    FallingBlock fallingBlock = world.spawnFallingBlock(
                        casterLoc.clone().add(0, 10, 0),
                        Material.OBSIDIAN.createBlockData()
                    );

                    // 设置速度指向目标
                    Vector velocity = targetLoc.subtract(fallingBlock.getLocation()).toVector().normalize().multiply(1.5);
                    fallingBlock.setVelocity(velocity);
                    fallingBlock.setDropItem(false);

                    // 设置自定义伤害和标记
                    fallingBlock.setMetadata("custom_damage", new FixedMetadataValue(plugin, blocksDamage));
                    fallingBlock.setMetadata("idc22_obsidian", new FixedMetadataValue(plugin, true));
                    fallingBlock.setMetadata("caster", new FixedMetadataValue(plugin, caster.getUniqueId().toString()));

                    // 实时监听方块状态，检测击中
                    startObsidianBlockTracking(fallingBlock, blocksDamage, placedBlocks, caster);
                }

                currentWave++;
            }
        }.runTaskTimer(plugin, 0L, 40L); // 每2秒一波

        // 延迟清理所有放置的方块
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            for (Location loc : placedBlocks) {
                if (loc.getBlock().getType() == Material.OBSIDIAN) {
                    loc.getBlock().setType(Material.AIR);
                    world.spawnParticle(Particle.SMOKE, loc, 10, 0.5, 0.5, 0.5, 0.1);
                }
            }
            placedBlocks.clear();
        }, 200L); // 10秒后清理

        logger.info(String.format("IDC22黑曜石块攻击执行 - 目标: %s, 伤害: %.1f, 波数: %d, 每波: %d",
                   target.getName(), blocksDamage, blocksWaves, blocksPerWave));
    }

    /**
     * 开始实时跟踪黑曜石方块，检测击中
     */
    private void startObsidianBlockTracking(FallingBlock fallingBlock, double damage, List<Location> placedBlocks, LivingEntity caster) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (fallingBlock.isDead() || !fallingBlock.isValid()) {
                    // 方块已落地或消失，检查最终状态
                    checkObsidianBlockLanding(fallingBlock, damage, placedBlocks);
                    cancel();
                    return;
                }

                // 检查方块是否击中玩家（实时检测）
                Location blockLoc = fallingBlock.getLocation();
                for (Entity entity : blockLoc.getWorld().getNearbyEntities(blockLoc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;
                        if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                            // 立即造成伤害和击退
                            player.damage(damage, caster);

                            // 强力击退效果
                            Vector knockback = player.getLocation().subtract(blockLoc).toVector().normalize();
                            knockback.setY(Math.max(0.8, knockback.getY())); // 确保向上击飞
                            knockback.multiply(2.0); // 强力击退
                            player.setVelocity(knockback);

                            // 击中效果
                            blockLoc.getWorld().spawnParticle(Particle.FLAME, blockLoc, 10, 0.5, 0.5, 0.5, 0.1);
                            blockLoc.getWorld().playSound(blockLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.2f);
                            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            logger.info(String.format("IDC22黑曜石块实时击中玩家: %s, 伤害: %.1f, 击退: 2.0x",
                                       player.getName(), damage));

                            // 移除方块（击中后消失）
                            fallingBlock.remove();
                            cancel();
                            return;
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 每tick检查一次
    }

    /**
     * 检查黑曜石方块落地并处理伤害
     */
    private void checkObsidianBlockLanding(FallingBlock fallingBlock, double damage, List<Location> placedBlocks) {
        if (fallingBlock.isDead()) {
            Location landLoc = fallingBlock.getLocation();
            World world = landLoc.getWorld();

            // 记录放置的方块位置
            if (landLoc.getBlock().getType() == Material.OBSIDIAN) {
                placedBlocks.add(landLoc.clone());
            }

            // 对附近玩家造成伤害和击退
            for (Entity entity : world.getNearbyEntities(landLoc, 3.0, 3.0, 3.0)) {
                if (entity instanceof Player) {
                    Player player = (Player) entity;
                    double distance = player.getLocation().distance(landLoc);

                    // 距离越近伤害越高
                    double actualDamage = damage * (3.0 - distance) / 3.0;
                    if (actualDamage > 0) {
                        player.damage(actualDamage);

                        // 击退效果
                        Vector knockback = player.getLocation().subtract(landLoc).toVector().normalize();
                        knockback.setY(0.5); // 向上击飞
                        knockback.multiply(1.5); // 击退强度
                        player.setVelocity(knockback);

                        logger.info(String.format("IDC22黑曜石块击中玩家: %s, 伤害: %.1f, 距离: %.1f",
                                   player.getName(), actualDamage, distance));
                    }
                }
            }

            // 爆炸效果
            world.spawnParticle(Particle.FLAME, landLoc, 5, 0.5, 0.5, 0.5, 0.1);
            world.playSound(landLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.8f);
        }
    }

    /**
     * 执行黑曜石柱攻击 - 修复版本（3x3粗柱子+连续击飞）
     */
    private void performMutationKingObsidianPillarAttack(LivingEntity caster, Player target,
                                                        double pillarDamage, int pillarCount) {
        Location targetLoc = target.getLocation();
        World world = targetLoc.getWorld();

        // 播放音效
        world.playSound(targetLoc, Sound.BLOCK_STONE_PLACE, 1.0f, 0.6f);
        world.playSound(targetLoc, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.8f);

        // 全局柱子方块列表，用于统一清理
        final List<Location> allPillarBlocks = new ArrayList<>();

        // 生成多个3x3粗的黑曜石柱
        for (int i = 0; i < pillarCount; i++) {
            // 随机位置
            Location pillarCenter = targetLoc.clone().add(
                (Math.random() - 0.5) * 16,
                0,
                (Math.random() - 0.5) * 16
            );

            // 找到地面
            while (pillarCenter.getY() > 0 && pillarCenter.getBlock().getType() == Material.AIR) {
                pillarCenter.add(0, -1, 0);
            }
            pillarCenter.add(0, 1, 0);

            // 为每个柱子创建独立的生长任务
            final int pillarIndex = i;
            final Location finalPillarCenter = pillarCenter.clone();

            // 逐层生长动画
            new BukkitRunnable() {
                int currentY = 0;

                @Override
                public void run() {
                    if (currentY >= 12 || !isAlive(caster)) {
                        cancel();
                        return;
                    }

                    // 生成当前层的3x3方块
                    for (int x = -1; x <= 1; x++) {
                        for (int z = -1; z <= 1; z++) {
                            Location blockLoc = finalPillarCenter.clone().add(x, currentY, z);
                            if (blockLoc.getBlock().getType() == Material.AIR ||
                                blockLoc.getBlock().getType() == Material.WATER ||
                                blockLoc.getBlock().getType() == Material.GRASS_BLOCK ||
                                blockLoc.getBlock().getType() == Material.DIRT) {
                                blockLoc.getBlock().setType(Material.CRYING_OBSIDIAN);

                                // 添加到全局清理列表
                                synchronized (allPillarBlocks) {
                                    allPillarBlocks.add(blockLoc.clone());
                                }

                                // 生长粒子效果
                                world.spawnParticle(Particle.LAVA, blockLoc, 3, 0.2, 0.2, 0.2, 0);
                                world.spawnParticle(Particle.SMOKE, blockLoc, 2, 0.1, 0.1, 0.1, 0.02);
                            }
                        }
                    }

                    // 生长音效
                    world.playSound(finalPillarCenter.clone().add(0, currentY, 0), Sound.BLOCK_STONE_PLACE, 0.8f, 0.6f + (currentY * 0.05f));

                    // 检查当前层的玩家碰撞
                    checkPillarPlayerCollision(finalPillarCenter.clone().add(0, currentY, 0), pillarDamage, caster);

                    currentY++;
                }
            }.runTaskTimer(plugin, pillarIndex * 10L, 3L); // 每个柱子错开0.5秒开始
        }

        // 统一安排所有柱子的清理任务
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            schedulePillarCleanup(allPillarBlocks, world);
        }, 200L + (pillarCount * 10L) + 36L); // 等待所有柱子生长完成后再清理

        logger.info(String.format("IDC22黑曜石柱攻击执行 - 目标: %s, 伤害: %.1f, 柱数: %d (3x3粗柱)",
                   target.getName(), pillarDamage, pillarCount));
    }

    /**
     * 安排柱子清理任务
     */
    private void schedulePillarCleanup(List<Location> pillarBlocks, World world) {
        // 10秒后清理柱子
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            int cleanedCount = 0;
            for (Location loc : pillarBlocks) {
                if (loc.getBlock().getType() == Material.CRYING_OBSIDIAN) {
                    loc.getBlock().setType(Material.AIR);
                    world.spawnParticle(Particle.LAVA, loc, 5, 0.2, 0.2, 0.2, 0);
                    cleanedCount++;
                }
            }
            logger.info(String.format("IDC22黑曜石柱清理完成，清理了 %d 个方块", cleanedCount));
        }, 200L); // 10秒后清理
    }

    /**
     * 检查柱子与玩家的碰撞并处理连续击飞
     */
    private void checkPillarPlayerCollision(Location pillarLoc, double damage, LivingEntity caster) {
        World world = pillarLoc.getWorld();

        // 检查3x3范围内的玩家
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                Location checkLoc = pillarLoc.clone().add(x, 0, z);
                for (Entity entity : world.getNearbyEntities(checkLoc, 1.0, 2.0, 1.0)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 造成伤害
                        player.damage(damage, caster);

                        // 强力向上击飞（连续击飞效果）
                        Vector knockback = new Vector(0, 2.0, 0); // 强力向上
                        // 添加随机水平方向
                        knockback.add(new Vector(
                            (Math.random() - 0.5) * 0.5,
                            0,
                            (Math.random() - 0.5) * 0.5
                        ));
                        player.setVelocity(knockback);

                        // 击中效果
                        world.spawnParticle(Particle.FLAME, player.getLocation(), 5, 0.3, 0.3, 0.3, 0.1);
                        world.playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.2f);
                        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        logger.info(String.format("IDC22黑曜石柱击中玩家: %s, 伤害: %.1f, 击飞强度: 2.0",
                                   player.getName(), damage));
                    }
                }
            }
        }
    }

    /**
     * 执行末影粒子减速场 - 简化实现
     */
    private void performMutationKingEnderField(LivingEntity caster, Player target,
                                              double fieldRadius, int fieldDuration) {
        Location centerLoc = target.getLocation();
        World world = centerLoc.getWorld();

        // 播放音效
        world.playSound(centerLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);

        // 创建末影粒子减速场
        new BukkitRunnable() {
            int ticks = 0;

            @Override
            public void run() {
                if (ticks >= fieldDuration) {
                    cancel();
                    return;
                }

                // 生成末影粒子圆圈
                for (int angle = 0; angle < 360; angle += 10) {
                    double radians = Math.toRadians(angle);
                    double x = centerLoc.getX() + fieldRadius * Math.cos(radians);
                    double z = centerLoc.getZ() + fieldRadius * Math.sin(radians);
                    Location particleLoc = new Location(world, x, centerLoc.getY() + 0.5, z);
                    world.spawnParticle(Particle.PORTAL, particleLoc, 2, 0.1, 0.1, 0.1, 0.02);
                }

                // 对范围内玩家施加减速
                for (Entity entity : world.getNearbyEntities(centerLoc, fieldRadius, fieldRadius, fieldRadius)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 2));
                        player.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 40, 1));
                    }
                }

                ticks += 10;
            }
        }.runTaskTimer(plugin, 0L, 10L); // 每0.5秒更新一次

        logger.info(String.format("IDC22末影粒子减速场执行 - 目标: %s, 半径: %.1f, 持续: %dtick",
                   target.getName(), fieldRadius, fieldDuration));
    }

    /**
     * 执行召唤变异生物 - 修复版本（真正召唤IDC1-IDC21）
     */
    private void performMutationKingSummon(LivingEntity caster, Player target) {
        Location casterLoc = caster.getLocation();
        World world = casterLoc.getWorld();

        // 播放音效
        world.playSound(casterLoc, Sound.ENTITY_EVOKER_CAST_SPELL, 1.0f, 1.0f);
        world.playSound(casterLoc, Sound.ENTITY_WITHER_SPAWN, 0.5f, 1.5f);

        // 召唤2-3个IDC系列变异生物
        int summonCount = 2 + (int) (Math.random() * 2);
        for (int i = 0; i < summonCount; i++) {
            // 随机位置
            Location summonLoc = casterLoc.clone().add(
                (Math.random() - 0.5) * 10,
                0,
                (Math.random() - 0.5) * 10
            );

            // 找到合适的地面
            while (summonLoc.getY() > 0 && summonLoc.getBlock().getType() == Material.AIR) {
                summonLoc.add(0, -1, 0);
            }
            summonLoc.add(0, 1, 0);

            // 随机选择IDC1-IDC21中的一个
            String[] idcTypes = {
                "IDC1", "IDC2", "IDC3", "IDC4", "IDC5", "IDC6", "IDC7", "IDC8", "IDC9", "IDC10",
                "IDC11", "IDC12", "IDC13", "IDC14", "IDC15", "IDC16", "IDC17", "IDC18", "IDC19", "IDC20", "IDC21"
            };
            String randomIDC = idcTypes[(int) (Math.random() * idcTypes.length)];

            // 召唤对应的IDC生物
            logger.info(String.format("尝试召唤 %s 在位置 (%.1f, %.1f, %.1f)",
                       randomIDC, summonLoc.getX(), summonLoc.getY(), summonLoc.getZ()));

            LivingEntity summonedEntity = summonIDCEntity(world, summonLoc, randomIDC);

            if (summonedEntity != null) {
                // 设置召唤生物的属性
                summonedEntity.setCustomName("§4[召唤]§c" + randomIDC);
                summonedEntity.setCustomNameVisible(true);

                // 增强属性（召唤的比原版弱一些）
                double originalMaxHealth = summonedEntity.getMaxHealth();
                summonedEntity.setMaxHealth(originalMaxHealth * 0.8);
                summonedEntity.setHealth(summonedEntity.getMaxHealth());

                // 添加召唤效果
                summonedEntity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 0));
                summonedEntity.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, 200, 0)); // 发光10秒

                // 设置目标为玩家
                if (summonedEntity instanceof Mob) {
                    ((Mob) summonedEntity).setTarget(target);
                }

                // 生成召唤粒子效果
                world.spawnParticle(Particle.PORTAL, summonLoc, 30, 1.0, 2.0, 1.0, 0.1);
                world.spawnParticle(Particle.WITCH, summonLoc, 20, 0.5, 1.0, 0.5, 0.05);
                world.spawnParticle(Particle.FLAME, summonLoc, 10, 0.5, 0.5, 0.5, 0.1);

                // 设置召唤生物的生存时间（60秒后自动消失）
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (summonedEntity.isValid() && !summonedEntity.isDead()) {
                        world.spawnParticle(Particle.PORTAL, summonedEntity.getLocation(), 20, 1.0, 1.0, 1.0, 0.1);
                        summonedEntity.remove();
                        logger.info(String.format("召唤生物 %s 生存时间到期，已移除", randomIDC));
                    }
                }, 1200L); // 60秒

                logger.info(String.format("IDC22成功召唤变异生物: %s (%s) 生命值: %.1f/%.1f",
                           randomIDC, summonedEntity.getType().name(),
                           summonedEntity.getHealth(), summonedEntity.getMaxHealth()));
            } else {
                logger.warning(String.format("IDC22召唤变异生物失败: %s", randomIDC));
            }
        }

        logger.info(String.format("IDC22召唤变异生物执行 - 目标: %s, 召唤数量: %d", target.getName(), summonCount));
    }

    /**
     * 召唤指定类型的IDC实体 - 真正调用UserCustomEntity系统
     */
    private LivingEntity summonIDCEntity(World world, Location location, String idcType) {
        try {
            logger.info(String.format("开始召唤 %s，位置: (%.1f, %.1f, %.1f)",
                       idcType, location.getX(), location.getY(), location.getZ()));

            // 记录召唤前的实体数量，用于验证
            int entitiesBeforeSpawn = location.getWorld().getNearbyEntities(location, 3.0, 3.0, 3.0).size();

            // 尝试通过UserCustomEntity系统创建真正的IDC实体
            LivingEntity entity = createRealIDCEntity(location, idcType);

            if (entity != null && isValidIDCEntity(entity, idcType)) {
                // 成功创建真正的IDC实体
                logger.info(String.format("✅ 成功通过UserCustomEntity系统召唤 %s: %s (生命值: %.1f/%.1f)",
                           idcType, entity.getType().name(), entity.getHealth(), entity.getMaxHealth()));
                return entity;
            } else {
                // UserCustomEntity系统不可用或该IDC类型未适配
                if (entity != null) {
                    logger.warning(String.format("❌ UserCustomEntity创建的实体验证失败: %s，移除并使用简化实现", idcType));
                    entity.remove(); // 移除无效实体
                } else {
                    logger.info(String.format("⚠️ UserCustomEntity系统不可用，使用简化实现召唤 %s", idcType));
                }

                // 清理可能的错误实体
                cleanupInvalidEntities(location, entitiesBeforeSpawn);

                return createSimplifiedIDCEntity(world, location, idcType);
            }
        } catch (Exception e) {
            logger.warning("召唤IDC实体失败: " + idcType + " - " + e.getMessage());
            e.printStackTrace();

            // 清理可能的错误实体
            cleanupInvalidEntities(location, 0);

            // 失败时召唤基础僵尸
            LivingEntity zombie = (LivingEntity) world.spawnEntity(location, EntityType.ZOMBIE);
            zombie.setCustomName("§4[召唤失败]§c" + idcType);
            zombie.setCustomNameVisible(true);
            return zombie;
        }
    }

    /**
     * 通过UserCustomEntity系统创建真正的IDC实体
     */
    private LivingEntity createRealIDCEntity(Location location, String idcType) {
        try {
            // 方法1：尝试通过ZombieHelper的spawnOtherEntityAtLocation方法
            LivingEntity entity = trySpawnViaZombieHelper(location, idcType);
            if (entity != null) {
                logger.info(String.format("成功通过ZombieHelper创建 %s: %s",
                           idcType, entity.getType().name()));
                return entity;
            }

            // 方法2：尝试通过UserCustomEntityIntegration
            entity = trySpawnViaUserCustomEntityIntegration(location, idcType);
            if (entity != null) {
                logger.info(String.format("成功通过UserCustomEntityIntegration创建 %s: %s",
                           idcType, entity.getType().name()));
                return entity;
            }

            // 方法3：尝试直接通过UserCustomEntity
            entity = trySpawnViaUserCustomEntity(location, idcType);
            if (entity != null) {
                logger.info(String.format("成功通过UserCustomEntity创建 %s: %s",
                           idcType, entity.getType().name()));
                return entity;
            }

            logger.info(String.format("所有UserCustomEntity方法都失败，%s将使用简化实现", idcType));
            return null;
        } catch (Exception e) {
            logger.info("无法通过UserCustomEntity系统创建 " + idcType + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证IDC实体是否正确
     */
    private boolean isValidIDCEntity(LivingEntity entity, String expectedIdcType) {
        if (entity == null) return false;

        try {
            // 检查实体是否有正确的元数据
            if (entity.hasMetadata("entityId")) {
                String entityId = entity.getMetadata("entityId").get(0).asString();
                boolean isCorrect = expectedIdcType.toLowerCase().equals(entityId.toLowerCase());

                if (isCorrect) {
                    logger.info(String.format("✅ 实体验证成功: %s = %s", expectedIdcType, entityId));
                } else {
                    logger.warning(String.format("❌ 实体ID不匹配: 期望 %s，实际 %s", expectedIdcType, entityId));
                }

                return isCorrect;
            }

            // 检查实体是否有UserCustomEntity标记
            if (entity.hasMetadata("userCustomEntity")) {
                logger.info(String.format("✅ 发现UserCustomEntity标记的实体: %s", expectedIdcType));
                return true;
            }

            // 检查实体是否有IDC标记
            if (entity.hasMetadata("idcZombieEntity")) {
                logger.info(String.format("✅ 发现IDC标记的实体: %s", expectedIdcType));
                return true;
            }

            logger.warning(String.format("❌ 实体缺少必要的元数据标记: %s", expectedIdcType));
            return false;

        } catch (Exception e) {
            logger.warning(String.format("❌ 验证实体时出错: %s - %s", expectedIdcType, e.getMessage()));
            return false;
        }
    }

    /**
     * 清理无效的实体
     */
    private void cleanupInvalidEntities(Location location, int entitiesBeforeSpawn) {
        try {
            // 查找召唤后新出现的实体
            for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 3.0, 3.0, 3.0)) {
                if (entity instanceof LivingEntity) {
                    LivingEntity livingEntity = (LivingEntity) entity;

                    // 检查是否是刚生成的实体（没有召唤标记）
                    if (!livingEntity.hasMetadata("summoned_by_idc22") &&
                        !livingEntity.hasMetadata("userCustomEntity") &&
                        !livingEntity.hasMetadata("idcZombieEntity") &&
                        !livingEntity.hasMetadata("entityId")) {

                        // 这可能是错误生成的实体，移除它
                        logger.info(String.format("🧹 清理无效实体: %s 在位置 (%.1f, %.1f, %.1f)",
                                   livingEntity.getType().name(),
                                   livingEntity.getLocation().getX(),
                                   livingEntity.getLocation().getY(),
                                   livingEntity.getLocation().getZ()));
                        livingEntity.remove();
                    }
                }
            }
        } catch (Exception e) {
            logger.warning("清理无效实体时出错: " + e.getMessage());
        }
    }

    /**
     * 尝试通过ZombieHelper创建IDC实体
     */
    private LivingEntity trySpawnViaZombieHelper(Location location, String idcType) {
        try {
            // 获取DeathZombieV4插件实例
            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
            org.Ver_zhzh.deathZombieV4.utils.ZombieHelper helper = dz.getZombieHelper();

            if (helper != null) {
                logger.info(String.format("🔄 尝试通过ZombieHelper召唤: %s", idcType));

                // 记录召唤前的实体，用于精确查找新实体
                java.util.Set<org.bukkit.entity.Entity> entitiesBeforeSpawn = new java.util.HashSet<>(
                    location.getWorld().getNearbyEntities(location, 3.0, 3.0, 3.0)
                );

                boolean success = helper.spawnOtherEntityAtLocation(location, idcType.toLowerCase());
                if (success) {
                    logger.info(String.format("✅ ZombieHelper报告召唤成功: %s", idcType));

                    // 等待一小段时间让实体完全生成
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    // 查找新生成的实体
                    for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 3.0, 3.0, 3.0)) {
                        if (!entitiesBeforeSpawn.contains(entity) && entity instanceof LivingEntity) {
                            LivingEntity livingEntity = (LivingEntity) entity;

                            // 验证实体是否正确
                            if (isValidIDCEntity(livingEntity, idcType)) {
                                // 设置召唤标记
                                livingEntity.setMetadata("summoned_by_idc22", new FixedMetadataValue(plugin, true));
                                livingEntity.setMetadata("summon_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));

                                logger.info(String.format("✅ ZombieHelper成功找到正确的IDC实体: %s (%s)",
                                           idcType, livingEntity.getType().name()));
                                return livingEntity;
                            } else {
                                logger.warning(String.format("❌ ZombieHelper生成了错误的实体，移除: %s",
                                              livingEntity.getType().name()));
                                livingEntity.remove();
                            }
                        }
                    }

                    logger.warning(String.format("⚠️ ZombieHelper报告成功但未找到正确实体: %s", idcType));
                } else {
                    logger.info(String.format("❌ ZombieHelper报告召唤失败: %s", idcType));
                }
            } else {
                logger.warning("❌ ZombieHelper实例为null");
            }
            return null;
        } catch (Exception e) {
            logger.info("ZombieHelper方法失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试通过UserCustomEntityIntegration创建IDC实体
     */
    private LivingEntity trySpawnViaUserCustomEntityIntegration(Location location, String idcType) {
        try {
            // 获取DeathZombieV4插件实例
            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dz = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;

            // 获取CustomZombie实例
            java.lang.reflect.Method getCustomZombieMethod = dz.getClass().getMethod("getCustomZombie");
            Object customZombie = getCustomZombieMethod.invoke(dz);

            if (customZombie != null) {
                // 获取EntityIntegration
                java.lang.reflect.Method getEntityIntegrationMethod = customZombie.getClass().getMethod("getEntityIntegration");
                Object entityIntegration = getEntityIntegrationMethod.invoke(customZombie);

                if (entityIntegration != null) {
                    // 调用spawnCustomEntity方法
                    java.lang.reflect.Method spawnMethod = entityIntegration.getClass()
                        .getMethod("spawnCustomEntity", Location.class, String.class);

                    LivingEntity entity = (LivingEntity) spawnMethod.invoke(entityIntegration, location, idcType.toLowerCase());

                    if (entity != null) {
                        // 设置召唤标记
                        entity.setMetadata("summoned_by_idc22", new FixedMetadataValue(plugin, true));
                        entity.setMetadata("summon_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));
                        return entity;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            logger.info("UserCustomEntityIntegration方法失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试直接通过UserCustomEntity创建IDC实体
     */
    private LivingEntity trySpawnViaUserCustomEntity(Location location, String idcType) {
        try {
            Object userCustomEntityInstance = getUserCustomEntityInstance();

            if (userCustomEntityInstance != null) {
                // 调用spawnUserCustomEntityDirect方法
                java.lang.reflect.Method spawnMethod = userCustomEntityInstance.getClass()
                    .getMethod("spawnUserCustomEntityDirect", Location.class, String.class);

                LivingEntity entity = (LivingEntity) spawnMethod.invoke(userCustomEntityInstance, location, idcType.toLowerCase());

                if (entity != null) {
                    // 设置召唤标记
                    entity.setMetadata("summoned_by_idc22", new FixedMetadataValue(plugin, true));
                    entity.setMetadata("summon_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));
                    return entity;
                }
            }
            return null;
        } catch (Exception e) {
            logger.info("直接UserCustomEntity方法失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取UserCustomEntity实例
     */
    private Object getUserCustomEntityInstance() {
        try {
            // 尝试通过插件的字段获取UserCustomEntity实例
            java.lang.reflect.Field[] fields = plugin.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                if (field.getType().getSimpleName().contains("UserCustomEntity")) {
                    field.setAccessible(true);
                    Object instance = field.get(plugin);
                    if (instance != null) {
                        return instance;
                    }
                }
            }

            // 尝试通过方法获取
            java.lang.reflect.Method[] methods = plugin.getClass().getDeclaredMethods();
            for (java.lang.reflect.Method method : methods) {
                if (method.getName().contains("getUserCustomEntity") ||
                    method.getName().contains("getEntityIntegration")) {
                    method.setAccessible(true);
                    Object instance = method.invoke(plugin);
                    if (instance != null) {
                        return instance;
                    }
                }
            }

            return null;
        } catch (Exception e) {
            logger.info("无法获取UserCustomEntity实例: " + e.getMessage());
            return null;
        }
    }

    /**
     * 创建简化的IDC实体（当UserCustomEntity不可用时）
     */
    private LivingEntity createSimplifiedIDCEntity(World world, Location location, String idcType) {
        try {
            logger.info(String.format("🔧 创建简化版IDC实体: %s", idcType));

            // 根据IDC类型选择基础实体类型
            EntityType baseType = getBaseEntityTypeForIDC(idcType);
            LivingEntity entity = (LivingEntity) world.spawnEntity(location, baseType);

            // 应用IDC特定的属性和技能
            applyIDCProperties(entity, idcType);

            // 设置简化实体的标记
            entity.setMetadata("simplified_idc", new FixedMetadataValue(plugin, true));
            entity.setMetadata("entityId", new FixedMetadataValue(plugin, idcType.toLowerCase()));
            entity.setMetadata("summoned_by_idc22", new FixedMetadataValue(plugin, true));
            entity.setMetadata("summon_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));

            logger.info(String.format("✅ 成功创建简化版 %s: %s (生命值: %.1f/%.1f)",
                       idcType, entity.getType().name(), entity.getHealth(), entity.getMaxHealth()));
            return entity;
        } catch (Exception e) {
            logger.warning("创建简化IDC实体失败: " + idcType + " - " + e.getMessage());
            e.printStackTrace();

            // 最后的保底：创建基础僵尸
            LivingEntity zombie = (LivingEntity) world.spawnEntity(location, EntityType.ZOMBIE);
            zombie.setCustomName("§4[召唤失败]§c" + idcType);
            zombie.setCustomNameVisible(true);
            zombie.setMetadata("summoned_by_idc22", new FixedMetadataValue(plugin, true));
            zombie.setMetadata("summon_time", new FixedMetadataValue(plugin, System.currentTimeMillis()));

            logger.warning(String.format("⚠️ 使用保底僵尸替代: %s", idcType));
            return zombie;
        }
    }

    /**
     * 获取IDC类型对应的基础实体类型
     */
    private EntityType getBaseEntityTypeForIDC(String idcType) {
        switch (idcType) {
            case "IDC1": case "IDC2": case "IDC3": case "IDC4": case "IDC5":
                return EntityType.ZOMBIE;
            case "IDC6": case "IDC7": case "IDC8":
                return EntityType.SKELETON;
            case "IDC9": case "IDC10":
                return EntityType.SPIDER;
            case "IDC11": case "IDC12":
                return EntityType.CREEPER;
            case "IDC13": case "IDC14":
                return EntityType.ENDERMAN;
            case "IDC15": case "IDC16":
                return EntityType.BLAZE;
            case "IDC17": case "IDC18":
                return EntityType.WITCH;
            case "IDC19": case "IDC20":
                return EntityType.WITHER_SKELETON;
            case "IDC21":
                return EntityType.WITHER_SKELETON;
            default:
                return EntityType.ZOMBIE;
        }
    }

    /**
     * 应用IDC特定的属性
     */
    private void applyIDCProperties(LivingEntity entity, String idcType) {
        // 基础属性增强
        entity.setMaxHealth(entity.getMaxHealth() * 2.0);
        entity.setHealth(entity.getMaxHealth());

        // 根据IDC类型添加特定效果
        switch (idcType) {
            case "IDC1": case "IDC2": case "IDC3":
                entity.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, Integer.MAX_VALUE, 0));
                break;
            case "IDC6": case "IDC7": case "IDC8":
                entity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1));
                break;
            case "IDC15": case "IDC16":
                entity.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, Integer.MAX_VALUE, 0));
                break;
            case "IDC19": case "IDC20": case "IDC21":
                entity.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, Integer.MAX_VALUE, 0));
                break;
        }

        // 设置自定义名称颜色
        entity.setCustomName("§4[召唤]§c" + idcType);
        entity.setCustomNameVisible(true);
    }

    // =========== 效果应用辅助方法 ===========

    public void applyAuraEffect(Player player, String skillName) {
        if (skillName.contains("poison")) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.POISON, 60, 1));
        } else if (skillName.contains("wither")) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1));
        } else if (skillName.contains("frost") || skillName.contains("freeze")) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2));
        }
        // 其他光环效果可以继续添加
    }

    /**
     * 应用光环效果（支持参数配置）- IDC21凋零光环专用
     */
    public void applyAuraEffect(Player player, String skillName, SkillConfig cfg) {
        if (skillName.contains("poison")) {
            int poisonLevel = Math.max(0, toInt(cfg.getParameter("poison_level", 1)) - 1);
            int poisonDuration = Math.max(20, toInt(cfg.getParameter("poison_duration", 60)));
            player.addPotionEffect(new PotionEffect(PotionEffectType.POISON, poisonDuration, poisonLevel));
        } else if (skillName.contains("wither")) {
            // IDC21凋零光环：复制原版实现逻辑，支持参数配置
            int witherLevel = Math.max(0, toInt(cfg.getParameter("wither_level",
                              cfg.getParameter("level", 2))) - 1); // 默认2级凋零
            int witherDuration = Math.max(20, toInt(cfg.getParameter("wither_duration",
                                cfg.getParameter("duration", 60)))); // 默认3秒
            player.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, witherDuration, witherLevel));

            // 添加凋零光环的视觉效果（黑色粒子）- 复制原版效果
            Location playerLoc = player.getLocation().add(0, 1, 0);
            player.getWorld().spawnParticle(Particle.LARGE_SMOKE, playerLoc, 5, 0.5, 0.5, 0.5, 0.02);
            player.getWorld().spawnParticle(Particle.WITCH, playerLoc, 3, 0.3, 0.3, 0.3, 0.1);

            logger.info(String.format("IDC21凋零光环效果应用 - 玩家: %s, 等级: %d, 持续时间: %d",
                       player.getName(), witherLevel + 1, witherDuration));
        } else if (skillName.contains("frost") || skillName.contains("freeze")) {
            int slownessLevel = Math.max(0, toInt(cfg.getParameter("slowness_level", 2)) - 1);
            int slownessDuration = Math.max(20, toInt(cfg.getParameter("slowness_duration", 60)));
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, slownessLevel));
        }
    }

    private PotionEffectType getBuffEffectType(String skillName) {
        if (skillName.contains("strength")) return PotionEffectType.STRENGTH;
        if (skillName.contains("defense")) return PotionEffectType.RESISTANCE;
        if (skillName.contains("speed")) return PotionEffectType.SPEED;
        if (skillName.contains("regeneration")) return PotionEffectType.REGENERATION;
        return null; // 非匹配技能不默认赋予力量，避免误加BUFF
    }

    private PotionEffectType getAreaEffectType(String skillName) {
        if (skillName.contains("buff")) return PotionEffectType.STRENGTH;
        if (skillName.contains("debuff")) return PotionEffectType.WEAKNESS;
        if (skillName.contains("protective")) return PotionEffectType.RESISTANCE;
        return PotionEffectType.WEAKNESS; // 默认虚弱
    }

    // 工具：从施法者挂载的技能配置里读取首个匹配参数
    private Object getFirstParamFromCaster(LivingEntity caster, String key, Object defVal) {
        try {
            if (caster.hasMetadata("idz_skills")) {
                for (org.bukkit.metadata.MetadataValue meta : caster.getMetadata("idz_skills")) {
                    Object v = meta.value();
                    if (v instanceof java.util.List) {
                        @SuppressWarnings("unchecked")
                        java.util.List<SkillConfig> list = (java.util.List<SkillConfig>) v;
                        for (SkillConfig cfg : list) {
                            Map<String, Object> ps = cfg.getParameters();
                            if (ps != null && ps.containsKey(key)) return ps.get(key);
                        }
                    }
                }
            }
        } catch (Exception ignored) {}
        return defVal;
    }

    // 工具：水平面上对向量做偏航旋转（度）
    private org.bukkit.util.Vector rotateYaw(org.bukkit.util.Vector vec, double degrees) {
        double rad = Math.toRadians(degrees);
        double cos = Math.cos(rad), sin = Math.sin(rad);
        double x = vec.getX(), z = vec.getZ();
        return new org.bukkit.util.Vector(x * cos - z * sin, vec.getY(), x * sin + z * cos);
    }

    public void launchProjectile(LivingEntity caster, Player target, String skillName) {
        Location start = caster.getEyeLocation();
        Location end = target.getEyeLocation();
        org.bukkit.util.Vector direction = end.toVector().subtract(start.toVector()).normalize();

        double speed = 1.6; // 默认速度
        double damage = 4.0; // 默认伤害
        // 毒箭额外参数（可选）
        int poisonLevel = 1;
        int poisonDuration = 100;
        boolean poisonArrowSkill = skillName != null && skillName.toLowerCase().contains("poison_arrow");
        try {
            // 尝试从caster上挂载的技能配置中读取（通过元数据idz_skills传入的SkillConfig列表）
            if (caster.hasMetadata("idz_skills")) {
                for (org.bukkit.metadata.MetadataValue meta : caster.getMetadata("idz_skills")) {
                    Object v = meta.value();
                    if (v instanceof java.util.List) {
                        @SuppressWarnings("unchecked")
                        java.util.List<SkillConfig> list = (java.util.List<SkillConfig>) v;
                        for (SkillConfig cfg : list) {
                            String name = cfg.getSkillName().toLowerCase();
                            if (name.contains("arrow") || name.contains("flame") || name.contains("snowball") || name.contains("skull") || name.contains("sonic") || name.contains("particle") || name.contains("ranged_magic_attack")) {
                                Object spd = cfg.getParameter("arrow_speed", cfg.getParameter("projectile_speed", 1.6));
                                Object dmg = cfg.getParameter("arrow_damage", cfg.getParameter("projectile_damage", 4.0));
                                speed = toDouble(spd);

                                damage = toDouble(dmg);
                            }
                            // 读取毒箭的药水参数
                            if (poisonArrowSkill || name.contains("poison_arrow")) {
                                poisonLevel = Math.max(0, toInt(cfg.getParameter("poison_level", 1)));
                                poisonDuration = Math.max(1, toInt(cfg.getParameter("poison_duration", 100)));
                                poisonArrowSkill = true; // 一旦找到配置，认为是毒箭技能
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {}

        if (skillName.contains("particle_ball") || skillName.contains("red_particle_ball")) {
            // 真实“粒子球”弹道：读取参数并以粒子可视化+碰撞判定推进
            double bSpeed = Math.max(0.1, toDouble(getFirstParamFromCaster(caster, "ball_speed", 1.5)));
            double bDamage = Math.max(0.0, toDouble(getFirstParamFromCaster(caster, "ball_damage", 6.0)));
            double bRange = Math.max(1.0, toDouble(getFirstParamFromCaster(caster, "ball_range", 15.0)));
            String pName = String.valueOf(getFirstParamFromCaster(caster, "ball_particle", skillName.contains("red_particle_ball") ? "REDSTONE" : "FIREWORKS_SPARK"));

            // 兼容老版本命名：REDSTONE -> DUST
            org.bukkit.Particle mainParticle = org.bukkit.Particle.DUST;
            org.bukkit.Particle trailParticle = null;
            String upper = pName == null ? "" : pName.toUpperCase();
            if (!upper.isEmpty()) {
                if ("REDSTONE".equals(upper)) {
                    mainParticle = org.bukkit.Particle.DUST;
                } else {
                    try { mainParticle = org.bukkit.Particle.valueOf(upper); } catch (Exception ignored) {}
                }
            }

            final org.bukkit.util.Vector vel = direction.clone().multiply(bSpeed);
            final org.bukkit.Particle finalMainParticle = mainParticle;
            final org.bukkit.Particle finalTrailParticle = trailParticle;
            final org.bukkit.util.Vector finalVel = vel;
            new org.bukkit.scheduler.BukkitRunnable() {
                double traveled = 0.0;
                Location pos = start.clone();
                @Override
                public void run() {
                    if (caster == null || caster.isDead() || !caster.isValid()) { cancel(); return; }
                    pos.add(finalVel);
                    traveled += finalVel.length();

                    // 显示粒子（若为DUST，则使用红色DustOptions；否则按默认）
                    try {
                        if (finalMainParticle == org.bukkit.Particle.DUST) {
                            caster.getWorld().spawnParticle(org.bukkit.Particle.DUST, pos, 3, 0.05, 0.05, 0.05, 0,
                                    new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(255,0,0), 1.2f));
                        } else {
                            caster.getWorld().spawnParticle(finalMainParticle, pos, 6, 0.05, 0.05, 0.05, 0.02);
                        }
                        if (finalTrailParticle != null) caster.getWorld().spawnParticle(finalTrailParticle, pos, 4, 0.02, 0.02, 0.02, 0.01);
                    } catch (Throwable ignored) {}

                    // 命中玩家判定
                    for (Entity e : caster.getWorld().getNearbyEntities(pos, 0.6, 0.6, 0.6)) {
                        if (e instanceof Player) {
                            Player p = (Player) e;
                            if (p.isDead() || !p.isOnline()) continue;
                            p.damage(bDamage, caster);
                            caster.getWorld().playSound(pos, org.bukkit.Sound.ENTITY_FIREWORK_ROCKET_BLAST, 0.8f, 1.2f);
                            caster.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, pos, 1, 0, 0, 0, 0);
                            cancel();
                            return;
                        }
                    }
                    // 撞方块或超出范围
                    if (!pos.getBlock().isPassable() || traveled >= bRange) {
                        caster.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, pos, 3, 0.15, 0.15, 0.15, 0.02);
                        cancel();
                    }
                }
            }.runTaskTimer(plugin, 0L, 1L);
        } else if (skillName.contains("ranged_magic_attack")) {
            // 远程魔法攻击：读取粒子/速度/尾迹等参数
            double mSpeed = toDouble(getFirstParamFromCaster(caster, "magic_speed", 1.2));
            double mDamage = toDouble(getFirstParamFromCaster(caster, "magic_damage", damage));
            String particleName = String.valueOf(getFirstParamFromCaster(caster, "magic_particle", "ENCHANTMENT_TABLE"));

            String trailName = String.valueOf(getFirstParamFromCaster(caster, "magic_trail", "CRIT"));
            int count = Math.max(1, toInt(getFirstParamFromCaster(caster, "magic_count", 1)));
            double spread = Math.max(0.0, toDouble(getFirstParamFromCaster(caster, "magic_spread", 8.0)));

            org.bukkit.Particle main = org.bukkit.Particle.valueOf(particleName.toUpperCase());
            org.bukkit.Particle trail = null;
            try { trail = org.bukkit.Particle.valueOf(trailName.toUpperCase()); } catch (Exception ignored) {}

            for (int i = 0; i < count; i++) {
                double yaw = (Math.random() - 0.5) * spread;
                org.bukkit.util.Vector dir2 = rotateYaw(direction.clone(), yaw).normalize().multiply(Math.max(0.1, mSpeed));
                // 用雪球作为命中载体，纯视觉用粒子
                Snowball sb = caster.launchProjectile(Snowball.class, dir2);
                sb.setMetadata("idz_projectile_damage", new FixedMetadataValue(plugin, mDamage));
                // 生成粒子主弹与尾迹
                caster.getWorld().spawnParticle(main, start, 6, 0.05, 0.05, 0.05, 0.02);
                if (trail != null) caster.getWorld().spawnParticle(trail, start, 8, 0.1, 0.1, 0.1, 0.01);
            }
        } else if (skillName.contains("triple_arrow")) {
            // IDC13风格：真正的三连发冰冻箭
            double tSpeed = toDouble(getFirstParamFromCaster(caster, "arrow_speed", 1.6));
            double tDamage = toDouble(getFirstParamFromCaster(caster, "arrow_damage", 4.0));
            double spreadDeg = Math.max(0.0, toDouble(getFirstParamFromCaster(caster, "arrow_spread", 15.0)));
            int freezeDuration = Math.max(1, toInt(getFirstParamFromCaster(caster, "freeze_duration", 100)));
            int slowLevel = Math.max(0, toInt(getFirstParamFromCaster(caster, "slowness_level", 2)));
            int miningLevel = Math.max(0, toInt(getFirstParamFromCaster(caster, "mining_level", 1)));

            double[] offsets = new double[]{-spreadDeg, 0.0, spreadDeg};
            for (double off : offsets) {
                org.bukkit.util.Vector dir2 = rotateYaw(direction.clone(), off).normalize().multiply(Math.max(0.1, tSpeed));
                Arrow arr = caster.launchProjectile(Arrow.class, dir2);
                arr.setDamage(tDamage);
                arr.setShooter(caster);
                // 标记为冰冻箭，由监听器施加减速/挖掘疲劳
                arr.setMetadata("idz_projectile_skill", new FixedMetadataValue(plugin, "ice_arrow"));
                arr.setMetadata("idz_freeze_duration", new FixedMetadataValue(plugin, freezeDuration));
                arr.setMetadata("idz_freeze_level", new FixedMetadataValue(plugin, slowLevel));
                arr.setMetadata("idz_mining_level", new FixedMetadataValue(plugin, miningLevel));
            }
        } else if (skillName.contains("arrow")) {
            Arrow arrow = caster.launchProjectile(Arrow.class, direction.multiply(speed));
            arrow.setDamage(damage);
            arrow.setShooter(caster);
            // 如果是毒箭，给箭矢打上元数据，命中时由监听器读取并施加药水
            if (poisonArrowSkill) {
                arrow.setMetadata("idz_projectile_skill", new FixedMetadataValue(plugin, "poison_arrow"));
                arrow.setMetadata("idz_poison_level", new FixedMetadataValue(plugin, poisonLevel));
                arrow.setMetadata("idz_poison_duration", new FixedMetadataValue(plugin, poisonDuration));
            }
        } else if (skillName.contains("snowball")) {
            Snowball snowball = caster.launchProjectile(Snowball.class, direction.multiply(speed));
            // 通过元数据传递伤害，由命中监听读取应用（如果有）
            snowball.setMetadata("idz_projectile_damage", new FixedMetadataValue(plugin, damage));
        } else if (skillName.contains("flame")) {
            // 改为和IDC变异烈焰人一致：发射多条烈焰粒子流并附带小火球
            double flameSpeed = toDouble(getFirstParamFromCaster(caster, "flame_speed", Math.max(0.5, speed)));
            int flameStreams = Math.max(1, toInt(getFirstParamFromCaster(caster, "flame_streams", 5)));
            double flameSpread = Math.max(0.0, toDouble(getFirstParamFromCaster(caster, "flame_spread", 0.0))); // 每条流的偏航范围
            int flameFireTicks = Math.max(0, toInt(getFirstParamFromCaster(caster, "flame_burn_ticks", 60)));
            double flameParticleDamage = toDouble(getFirstParamFromCaster(caster, "flame_particle_damage", Math.max(1.0, damage)));
            double flameRange = Math.max(1.0, toDouble(getFirstParamFromCaster(caster, "flame_range", 15.0)));
            boolean withSmallFireball = Boolean.parseBoolean(String.valueOf(getFirstParamFromCaster(caster, "flame_with_fireball", true)));
            double smallFireballSpeed = Math.max(0.1, toDouble(getFirstParamFromCaster(caster, "fireball_speed", 1.2)));
            double fireballDamage = Math.max(0.0, toDouble(getFirstParamFromCaster(caster, "fireball_damage", damage)));

            // 发射多条粒子“射线”，每条每tick前进并检测碰撞
            for (int i = 0; i < flameStreams; i++) {
                final org.bukkit.util.Vector baseDir = direction.clone();
                final double yawOffset = (flameSpread > 0.0) ? (Math.random() - 0.5) * flameSpread : 0.0;
                final org.bukkit.util.Vector dir = rotateYaw(baseDir, yawOffset).normalize().multiply(Math.max(0.1, flameSpeed));
                new org.bukkit.scheduler.BukkitRunnable() {
                    double traveled = 0.0;
                    org.bukkit.Location particleLoc = null;
                    @Override
                    public void run() {
                        if (caster == null || caster.isDead() || !caster.isValid()) { cancel(); return; }
                        if (traveled == 0.0) {
                            particleLoc = caster.getLocation().add(0, 1, 0).clone();
                        }
                        particleLoc.add(dir);
                        traveled += dir.length();
                        // 显示火焰粒子
                        caster.getWorld().spawnParticle(org.bukkit.Particle.FLAME, particleLoc, 5, 0.1, 0.1, 0.1, 0.02);
                        // 碰撞检测：命中玩家
                        for (Entity e : caster.getWorld().getNearbyEntities(particleLoc, 0.8, 0.8, 0.8)) {
                            if (e instanceof Player) {
                                Player p = (Player) e;
                                p.damage(flameParticleDamage, caster);
                                if (flameFireTicks > 0) p.setFireTicks(flameFireTicks);
                                // 命中效果与音效
                                caster.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 1, 0, 0, 0, 0);
                                caster.getWorld().playSound(particleLoc, org.bukkit.Sound.ENTITY_PLAYER_HURT_ON_FIRE, 1.0f, 1.0f);
                                cancel();
                                return;
                            }
                        }
                        // 命中方块或超出范围停止
                        if (traveled > flameRange || !particleLoc.getBlock().isPassable()) {
                            caster.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 3, 0.2, 0.2, 0.2, 0.02);
                            cancel();
                        }
                    }
                }.runTaskTimer(plugin, i * 5L, 1L);
            }
            // 附带发射一个小火球作为次要弹幕
            if (withSmallFireball && caster instanceof org.bukkit.entity.Blaze) {
                SmallFireball sfb = ((org.bukkit.entity.Blaze) caster).launchProjectile(SmallFireball.class);
                sfb.setVelocity(direction.clone().multiply(smallFireballSpeed));
                sfb.setMetadata("idz_projectile_damage", new FixedMetadataValue(plugin, fireballDamage));
            } else if (withSmallFireball) {
                SmallFireball sfb = caster.launchProjectile(SmallFireball.class, direction.clone().multiply(smallFireballSpeed));
                sfb.setMetadata("idz_projectile_damage", new FixedMetadataValue(plugin, fireballDamage));
            }
        } else {
            Arrow arrow = caster.launchProjectile(Arrow.class, direction.multiply(speed));
            arrow.setDamage(damage);
            arrow.setShooter(caster);
        }
    }

    public EntityType getSummonEntityType(String skillName) {
        if (skillName == null) return EntityType.ZOMBIE;
        String s = skillName.toLowerCase();
        // IDZ: 唤魔者召唤应生成 恼鬼(VEX)，而不是 卫道士
        if (s.contains("evoker") || s.contains("唤魔者")) return EntityType.VEX;
        if (s.contains("vex") || s.contains("恼鬼")) return EntityType.VEX;
        if (s.contains("vindicator") || s.contains("卫道士")) return EntityType.VINDICATOR;
        if (s.contains("warrior") || s.contains("战士")) return EntityType.SKELETON;
        if (s.contains("zombie") || s.contains("僵尸")) return EntityType.ZOMBIE;
        return EntityType.ZOMBIE; // 默认僵尸
    }

    private void applyTrapEffect(Player target, String skillName, SkillConfig cfg) {
        Location loc = target.getLocation();

        // 读取技能参数
        int blockCount = toInt(cfg.getParameter("block_count", 5));
        double explosionPower = toDouble(cfg.getParameter("explosion_power", 2.0));
        double damage = toDouble(cfg.getParameter("obsidian_damage", cfg.getParameter("damage", 5.0)));

        logger.info(String.format("执行陷阱效果[%s]: blockCount=%d, explosionPower=%.2f, damage=%.2f",
                   skillName, blockCount, explosionPower, damage));

        if (skillName.contains("web")) {
            // 蜘蛛网陷阱：读取持续时间与放置范围
            int webDuration = Math.max(1, toInt(cfg.getParameter("web_duration", 100)));
            double webRange = Math.max(0.0, toDouble(cfg.getParameter("web_range", 3.0)));

            // 在目标脚下与周围随机点生成蛛网
            java.util.List<Location> placed = new java.util.ArrayList<>();
            Location base = loc.clone();
            if (base.getBlock().getType() == Material.AIR) {
                base.getBlock().setType(Material.COBWEB);
                placed.add(base.clone());
            }
            int extra = Math.max(0, (int) Math.round(webRange));
            for (int i = 0; i < extra * 2 + 2; i++) {
                Location p = loc.clone().add((Math.random() - 0.5) * webRange * 2, 0, (Math.random() - 0.5) * webRange * 2);
                if (p.getBlock().getType() == Material.AIR) {
                    p.getBlock().setType(Material.COBWEB);
                    placed.add(p.clone());
                }
            }
            // 到期后统一清理
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                for (Location w : placed) {
                    if (w.getBlock().getType() == Material.COBWEB) {
                        w.getBlock().setType(Material.AIR);
                    }
                }
            }, webDuration);
        } else if (skillName.contains("pillar") || skillName.contains("obsidian")) {
            // 创建柱子，使用配置的方块数量
            for (int y = 0; y < blockCount; y++) {
                Location pillarLoc = loc.clone().add(0, y, 0);
                pillarLoc.getBlock().setType(Material.OBSIDIAN);
            }

            // 对玩家造成配置的伤害
            target.damage(damage);

            // 如果配置了爆炸威力，创建爆炸
            if (explosionPower > 0) {
                loc.getWorld().createExplosion(loc, (float)explosionPower, false, false);
            }

        } else if (skillName.contains("fence")) {
            // IDC21下界栅栏攻击 - 复制原版performWitherLordFenceAttack逻辑
            performFenceAttack(target, cfg);
        } else if (skillName.contains("knockup") || skillName.contains("slam")) {
            // 击飞效果
            target.setVelocity(target.getVelocity().add(new org.bukkit.util.Vector(0, 1.5, 0)));
            // 造成伤害
            target.damage(damage);
        }
    }

    /**
     * 执行下界栅栏攻击 - 完全复制原版UserCustomEntity.shootNetherFenceRays逻辑
     */
    private void performFenceAttack(Player target, SkillConfig cfg) {
        // 这个方法需要caster参数，从applyTrapEffect调用时获取
        // 暂时使用简化实现，真正的实现在handleTrapControl中
        logger.warning("performFenceAttack被调用，但需要caster参数。请使用handleTrapControl中的实现。");
    }

    /**
     * 执行下界栅栏攻击 - 完全复制原版逻辑（带caster参数）
     */
    private void performFenceAttackWithCaster(LivingEntity caster, Player target, SkillConfig cfg) {
        final Location casterLoc = caster.getLocation();
        final World world = casterLoc.getWorld();
        final Map<Location, Material> originalBlocks = new HashMap<>();
        final List<Location> currentAttackBlocks = new ArrayList<>();

        // 读取技能参数（复制原版参数名）
        double fenceDamage = toDouble(cfg.getParameter("fence_damage",
                            cfg.getParameter("damage", 5.0))); // 默认5点伤害

        int slownessLevel = toInt(cfg.getParameter("fence_slowness_level",
                           cfg.getParameter("slowness_level", 2))); // 默认减速3

        int slownessDuration = toInt(cfg.getParameter("fence_slowness_duration",
                              cfg.getParameter("slowness_duration", 60))); // 默认3秒

        int rayLength = toInt(cfg.getParameter("fence_ray_length",
                       cfg.getParameter("ray_length", 15))); // 默认15格

        int restoreDelay = toInt(cfg.getParameter("fence_restore_delay",
                          cfg.getParameter("restore_delay", 40))); // 默认2秒

        // 获取附近玩家列表（用于伤害检测）
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : caster.getNearbyEntities(30, 30, 30)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 播放攻击音效（复制原版）
        world.playSound(casterLoc, Sound.BLOCK_NETHER_BRICKS_BREAK, 1.0f, 0.6f);

        // 创建3条不同角度的下界栅栏射线（复制原版：-30度、0度、+30度）
        for (int angleOffset = -30; angleOffset <= 30; angleOffset += 30) {
            // 计算基础方向向量（从caster到target）
            Vector baseDirection = target.getLocation().subtract(casterLoc).toVector().normalize();

            // 应用角度偏移（复制原版逻辑）
            double angle = Math.toRadians(angleOffset);
            double x = baseDirection.getX() * Math.cos(angle) - baseDirection.getZ() * Math.sin(angle);
            double z = baseDirection.getX() * Math.sin(angle) + baseDirection.getZ() * Math.cos(angle);
            Vector direction = new Vector(x, baseDirection.getY(), z).normalize();

            // 创建一条下界栅栏射线
            for (int i = 1; i <= rayLength; i++) {
                Location blockLoc = casterLoc.clone().add(direction.clone().multiply(i));

                // 如果方块是空气、水或草方块，替换为下界栅栏（复制原版条件）
                if (blockLoc.getBlock().getType() == Material.AIR
                        || blockLoc.getBlock().getType() == Material.WATER
                        || blockLoc.getBlock().getType() == Material.GRASS_BLOCK) {
                    originalBlocks.put(blockLoc, blockLoc.getBlock().getType());
                    blockLoc.getBlock().setType(Material.NETHER_BRICK_FENCE);
                    currentAttackBlocks.add(blockLoc);

                    // 显示粒子效果（复制原版）
                    world.spawnParticle(Particle.FLAME, blockLoc, 5, 0.3, 0.3, 0.3, 0.05);

                    // 检查是否有玩家在这个位置（复制原版1.5格检测）
                    for (Player p : nearbyPlayers) {
                        if (p.getLocation().distance(blockLoc) < 1.5) {
                            // 造成伤害并减速（复制原版逻辑）
                            p.damage(fenceDamage, caster);
                            p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, slownessLevel));

                            logger.info(String.format("IDC21下界栅栏攻击命中玩家: %s, 伤害: %.1f, 减速%d级",
                                       p.getName(), fenceDamage, slownessLevel + 1));
                        }
                    }
                }
            }
        }

        // 播放音效（复制原版）
        world.playSound(casterLoc, Sound.BLOCK_WOOD_PLACE, 1.0f, 0.5f);

        // 安排任务在指定时间后恢复方块（复制原版逻辑）
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Location loc : currentAttackBlocks) {
                    if (originalBlocks.containsKey(loc)) {
                        loc.getBlock().setType(originalBlocks.get(loc));
                    }
                }
            }
        }.runTaskLater(plugin, restoreDelay);

        logger.info(String.format("IDC21下界栅栏攻击执行 - 目标: %s, 射线长度: %d, 恢复延迟: %dtick",
                   target.getName(), rayLength, restoreDelay));
    }

    private void applyMovementEffect(LivingEntity caster, Player target, String skillName, SkillConfig cfg) {
        String lower = skillName == null ? "" : skillName.toLowerCase();
        if (lower.contains("dash")) {
            // 冲向目标
            org.bukkit.util.Vector direction = target.getLocation().subtract(caster.getLocation()).toVector().normalize();
            double dashSpeed = Math.max(0.1, toDouble(cfg.getParameter("dash_speed", 2.0)));
            caster.setVelocity(direction.multiply(dashSpeed));
        } else if (lower.contains("teleport")) {
            // 瞬移到目标附近
            Location teleportLoc = target.getLocation().add(
                (Math.random() - 0.5) * 4,
                0,
                (Math.random() - 0.5) * 4
            );
            caster.teleport(teleportLoc);
        } else if (lower.contains("stealth")) {
            // 隐身攻击：读取参数并提供“瞬移+完美隐身+一次性攻击加成”
            int stealthDuration = Math.max(1, toInt(cfg.getParameter("stealth_duration", cfg.getParameter("duration", 100))));
            double maxTpRange = Math.max(1.0, toDouble(cfg.getParameter("teleport_range", cfg.getParameter("range", 12.0))));
            double attackBonus = Math.max(0.0, toDouble(cfg.getParameter("attack_bonus", 0.0)));

            // 若目标过远，则不瞬移（或只隐身）；若在范围内，则尝试瞬移到目标身后少许位置

            if (target != null && target.isOnline() && !target.isDead()) {
                Location tLoc = target.getLocation();
                Location cLoc = caster.getLocation();
                if (tLoc.getWorld() == cLoc.getWorld() && tLoc.distanceSquared(cLoc) <= maxTpRange * maxTpRange) {
                    org.bukkit.util.Vector back = target.getLocation().getDirection().normalize().multiply(-1.5);
                    Location behind = tLoc.clone().add(back.getX(), 0.0, back.getZ());
                    behind.setY(tLoc.getY());
                    try {
                        caster.teleport(behind);
                    } catch (Exception ignored) {}
                }
            }

            // 完美隐身：药水隐身 + 暂时移除护甲（支持重入，避免护甲“卡没”）
            try { caster.setInvisible(true); } catch (Throwable ignored) {}
            caster.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, stealthDuration, 1));

            // 记录一次性攻击加成（由事件监听在下一次命中时结算并清除）
            if (attackBonus > 0.0) {
                caster.setMetadata("idz_stealth_bonus", new FixedMetadataValue(plugin, attackBonus));
            }

            // 使用计数器支持重入，避免多次触发导致护甲丢失
            int counter = 0;
            try {
                if (caster.hasMetadata("idz_stealth_counter")) {
                    counter = Integer.parseInt(String.valueOf(caster.getMetadata("idz_stealth_counter").get(0).value()));
                }
            } catch (Exception ignored) {}
            boolean firstEnter = (counter <= 0);
            counter = Math.max(0, counter) + 1;
            caster.setMetadata("idz_stealth_counter", new FixedMetadataValue(plugin, counter));

            // 首次进入隐身时保存并移除护甲
            if (firstEnter) {
                try {
                    org.bukkit.inventory.EntityEquipment eq = caster.getEquipment();
                    if (eq != null) {
                        org.bukkit.inventory.ItemStack[] saved = new org.bukkit.inventory.ItemStack[]{
                                eq.getHelmet(), eq.getChestplate(), eq.getLeggings(), eq.getBoots()
                        };
                        caster.setMetadata("idz_stealth_saved_armor", new FixedMetadataValue(plugin, saved));
                        eq.setHelmet(null);
                        eq.setChestplate(null);
                        eq.setLeggings(null);
                        eq.setBoots(null);
                    }
                } catch (Exception ignored) {}
            }

            // 到期后：计数器-1；当计数归零时，恢复护甲并取消不可见
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                try {
                    if (caster.isDead() || !caster.isValid()) return;
                    int c = 0;
                    if (caster.hasMetadata("idz_stealth_counter")) {
                        try { c = Integer.parseInt(String.valueOf(caster.getMetadata("idz_stealth_counter").get(0).value())); } catch (Exception ignored) {}
                    }
                    c = Math.max(0, c - 1);
                    if (c <= 0) {
                        caster.removeMetadata("idz_stealth_counter", plugin);
                        // 恢复护甲
                        try {
                            org.bukkit.inventory.EntityEquipment eq2 = caster.getEquipment();
                            if (caster.hasMetadata("idz_stealth_saved_armor")) {
                                Object val = caster.getMetadata("idz_stealth_saved_armor").get(0).value();
                                if (val instanceof org.bukkit.inventory.ItemStack[]) {
                                    org.bukkit.inventory.ItemStack[] arr = (org.bukkit.inventory.ItemStack[]) val;
                                    if (eq2 != null) {
                                        if (eq2.getHelmet() == null) eq2.setHelmet(arr[0]);
                                        if (eq2.getChestplate() == null) eq2.setChestplate(arr[1]);
                                        if (eq2.getLeggings() == null) eq2.setLeggings(arr[2]);
                                        if (eq2.getBoots() == null) eq2.setBoots(arr[3]);
                                    }
                                }
                                caster.removeMetadata("idz_stealth_saved_armor", plugin);
                            }
                        } catch (Exception ignored) {}
                        try { caster.setInvisible(false); } catch (Throwable ignored) {}
                    } else {
                        caster.setMetadata("idz_stealth_counter", new FixedMetadataValue(plugin, c));
                    }
                } catch (Exception ignored) {}
            }, stealthDuration);
        } else if (lower.contains("flight")) {
            // 飞行效果：支持垂直和水平飞行；无目标或目标过远时也保持上升
            double vSpeed = Math.max(0.0, toDouble(cfg.getParameter("ascent_speed", cfg.getParameter("vertical_speed", 0.25))));
            double hSpeed = Math.max(0.0, toDouble(cfg.getParameter("flight_speed", cfg.getParameter("horizontal_speed", 0.3))));
            double maxHeight = Math.max(0.0, toDouble(cfg.getParameter("max_height", cfg.getParameter("flight_height", 10.0))));

            org.bukkit.util.Vector horizontal = new org.bukkit.util.Vector(0, 0, 0);
            if (target != null && target.isOnline() && !target.isDead()) {
                org.bukkit.util.Vector toTarget = target.getLocation().toVector().subtract(caster.getLocation().toVector());
                horizontal = new org.bukkit.util.Vector(toTarget.getX(), 0, toTarget.getZ());
            }
            if (horizontal.lengthSquared() > 0) {
                horizontal.normalize().multiply(hSpeed);
            }

            double dy = 0.0;
            double casterY = caster.getLocation().getY();
            double targetY = (target != null ? target.getLocation().getY() : casterY + 1.0);
            double groundY = caster.getWorld().getHighestBlockYAt(caster.getLocation());
            double heightAboveGround = casterY - groundY;
            boolean belowTarget = casterY + 0.5 < targetY;
            if (belowTarget || heightAboveGround < maxHeight) {
                dy = Math.max(vSpeed, 0.15); // 最小上升速度，强制克服重力
            }

            caster.setVelocity(new org.bukkit.util.Vector(horizontal.getX(), dy, horizontal.getZ()));
            caster.setFallDistance(0.0f);
        }
    }

    private void applySpecialEffect(Player target, String skillName, SkillConfig cfg) {
        if (skillName.contains("inventory_chaos")) {
            // 简化：给玩家随机物品
            target.getInventory().addItem(new ItemStack(Material.DIRT, 1));
        } else if (skillName.contains("time_control")) {
            // 读取参数并应用到目标（备用路径；主要逻辑已在handleSpecialEffects内批量生效）
            int duration = Math.max(1, toInt(cfg.getParameter("time_duration", cfg.getParameter("duration", 100))));
            int amplifier = Math.max(0, toInt(cfg.getParameter("time_amplifier", cfg.getParameter("slowness_level", 3))));
            target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, amplifier));
        } else if (skillName.contains("fog")) {
            // 简化：失明效果
            target.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 100, 1));
        }
    }

    // 工具方法
    public boolean isAlive(Entity e) {
        return e != null && e.isValid() && !e.isDead();
    }

    public Player findNearestPlayer(LivingEntity from, double range) {
        World w = from.getWorld();
        Location c = from.getLocation();
        Player best = null;
        double bestD2 = range * range;
        for (Player p : w.getPlayers()) {
            if (!p.isOnline() || p.isDead()) continue;
            double d2 = p.getLocation().distanceSquared(c);
            if (d2 <= bestD2) {
                bestD2 = d2;
                best = p;
            }
        }
        return best;
    }

    // 辅助：在玩家周围生成圈式尖牙与中心尖牙
    private void createFangCircle(org.bukkit.Location center, org.bukkit.entity.Evoker evoker, int count, double radius) {
        for (int i = 0; i < Math.max(1, count); i++) {
            double angle = 2 * Math.PI * i / Math.max(1, count);
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            org.bukkit.Location fangLoc = center.clone().add(x, 0, z);
            fangLoc.setY(fangLoc.getWorld().getHighestBlockYAt(fangLoc) + 1);
            org.bukkit.entity.Entity fang = fangLoc.getWorld().spawnEntity(fangLoc, org.bukkit.entity.EntityType.EVOKER_FANGS);
            if (fang instanceof org.bukkit.entity.EvokerFangs && evoker != null) {
                ((org.bukkit.entity.EvokerFangs) fang).setOwner(evoker);
            }
            fangLoc.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, fangLoc.clone().add(0, 0.5, 0), 8, 0.2, 0.5, 0.2, 0);
        }
        center.getWorld().playSound(center, org.bukkit.Sound.ENTITY_EVOKER_PREPARE_ATTACK, 1.0f, 1.2f);
    }

    private void createCenterFang(org.bukkit.Location center, org.bukkit.entity.Evoker evoker) {
        org.bukkit.Location fangLoc = center.clone();
        fangLoc.setY(fangLoc.getWorld().getHighestBlockYAt(fangLoc) + 1);
        org.bukkit.entity.Entity fang = fangLoc.getWorld().spawnEntity(fangLoc, org.bukkit.entity.EntityType.EVOKER_FANGS);
        if (fang instanceof org.bukkit.entity.EvokerFangs && evoker != null) {
            ((org.bukkit.entity.EvokerFangs) fang).setOwner(evoker);
        }
        fangLoc.getWorld().createExplosion(fangLoc, 0.0F, false, false);
        fangLoc.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, fangLoc.clone().add(0, 0.5, 0), 20, 0.5, 0.5, 0.5, 0.1);
        center.getWorld().playSound(center, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);
    }

    public int toInt(Object v) {
        if (v instanceof Number) return ((Number) v).intValue();
        try { return Integer.parseInt(String.valueOf(v)); } catch (Exception e) { return 0; }
    }

    public double toDouble(Object v) {
        if (v instanceof Number) return ((Number) v).doubleValue();
        try { return Double.parseDouble(String.valueOf(v)); } catch (Exception e) { return 0.0; }
    }

    /**
     * 执行IDC14风格的机枪攻击
     */
    private void performMachineGunAttack(LivingEntity caster, Player target, int bulletCount, double bulletDamage, double spreadAngle) {
        Location shootLocation = caster.getEyeLocation().clone();
        org.bukkit.util.Vector direction = target.getLocation().add(0, 1, 0).subtract(shootLocation).toVector().normalize();

        // 播放机枪射击音效
        shootLocation.getWorld().playSound(shootLocation, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 2.0f);

        // 发射多颗子弹（模拟机枪连射）
        for (int i = 0; i < bulletCount; i++) {
            final int index = i;

            // 延迟发射，模拟连射效果
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    if (!isAlive(caster)) return;

                    // 计算散布角度
                    double yawOffset = (Math.random() - 0.5) * spreadAngle;
                    double pitchOffset = (Math.random() - 0.5) * spreadAngle * 0.5;

                    org.bukkit.util.Vector bulletDir = rotateVector(direction.clone(), yawOffset, pitchOffset);

                    // 创建粒子轨迹模拟子弹
                    createBulletTrail(shootLocation.clone(), bulletDir, bulletDamage, 30.0);
                }
            }.runTaskLater(plugin, index * 2); // 每颗子弹间隔2tick
        }
    }

    /**
     * 创建子弹粒子轨迹
     */
    private void createBulletTrail(Location start, org.bukkit.util.Vector direction, double damage, double maxDistance) {
        new org.bukkit.scheduler.BukkitRunnable() {
            Location currentLoc = start.clone();
            double traveled = 0;

            @Override
            public void run() {
                if (traveled >= maxDistance || currentLoc.getBlock().getType().isSolid()) {
                    cancel();
                    return;
                }

                // 移动子弹
                currentLoc.add(direction.clone().multiply(0.8));
                traveled += 0.8;

                // 显示子弹粒子
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.CRIT, currentLoc, 2, 0.05, 0.05, 0.05, 0.02);
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, currentLoc, 1, 0.02, 0.02, 0.02, 0.01);

                // 碰撞检测
                for (org.bukkit.entity.Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof Player) {
                        Player hitPlayer = (Player) entity;
                        hitPlayer.damage(damage);

                        // 命中效果
                        currentLoc.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR, currentLoc, 5, 0.2, 0.2, 0.2, 0.1);
                        currentLoc.getWorld().playSound(currentLoc, org.bukkit.Sound.ENTITY_PLAYER_HURT, 0.8f, 1.2f);

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * 执行IDC14风格的烟雾粒子效果
     */
    private void performSmokeParticleEffect(LivingEntity caster, double range, int blindnessDuration, int blindnessLevel) {
        Location center = caster.getLocation();

        // 创建烟雾粒子环
        int points = 32;
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = center.getX() + Math.cos(angle) * range;
            double z = center.getZ() + Math.sin(angle) * range;
            Location particleLoc = new Location(center.getWorld(), x, center.getY() + 0.5, z);

            // 显示烟雾粒子
            center.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, particleLoc, 8, 0.3, 0.3, 0.3, 0.02);
            center.getWorld().spawnParticle(org.bukkit.Particle.LARGE_SMOKE, particleLoc, 3, 0.2, 0.2, 0.2, 0.01);
        }

        // 对范围内玩家施加失明效果
        for (org.bukkit.entity.Entity entity : center.getWorld().getNearbyEntities(center, range, 3, range)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.BLINDNESS, blindnessDuration, blindnessLevel - 1));

                // 显示受影响效果
                Location playerLoc = player.getLocation().add(0, 1, 0);
                center.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, playerLoc, 15, 0.5, 1.0, 0.5, 0.1);
            }
        }
    }

    /**
     * 执行IDC13风格的冰霜光环效果
     */
    private void performFrostAuraEffect(LivingEntity caster, double range, int slownessDuration, int slownessLevel) {
        Location center = caster.getLocation();

        // 播放冰霜音效
        center.getWorld().playSound(center, org.bukkit.Sound.BLOCK_GLASS_BREAK, 1.0f, 1.5f);

        // 创建冰霜粒子环
        int points = 32;
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = center.getX() + Math.cos(angle) * range;
            double z = center.getZ() + Math.sin(angle) * range;
            Location particleLoc = new Location(center.getWorld(), x, center.getY() + 0.1, z);

            // 显示冰霜粒子
            center.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, particleLoc, 5, 0.2, 0.2, 0.2, 0.0);
            center.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, particleLoc, 3, 0.2, 0.2, 0.2, 0.0);
        }

        // 对范围内玩家施加缓慢效果
        for (org.bukkit.entity.Entity entity : center.getWorld().getNearbyEntities(center, range, 3, range)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SLOWNESS, slownessDuration, slownessLevel - 1));

                // 显示受影响效果
                Location playerLoc = player.getLocation().add(0, 1, 0);
                center.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, playerLoc, 10, 0.5, 1.0, 0.5, 0.1);
            }
        }
    }

    /**
     * 旋转向量（用于子弹散布）
     */
    private org.bukkit.util.Vector rotateVector(org.bukkit.util.Vector vector, double yawDegrees, double pitchDegrees) {
        double yaw = Math.toRadians(yawDegrees);
        double pitch = Math.toRadians(pitchDegrees);

        // 简化的向量旋转
        double x = vector.getX();
        double y = vector.getY();
        double z = vector.getZ();

        // 应用偏航旋转
        double newX = x * Math.cos(yaw) - z * Math.sin(yaw);
        double newZ = x * Math.sin(yaw) + z * Math.cos(yaw);

        return new org.bukkit.util.Vector(newX, y + Math.sin(pitch) * 0.1, newZ).normalize();
    }

    /**
     * 执行IDC14风格的冲刺攻击
     */
    private void performDashAttack(LivingEntity caster, Player target, double damage, double speed) {
        Location startLoc = caster.getLocation();
        Location targetLoc = target.getLocation();

        // 计算冲刺方向和距离
        org.bukkit.util.Vector direction = targetLoc.subtract(startLoc).toVector().normalize();
        double distance = startLoc.distance(targetLoc);

        // 播放冲刺音效
        startLoc.getWorld().playSound(startLoc, org.bukkit.Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 1.5f);

        // 逐步冲刺移动（基于IDC14实现）
        int totalSteps = Math.max(5, (int)(distance * 2)); // 根据距离计算步数
        final double stepDistance = distance / totalSteps;

        new org.bukkit.scheduler.BukkitRunnable() {
            int steps = 0;
            Location currentLoc = startLoc.clone();

            @Override
            public void run() {
                if (!isAlive(caster) || steps >= totalSteps) {
                    cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction.clone().multiply(stepDistance));
                caster.teleport(currentLoc);

                // 显示冲刺粒子效果
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.CRIT, currentLoc.clone().add(0, 1, 0), 5, 0.3, 0.5, 0.3, 0.1);
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, currentLoc, 3, 0.2, 0.2, 0.2, 0.05);

                // 最后一步时检查是否命中目标
                if (steps == totalSteps - 1) {
                    if (target.getLocation().distance(currentLoc) <= 2.0) {
                        // 造成伤害
                        target.damage(damage, caster);

                        // 击退效果
                        target.setVelocity(direction.clone().multiply(1.0));

                        // 播放命中音效
                        target.getWorld().playSound(target.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);

                        // 显示伤害粒子
                        target.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR, target.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                    }
                }

                steps++;
            }
        }.runTaskTimer(plugin, 0, 2); // 每2tick执行一次
    }

    /**
     * 执行IDC15风格的红色粒子球效果
     */
    private void performRedParticleBallEffect(LivingEntity caster, double radius, int particleCount) {
        Location center = caster.getLocation().add(0, 1, 0); // 稍微抬高

        // 创建红色粒子环
        for (int i = 0; i < particleCount; i++) {
            double angle = 2 * Math.PI * i / particleCount;
            double x = center.getX() + Math.cos(angle) * radius;
            double z = center.getZ() + Math.sin(angle) * radius;
            Location particleLoc = new Location(center.getWorld(), x, center.getY(), z);

            // 显示红色粒子
            center.getWorld().spawnParticle(org.bukkit.Particle.DUST, particleLoc, 3, 0.1, 0.1, 0.1, 0.0,
                new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(255, 0, 0), 1.5f));
            center.getWorld().spawnParticle(org.bukkit.Particle.FLAME, particleLoc, 2, 0.05, 0.05, 0.05, 0.0);
        }

        // 中心也显示一些红色粒子
        center.getWorld().spawnParticle(org.bukkit.Particle.DUST, center, 8, 0.3, 0.3, 0.3, 0.0,
            new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(200, 0, 0), 1.0f));
    }

    /**
     * 执行IDC16风格的潜影贝三连射子弹攻击
     */
    private void performShulkerBulletAttack(LivingEntity caster, Player target, int bulletCount, double bulletDamage, int levitationDuration) {
        Location shootLocation = caster.getLocation().add(0, 1, 0);

        // 播放潜影贝射击音效
        shootLocation.getWorld().playSound(shootLocation, org.bukkit.Sound.ENTITY_SHULKER_SHOOT, 1.0f, 1.0f);

        // 发射多颗潜影贝子弹（模拟三连射）
        for (int i = 0; i < bulletCount; i++) {
            final int index = i;
            final Player finalTarget = target;

            // 延迟发射，模拟连射效果（每个子弹间隔5tick）
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    if (!isAlive(caster) || finalTarget.isDead()) return;

                    // 发射真正的潜影贝子弹
                    org.bukkit.entity.ShulkerBullet bullet = (org.bukkit.entity.ShulkerBullet)
                        caster.getWorld().spawnEntity(shootLocation, org.bukkit.entity.EntityType.SHULKER_BULLET);
                    bullet.setTarget(finalTarget);

                    // 设置子弹元数据用于伤害处理
                    bullet.setMetadata("idz_shulker_damage", new org.bukkit.metadata.FixedMetadataValue(plugin, bulletDamage));
                    bullet.setMetadata("idz_levitation_duration", new org.bukkit.metadata.FixedMetadataValue(plugin, levitationDuration));

                    // 添加粒子效果
                    caster.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, shootLocation, 10, 0.2, 0.2, 0.2, 0.05);

                    logger.info(String.format("IDZ发射第%d颗潜影贝子弹，目标: %s", index + 1, finalTarget.getName()));
                }
            }.runTaskLater(plugin, index * 5); // 每颗子弹间隔5tick
        }
    }

    /**
     * 检查并执行IDC16风格的受伤混乱物品栏
     */
    private void checkAndPerformShulkerChaos(LivingEntity caster) {
        try {
            // 读取元数据参数
            double healthThreshold = caster.hasMetadata("shulker_chaos_threshold") ?
                caster.getMetadata("shulker_chaos_threshold").get(0).asDouble() : 0.7;
            double shuffleRange = caster.hasMetadata("shulker_chaos_range") ?
                caster.getMetadata("shulker_chaos_range").get(0).asDouble() : 8.0;
            long shuffleCooldown = caster.hasMetadata("shulker_chaos_cooldown") ?
                caster.getMetadata("shulker_chaos_cooldown").get(0).asLong() : 10000L;
            long lastShuffleTime = caster.hasMetadata("shulker_chaos_last_time") ?
                caster.getMetadata("shulker_chaos_last_time").get(0).asLong() : 0L;

            // 检查是否受伤（生命值低于阈值）
            double currentHealth = caster.getHealth();
            double maxHealth = caster.getMaxHealth();

            if (currentHealth < maxHealth * healthThreshold &&
                System.currentTimeMillis() - lastShuffleTime > shuffleCooldown) {

                performShulkerInventoryChaos(caster, shuffleRange);

                // 更新最后触发时间
                caster.setMetadata("shulker_chaos_last_time",
                    new org.bukkit.metadata.FixedMetadataValue(plugin, System.currentTimeMillis()));
            }
        } catch (Exception e) {
            logger.warning("检查潜影贝混乱时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC16风格的潜影贝混乱物品栏效果
     */
    private void performShulkerInventoryChaos(LivingEntity caster, double shuffleRange) {
        Location center = caster.getLocation();

        // 查找范围内的玩家
        java.util.List<Player> affectedPlayers = new java.util.ArrayList<>();
        for (org.bukkit.entity.Entity entity : center.getWorld().getNearbyEntities(center, shuffleRange, 3, shuffleRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != org.bukkit.GameMode.SPECTATOR &&
                    player.getGameMode() != org.bukkit.GameMode.CREATIVE) {
                    affectedPlayers.add(player);
                }
            }
        }

        if (!affectedPlayers.isEmpty()) {
            for (Player player : affectedPlayers) {
                // 混乱玩家的1-9格物品栏
                shufflePlayerHotbar(player);

                // 播放音效
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);

                // 添加粒子效果
                player.getWorld().spawnParticle(org.bukkit.Particle.REVERSE_PORTAL,
                    player.getLocation().add(0, 1, 0), 30, 0.5, 1.0, 0.5, 0.05);

                logger.info("IDZ潜影贝混乱玩家物品栏: " + player.getName());
            }
        }
    }

    /**
     * 混乱玩家的物品栏（1-9格）
     */
    private void shufflePlayerHotbar(Player player) {
        try {
            org.bukkit.inventory.PlayerInventory inventory = player.getInventory();

            // 创建物品列表
            java.util.List<org.bukkit.inventory.ItemStack> hotbarItems = new java.util.ArrayList<>();
            for (int i = 0; i < 9; i++) {
                hotbarItems.add(inventory.getItem(i));
            }

            // 打乱物品列表
            java.util.Collections.shuffle(hotbarItems);

            // 将打乱后的物品放回物品栏
            for (int i = 0; i < 9; i++) {
                inventory.setItem(i, hotbarItems.get(i));
            }

            // 更新玩家物品栏
            player.updateInventory();
        } catch (Exception e) {
            logger.warning("混乱玩家物品栏时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC16/IDC19风格的潜影贝隐身效果
     */
    private void performShulkerInvisibility(LivingEntity caster, int invisibilityDuration) {
        try {
            // 播放隐身效果音效和粒子（基于IDC19实现）
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
            caster.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, caster.getLocation().add(0, 1, 0), 30, 0.5, 1, 0.5, 0.05);

            // 添加隐身效果
            caster.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.INVISIBILITY, invisibilityDuration, 0));

            // 如果是潜影贝，设置实体隐身属性
            if (caster instanceof org.bukkit.entity.Shulker) {
                try {
                    caster.setInvisible(true);
                    // 延迟恢复可见性
                    new org.bukkit.scheduler.BukkitRunnable() {
                        @Override
                        public void run() {
                            if (isAlive(caster)) {
                                caster.setInvisible(false);
                            }
                        }
                    }.runTaskLater(plugin, invisibilityDuration);
                } catch (Exception ignored) {}
            }

            logger.info(String.format("IDZ潜影贝隐身: %s 隐身 %d tick", caster.getType(), invisibilityDuration));
        } catch (Exception e) {
            logger.warning("执行潜影贝隐身时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC17风格的雪球弹幕攻击
     */
    private void performSnowballBarrage(LivingEntity caster, Player target, int snowballCount, int snowballInterval,
                                       double spreadFactor, double snowballSpeed, double snowballDamage, double knockbackStrength, boolean poisonEnabled) {
        Location shootLocation = caster.getEyeLocation();
        org.bukkit.util.Vector baseDirection = target.getLocation().add(0, 1, 0).subtract(shootLocation).toVector().normalize();

        // 连发雪球攻击（基于IDC17实现）
        for (int i = 0; i < snowballCount; i++) {
            final int index = i;

            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    if (!isAlive(caster) || target.isDead()) return;

                    // 计算散布方向
                    org.bukkit.util.Vector direction = baseDirection.clone();
                    if (spreadFactor > 0) {
                        double yawOffset = (Math.random() - 0.5) * spreadFactor * 2;
                        double pitchOffset = (Math.random() - 0.5) * spreadFactor;
                        direction = rotateVector(direction, yawOffset * 57.3, pitchOffset * 57.3); // 转换为度数
                    }

                    // 发射雪球
                    org.bukkit.entity.Snowball snowball = caster.launchProjectile(
                        org.bukkit.entity.Snowball.class, direction.multiply(snowballSpeed));

                    // 添加元数据，用于识别这是IDZ雪球弹幕
                    snowball.setMetadata("idz_snowball_barrage", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    snowball.setMetadata("idz_snowball_damage", new org.bukkit.metadata.FixedMetadataValue(plugin, snowballDamage));
                    snowball.setMetadata("idz_snowball_knockback", new org.bukkit.metadata.FixedMetadataValue(plugin, knockbackStrength));

                    // 添加毒性效果到雪球（如果启用）
                    if (poisonEnabled) {
                        snowball.setMetadata("idz_poison_snowball", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    }

                    // 添加粒子效果
                    shootLocation.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, shootLocation, 5, 0.1, 0.1, 0.1, 0.01);

                    // 播放发射音效
                    caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_SNOWBALL_THROW,
                                              0.5f, 1.0f + (index * 0.1f)); // 音调随发射次数变化

                    logger.info(String.format("IDZ发射第%d个雪球弹幕，目标: %s", index + 1, target.getName()));
                }
            }.runTaskLater(plugin, index * snowballInterval);
        }
    }

    /**
     * 执行IDC17风格的雪人冰冻光环效果
     */
    private void performSnowFreezeAura(LivingEntity caster, double freezeRange, int freezeDuration,
                                      int slownessLevel, int miningFatigueLevel) {
        Location center = caster.getLocation();

        // 播放冰冻音效（基于变异雷霆僵尸的冻结技能）
        center.getWorld().playSound(center, org.bukkit.Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);
        center.getWorld().playSound(center, org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.5f);

        // 创建冰霜粒子环
        int points = 32;
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = center.getX() + Math.cos(angle) * freezeRange;
            double z = center.getZ() + Math.sin(angle) * freezeRange;
            Location particleLoc = new Location(center.getWorld(), x, center.getY() + 0.1, z);

            // 显示冰霜粒子
            center.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, particleLoc, 3, 0.2, 0.2, 0.2, 0.0);
            center.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, particleLoc, 2, 0.2, 0.2, 0.2, 0.0);
        }

        // 对范围内玩家施加冰冻效果
        for (org.bukkit.entity.Entity entity : center.getWorld().getNearbyEntities(center, freezeRange, 3, freezeRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;

                // 施加缓慢效果
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SLOWNESS, freezeDuration, slownessLevel - 1));

                // 施加挖掘疲劳效果
                if (miningFatigueLevel > 0) {
                    player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.MINING_FATIGUE, freezeDuration, miningFatigueLevel - 1));
                }

                // 冻结视觉效果
                Location playerLoc = player.getLocation().add(0, 1, 0);
                player.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, playerLoc, 20, 0.5, 1, 0.5, 0.1);
                player.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, playerLoc, 15, 0.5, 1, 0.5, 0.1);

                logger.info(String.format("IDZ雪人冰冻光环冻结玩家: %s (缓慢%d级，挖掘疲劳%d级，持续%d tick)",
                           player.getName(), slownessLevel, miningFatigueLevel, freezeDuration));
            }
        }
    }

    /**
     * 执行IDC18风格的草方块伸展攻击
     */
    private void performGrassBlockAttack(LivingEntity caster, Player target, double grassDamage, double grassSpeed, int slownessDuration) {
        try {
            // 播放草方块攻击音效（基于IDC18实现）
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.BLOCK_GRASS_BREAK, 1.0f, 0.5f);

            // 发射真实草方块延伸攻击
            Location start = caster.getLocation().add(0, 1.5, 0);
            org.bukkit.util.Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
            org.bukkit.World world = start.getWorld();

            // 创建草方块延伸路径（基于IDC18原版实现）
            java.util.List<Location> grassBlockLocations = new java.util.ArrayList<>();
            final double maxDistance = start.distance(target.getLocation()) + 5;

            new org.bukkit.scheduler.BukkitRunnable() {
                Location currentLoc = start.clone();
                double traveled = 0;
                int step = 0;

                @Override
                public void run() {
                    if (!isAlive(caster) || traveled >= maxDistance) {
                        cancel();
                        return;
                    }

                    // 移动到下一个位置
                    currentLoc.add(direction.clone().multiply(grassSpeed));
                    traveled += grassSpeed;
                    step++;

                    // 智能处理障碍物
                    Location adjustedLoc = handleObstacles(currentLoc, direction, world);
                    if (adjustedLoc != null) {
                        currentLoc = adjustedLoc;
                    }

                    // 放置草方块（每隔一定距离放置一个）
                    if (step % Math.max(1, (int)(1.0 / grassSpeed)) == 0) { // 确保草方块间距合理
                        Location grassLoc = findAirLocationForGrass(currentLoc, world);
                        if (grassLoc != null) {
                            org.bukkit.block.Block block = grassLoc.getBlock();
                            if (block.getType() == org.bukkit.Material.AIR) {
                                // 只在空气中放置草方块，不破坏原有方块
                                block.setType(org.bukkit.Material.GRASS_BLOCK);
                                grassBlockLocations.add(grassLoc.clone());

                                // 显示草方块生成粒子
                                world.spawnParticle(org.bukkit.Particle.BLOCK, grassLoc.clone().add(0.5, 1, 0.5),
                                                  5, 0.3, 0.3, 0.3, 0.1, org.bukkit.Material.GRASS_BLOCK.createBlockData());
                            }
                        }
                    }

                    // 检测是否击中玩家
                    for (org.bukkit.entity.Entity entity : world.getNearbyEntities(currentLoc, 1.5, 1.5, 1.5)) {
                        if (entity instanceof Player && entity != caster) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害（与IDC18原版一致：44点伤害）
                            hitPlayer.damage(grassDamage, caster);

                            // 禁止移动（通过给予100级缓慢效果实现）
                            hitPlayer.addPotionEffect(new org.bukkit.potion.PotionEffect(
                                org.bukkit.potion.PotionEffectType.SLOWNESS, slownessDuration, 100));

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR,
                                hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                            logger.info(String.format("IDC18草方块攻击命中玩家: %s，伤害: %.2f，缓慢%d tick",
                                       hitPlayer.getName(), grassDamage, slownessDuration));

                            // 延迟清理所有草方块
                            cleanupGrassBlocks(grassBlockLocations);
                            cancel();
                            return;
                        }
                    }

                    // 如果到达最大距离，清理草方块
                    if (traveled >= maxDistance) {
                        cleanupGrassBlocks(grassBlockLocations);
                    }
                }
            }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（最高速度）

        } catch (Exception e) {
            logger.warning("执行草方块伸展攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC18风格的智能主动追踪玩家
     */
    private void performPlayerTracking(LivingEntity caster, double trackingRange, double trackingSpeed) {
        try {
            // 查找范围内最近的玩家
            Player target = findNearestPlayer(caster, trackingRange);
            if (target == null) return;

            Location casterLoc = caster.getLocation();
            Location targetLoc = target.getLocation();
            double distance = casterLoc.distance(targetLoc);

            // 如果距离太近，不需要移动
            if (distance < 3.0) {
                // 只是面向目标
                if (caster instanceof org.bukkit.entity.Mob) {
                    ((org.bukkit.entity.Mob) caster).setTarget(target);
                }
                return;
            }

            // 智能路径寻找
            org.bukkit.util.Vector moveDirection = calculateSmartPath(casterLoc, targetLoc, caster);

            if (moveDirection != null) {
                // 应用智能移动
                applySmartMovement(caster, moveDirection, trackingSpeed, target);

                // 显示追踪粒子效果
                caster.getWorld().spawnParticle(org.bukkit.Particle.ANGRY_VILLAGER,
                    casterLoc.clone().add(0, 2, 0), 2, 0.2, 0.2, 0.2, 0);

                logger.info(String.format("IDC18智能追踪: %s 追踪玩家 %s，距离: %.2f",
                           caster.getType(), target.getName(), distance));
            }

        } catch (Exception e) {
            logger.warning("执行智能追踪玩家时出错: " + e.getMessage());
        }
    }

    /**
     * 计算智能路径（避开障碍物）
     */
    private org.bukkit.util.Vector calculateSmartPath(Location from, Location to, LivingEntity caster) {
        org.bukkit.util.Vector directPath = to.clone().subtract(from).toVector().normalize();

        // 检查直接路径是否被阻挡
        if (isPathClear(from, to, caster)) {
            return directPath;
        }

        // 尝试绕过障碍物
        org.bukkit.util.Vector[] alternativePaths = {
            rotateVector(directPath, 45, 0),   // 右转45度
            rotateVector(directPath, -45, 0),  // 左转45度
            rotateVector(directPath, 90, 0),   // 右转90度
            rotateVector(directPath, -90, 0),  // 左转90度
            rotateVector(directPath, 0, 30),   // 向上30度
            rotateVector(directPath, 0, -30)   // 向下30度
        };

        // 找到最佳替代路径
        for (org.bukkit.util.Vector altPath : alternativePaths) {
            Location testTo = from.clone().add(altPath.clone().multiply(3));
            if (isPathClear(from, testTo, caster)) {
                return altPath;
            }
        }

        // 如果所有路径都被阻挡，尝试跳跃
        if (canJump(from, caster)) {
            org.bukkit.util.Vector jumpPath = directPath.clone();
            jumpPath.setY(0.8); // 添加向上的分量
            return jumpPath;
        }

        return directPath; // 最后回退到直接路径
    }

    /**
     * 检查路径是否畅通
     */
    private boolean isPathClear(Location from, Location to, LivingEntity caster) {
        org.bukkit.util.Vector direction = to.clone().subtract(from).toVector();
        double distance = direction.length();
        direction.normalize();

        // 检查路径上的方块
        for (double d = 0; d < distance; d += 0.5) {
            Location checkLoc = from.clone().add(direction.clone().multiply(d));

            // 检查脚部和头部位置
            if (checkLoc.getBlock().getType().isSolid() ||
                checkLoc.clone().add(0, 1, 0).getBlock().getType().isSolid()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查是否可以跳跃
     */
    private boolean canJump(Location location, LivingEntity caster) {
        // 检查上方是否有足够空间
        for (int y = 1; y <= 3; y++) {
            if (location.clone().add(0, y, 0).getBlock().getType().isSolid()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 应用智能移动
     */
    private void applySmartMovement(LivingEntity caster, org.bukkit.util.Vector direction, double speed, Player target) {
        // 调整移动速度
        org.bukkit.util.Vector movement = direction.clone().multiply(speed * 0.4);

        // 限制Y轴移动，避免过度飞行
        if (movement.getY() > 0.5) {
            movement.setY(0.5);
        } else if (movement.getY() < -0.5) {
            movement.setY(-0.2);
        }

        // 应用移动
        caster.setVelocity(movement);

        // 设置目标和面向
        if (caster instanceof org.bukkit.entity.Mob) {
            org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) caster;
            mob.setTarget(target);

            // 设置AI目标（如果可能）
            try {
                if (mob.getPathfinder() != null) {
                    mob.getPathfinder().moveTo(target.getLocation(), speed);
                }
            } catch (Exception ignored) {
                // 某些实体可能不支持pathfinder
            }
        }

        // 让实体面向目标
        Location casterLoc = caster.getLocation();
        Location targetLoc = target.getLocation();
        org.bukkit.util.Vector lookDirection = targetLoc.subtract(casterLoc).toVector().normalize();

        float yaw = (float) Math.toDegrees(Math.atan2(-lookDirection.getX(), lookDirection.getZ()));
        float pitch = (float) Math.toDegrees(Math.asin(-lookDirection.getY()));

        casterLoc.setYaw(yaw);
        casterLoc.setPitch(pitch);
        caster.teleport(casterLoc);
    }

    /**
     * 清理草方块延伸路径（恢复为空气）
     */
    private void cleanupGrassBlocks(java.util.List<Location> grassBlockLocations) {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                for (Location loc : grassBlockLocations) {
                    try {
                        org.bukkit.block.Block block = loc.getBlock();
                        if (block.getType() == org.bukkit.Material.GRASS_BLOCK) {
                            // 恢复为空气（因为我们只在空气中放置草方块）
                            block.setType(org.bukkit.Material.AIR);

                            // 显示草方块消失粒子
                            loc.getWorld().spawnParticle(org.bukkit.Particle.BLOCK, loc.clone().add(0.5, 1, 0.5),
                                                       3, 0.2, 0.2, 0.2, 0.1, org.bukkit.Material.GRASS_BLOCK.createBlockData());
                        }
                    } catch (Exception e) {
                        // 忽略清理错误
                    }
                }
            }
        }.runTaskLater(plugin, 40L); // 2秒后清理所有草方块
    }

    /**
     * 智能处理障碍物
     */
    private Location handleObstacles(Location currentLoc, org.bukkit.util.Vector direction, org.bukkit.World world) {
        // 如果当前位置是固体方块，尝试调整位置
        if (currentLoc.getBlock().getType().isSolid()) {
            // 尝试向上调整
            Location upLoc = currentLoc.clone().add(0, 1, 0);
            if (!upLoc.getBlock().getType().isSolid()) {
                return upLoc;
            }

            // 尝试向下调整
            Location downLoc = currentLoc.clone().add(0, -1, 0);
            if (!downLoc.getBlock().getType().isSolid()) {
                return downLoc;
            }

            // 尝试左右调整
            org.bukkit.util.Vector perpendicular = new org.bukkit.util.Vector(-direction.getZ(), 0, direction.getX()).normalize();

            Location leftLoc = currentLoc.clone().add(perpendicular.clone().multiply(0.5));
            if (!leftLoc.getBlock().getType().isSolid()) {
                return leftLoc;
            }

            Location rightLoc = currentLoc.clone().add(perpendicular.clone().multiply(-0.5));
            if (!rightLoc.getBlock().getType().isSolid()) {
                return rightLoc;
            }
        }

        return null; // 无法调整，使用原位置
    }

    /**
     * 寻找空气位置放置草方块（不破坏原有方块）
     */
    private Location findAirLocationForGrass(Location baseLoc, org.bukkit.World world) {
        // 优先使用原位置（如果是空气）
        if (baseLoc.getBlock().getType() == org.bukkit.Material.AIR) {
            return baseLoc;
        }

        // 尝试周围的空气位置
        Location[] candidates = {
            baseLoc.clone().add(0, 1, 0),   // 上方
            baseLoc.clone().add(0, -1, 0),  // 下方
            baseLoc.clone().add(1, 0, 0),   // 东
            baseLoc.clone().add(-1, 0, 0),  // 西
            baseLoc.clone().add(0, 0, 1),   // 南
            baseLoc.clone().add(0, 0, -1),  // 北
            baseLoc.clone().add(1, 1, 0),   // 东上
            baseLoc.clone().add(-1, 1, 0),  // 西上
            baseLoc.clone().add(0, 1, 1),   // 南上
            baseLoc.clone().add(0, 1, -1),  // 北上
            baseLoc.clone().add(1, -1, 0),  // 东下
            baseLoc.clone().add(-1, -1, 0), // 西下
            baseLoc.clone().add(0, -1, 1),  // 南下
            baseLoc.clone().add(0, -1, -1)  // 北下
        };

        for (Location candidate : candidates) {
            if (candidate.getBlock().getType() == org.bukkit.Material.AIR) {
                return candidate;
            }
        }

        return null; // 找不到合适的空气位置
    }

    /**
     * 执行IDC18风格的声波弹攻击
     */
    private void performSonicAttack(LivingEntity caster, Player target, double sonicDamage, double sonicSpeed) {
        try {
            Location shootLocation = caster.getEyeLocation();
            org.bukkit.util.Vector direction = target.getLocation().add(0, 1, 0).subtract(shootLocation).toVector().normalize();

            // 播放声波弹发射音效（基于IDC18实现）
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_WITHER_SHOOT, 1.0f, 1.5f);
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 2.0f);

            // 创建声波弹粒子轨迹（不使用箭矢，使用纯粒子效果）
            new org.bukkit.scheduler.BukkitRunnable() {
                Location currentLoc = shootLocation.clone();
                double traveled = 0;
                final double maxDistance = shootLocation.distance(target.getLocation()) + 10;

                @Override
                public void run() {
                    if (!isAlive(caster) || traveled >= maxDistance) {
                        cancel();
                        return;
                    }

                    // 移动声波弹
                    currentLoc.add(direction.clone().multiply(sonicSpeed));
                    traveled += sonicSpeed;

                    // 检查是否碰到固体方块
                    if (currentLoc.getBlock().getType().isSolid()) {
                        // 声波弹爆炸效果
                        createSonicExplosion(currentLoc);
                        cancel();
                        return;
                    }

                    // 显示声波弹粒子效果（圆形波纹）
                    org.bukkit.World world = currentLoc.getWorld();

                    // 主要声波粒子（白色和蓝色）
                    world.spawnParticle(org.bukkit.Particle.SONIC_BOOM, currentLoc, 1, 0, 0, 0, 0);
                    world.spawnParticle(org.bukkit.Particle.ENCHANT, currentLoc, 8, 0.3, 0.3, 0.3, 0.1);
                    world.spawnParticle(org.bukkit.Particle.CLOUD, currentLoc, 3, 0.2, 0.2, 0.2, 0.05);

                    // 声波环形效果
                    for (int i = 0; i < 8; i++) {
                        double angle = 2 * Math.PI * i / 8;
                        double x = currentLoc.getX() + Math.cos(angle) * 0.5;
                        double z = currentLoc.getZ() + Math.sin(angle) * 0.5;
                        Location ringLoc = new Location(world, x, currentLoc.getY(), z);
                        world.spawnParticle(org.bukkit.Particle.CRIT, ringLoc, 1, 0, 0, 0, 0);
                    }

                    // 碰撞检测
                    for (org.bukkit.entity.Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != caster) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(sonicDamage, caster);

                            // 声波弹命中效果
                            createSonicExplosion(currentLoc);

                            // 播放命中音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.2f);

                            // 显示命中粒子效果
                            hitPlayer.getWorld().spawnParticle(org.bukkit.Particle.SONIC_BOOM, hitPlayer.getLocation().add(0, 1, 0), 2, 0.5, 0.5, 0.5, 0);
                            hitPlayer.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 8, 0.3, 0.3, 0.3, 0.1);

                            logger.info(String.format("IDC18声波弹攻击命中玩家: %s，伤害: %.2f", hitPlayer.getName(), sonicDamage));

                            cancel();
                            return;
                        }
                    }
                }
            }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次

        } catch (Exception e) {
            logger.warning("执行声波弹攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 创建声波弹爆炸效果
     */
    private void createSonicExplosion(Location location) {
        org.bukkit.World world = location.getWorld();

        // 播放爆炸音效
        world.playSound(location, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.5f);
        world.playSound(location, org.bukkit.Sound.ENTITY_WITHER_HURT, 0.8f, 1.8f);

        // 爆炸粒子效果
        world.spawnParticle(org.bukkit.Particle.SONIC_BOOM, location, 3, 0.5, 0.5, 0.5, 0);
        world.spawnParticle(org.bukkit.Particle.EXPLOSION, location, 5, 0.3, 0.3, 0.3, 0.1);
        world.spawnParticle(org.bukkit.Particle.CLOUD, location, 15, 1.0, 1.0, 1.0, 0.1);

        // 环形冲击波效果
        for (int ring = 1; ring <= 3; ring++) {
            final int currentRing = ring;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    for (int i = 0; i < 16; i++) {
                        double angle = 2 * Math.PI * i / 16;
                        double x = location.getX() + Math.cos(angle) * currentRing;
                        double z = location.getZ() + Math.sin(angle) * currentRing;
                        Location ringLoc = new Location(world, x, location.getY(), z);
                        world.spawnParticle(org.bukkit.Particle.ENCHANT, ringLoc, 2, 0.1, 0.1, 0.1, 0);
                    }
                }
            }.runTaskLater(plugin, ring * 2L); // 延迟显示环形效果
        }
    }

    /**
     * 执行IDC19风格的天气控制
     */
    private void performWeatherControl(LivingEntity caster, int stormDuration, double lightningChance) {
        try {
            org.bukkit.World world = caster.getWorld();

            // 播放天气控制音效
            world.playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.5f);
            world.playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 0.8f);

            // 设置雷雨天气
            world.setStorm(true);
            world.setThundering(true);
            world.setWeatherDuration(stormDuration);
            world.setThunderDuration(stormDuration);

            // 显示天气控制粒子效果
            Location casterLoc = caster.getLocation().add(0, 2, 0);
            world.spawnParticle(org.bukkit.Particle.CLOUD, casterLoc, 30, 2.0, 2.0, 2.0, 0.1);
            world.spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK, casterLoc, 20, 1.5, 1.5, 1.5, 0.1);

            // 如果启用闪电概率，在附近召唤闪电
            if (Math.random() <= lightningChance) {
                Player nearestPlayer = findNearestPlayer(caster, 20.0);
                if (nearestPlayer != null) {
                    Location lightningLoc = nearestPlayer.getLocation().add(
                        (Math.random() - 0.5) * 6, 0, (Math.random() - 0.5) * 6);
                    world.strikeLightning(lightningLoc);
                }
            }

            logger.info(String.format("IDC19天气控制: %s 控制天气，持续%d tick，闪电概率%.2f",
                       caster.getType(), stormDuration, lightningChance));

        } catch (Exception e) {
            logger.warning("执行天气控制时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC19风格的三叉戟攻击
     */
    private void performTridentAttack(LivingEntity caster, Player target, double tridentDamage, boolean loyaltyEnabled) {
        try {
            Location shootLocation = caster.getEyeLocation();
            org.bukkit.util.Vector direction = target.getLocation().add(0, 1, 0).subtract(shootLocation).toVector().normalize();

            // 播放三叉戟投掷音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ITEM_TRIDENT_THROW, 1.0f, 1.0f);

            // 发射三叉戟
            org.bukkit.entity.Trident trident = (org.bukkit.entity.Trident) caster.launchProjectile(
                org.bukkit.entity.Trident.class, direction.multiply(2.0));

            // 设置三叉戟属性
            trident.setDamage(tridentDamage);
            if (loyaltyEnabled) {
                // 设置忠诚附魔（如果支持）
                try {
                    org.bukkit.inventory.ItemStack tridentItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.TRIDENT);
                    tridentItem.addEnchantment(org.bukkit.enchantments.Enchantment.LOYALTY, 3);
                    trident.setItem(tridentItem);
                } catch (Exception ignored) {
                    // 某些版本可能不支持
                }
            }

            // 添加元数据用于识别
            trident.setMetadata("idz_trident_attack", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
            trident.setMetadata("idz_trident_damage", new org.bukkit.metadata.FixedMetadataValue(plugin, tridentDamage));

            // 显示投掷粒子效果
            shootLocation.getWorld().spawnParticle(org.bukkit.Particle.CRIT, shootLocation, 8, 0.3, 0.3, 0.3, 0.1);
            shootLocation.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, shootLocation, 5, 0.2, 0.2, 0.2, 0.1);

            logger.info(String.format("IDC19三叉戟攻击: %s 投掷三叉戟攻击 %s，伤害: %.2f，忠诚: %b",
                       caster.getType(), target.getName(), tridentDamage, loyaltyEnabled));

        } catch (Exception e) {
            logger.warning("执行三叉戟攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC19风格的粒子攻击
     */
    private void performParticleAttack(LivingEntity caster, Player target, double particleDamage, int particleCount) {
        try {
            Location casterLoc = caster.getLocation().add(0, 1.5, 0);
            Location targetLoc = target.getLocation().add(0, 1, 0);

            // 播放粒子攻击音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_FIREWORK_ROCKET_LAUNCH, 1.0f, 1.5f);

            // 创建粒子攻击轨迹
            org.bukkit.util.Vector direction = targetLoc.subtract(casterLoc).toVector().normalize();

            for (int i = 0; i < particleCount; i++) {
                final int index = i;

                new org.bukkit.scheduler.BukkitRunnable() {
                    @Override
                    public void run() {
                        if (!isAlive(caster) || target.isDead()) return;

                        // 计算粒子发射角度（扇形攻击）
                        double angleOffset = (Math.random() - 0.5) * 0.5; // ±0.25弧度
                        org.bukkit.util.Vector particleDirection = rotateVector(direction, Math.toDegrees(angleOffset), 0);

                        // 创建粒子轨迹
                        createParticleTrail(casterLoc, particleDirection, particleDamage, 15.0);
                    }
                }.runTaskLater(plugin, index * 2L); // 每个粒子间隔2tick
            }

            logger.info(String.format("IDC19粒子攻击: %s 发射%d个粒子攻击 %s，伤害: %.2f",
                       caster.getType(), particleCount, target.getName(), particleDamage));

        } catch (Exception e) {
            logger.warning("执行粒子攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 创建粒子轨迹
     */
    private void createParticleTrail(Location start, org.bukkit.util.Vector direction, double damage, double maxDistance) {
        new org.bukkit.scheduler.BukkitRunnable() {
            Location currentLoc = start.clone();
            double traveled = 0;

            @Override
            public void run() {
                if (traveled >= maxDistance) {
                    cancel();
                    return;
                }

                // 移动粒子
                currentLoc.add(direction.clone().multiply(0.8));
                traveled += 0.8;

                // 显示粒子效果
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.FIREWORK, currentLoc, 3, 0.1, 0.1, 0.1, 0.05);
                currentLoc.getWorld().spawnParticle(org.bukkit.Particle.ENCHANT, currentLoc, 2, 0.1, 0.1, 0.1, 0.1);

                // 检测碰撞
                for (org.bukkit.entity.Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                    if (entity instanceof Player && entity != start.getWorld().getNearbyEntities(start, 3, 3, 3).stream()
                        .filter(e -> e instanceof LivingEntity && !(e instanceof Player)).findFirst().orElse(null)) {

                        Player hitPlayer = (Player) entity;
                        hitPlayer.damage(damage);

                        // 粒子命中效果
                        hitPlayer.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, hitPlayer.getLocation().add(0, 1, 0), 5, 0.3, 0.3, 0.3, 0.1);
                        hitPlayer.getWorld().playSound(hitPlayer.getLocation(), org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.2f);

                        cancel();
                        return;
                    }
                }

                // 检查是否碰到固体方块
                if (currentLoc.getBlock().getType().isSolid()) {
                    // 爆炸效果
                    currentLoc.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, currentLoc, 3, 0.2, 0.2, 0.2, 0.1);
                    cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * 执行IDC19风格的闪电攻击
     */
    private void performLightningAttack(LivingEntity caster, Player target, double lightningDamage, boolean chainLightning) {
        try {
            // 播放闪电攻击音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

            // 在目标位置召唤闪电
            Location lightningLoc = target.getLocation();
            target.getWorld().strikeLightning(lightningLoc);

            // 造成额外伤害（因为原版闪电伤害可能不够）
            target.damage(lightningDamage, caster);

            // 闪电粒子效果
            target.getWorld().spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK, lightningLoc.add(0, 1, 0), 20, 1.0, 2.0, 1.0, 0.1);

            // 连锁闪电效果
            if (chainLightning) {
                for (org.bukkit.entity.Entity entity : target.getWorld().getNearbyEntities(lightningLoc, 5.0, 3.0, 5.0)) {
                    if (entity instanceof Player && entity != target) {
                        Player chainTarget = (Player) entity;

                        // 延迟0.5秒后连锁闪电
                        new org.bukkit.scheduler.BukkitRunnable() {
                            @Override
                            public void run() {
                                chainTarget.getWorld().strikeLightning(chainTarget.getLocation());
                                chainTarget.damage(lightningDamage * 0.7, caster); // 连锁伤害减少30%

                                // 连锁粒子效果
                                chainTarget.getWorld().spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK,
                                    chainTarget.getLocation().add(0, 1, 0), 15, 0.8, 1.5, 0.8, 0.1);
                            }
                        }.runTaskLater(plugin, 10L);

                        break; // 只连锁一个目标
                    }
                }
            }

            logger.info(String.format("IDC19闪电攻击: %s 闪电攻击 %s，伤害: %.2f，连锁: %b",
                       caster.getType(), target.getName(), lightningDamage, chainLightning));

        } catch (Exception e) {
            logger.warning("执行闪电攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC19风格的隐身能力
     */
    private void performInvisibilitySkill(LivingEntity caster, int invisibilityDuration) {
        try {
            // 播放隐身音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.5f);
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.BLOCK_BEACON_ACTIVATE, 0.8f, 2.0f);

            // 隐身前的粒子效果
            Location casterLoc = caster.getLocation().add(0, 1, 0);
            caster.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, casterLoc, 25, 0.8, 1.0, 0.8, 0.05);
            caster.getWorld().spawnParticle(org.bukkit.Particle.ENCHANT, casterLoc, 15, 0.5, 0.5, 0.5, 0.1);

            // 添加隐身效果
            caster.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.INVISIBILITY, invisibilityDuration, 0));

            // 如果是特定实体类型，设置实体隐身属性
            try {
                caster.setInvisible(true);

                // 延迟恢复可见性
                new org.bukkit.scheduler.BukkitRunnable() {
                    @Override
                    public void run() {
                        if (isAlive(caster)) {
                            caster.setInvisible(false);

                            // 显现时的粒子效果
                            Location loc = caster.getLocation().add(0, 1, 0);
                            caster.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, loc, 20, 0.5, 1.0, 0.5, 0.1);
                            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 0.8f, 0.8f);
                        }
                    }
                }.runTaskLater(plugin, invisibilityDuration);

            } catch (Exception ignored) {
                // 某些实体可能不支持setInvisible
            }

            logger.info(String.format("IDC19隐身能力: %s 隐身 %d tick", caster.getType(), invisibilityDuration));

        } catch (Exception e) {
            logger.warning("执行隐身能力时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC20风格的黑曜石方块攻击（延伸攻击，类似草方块）
     */
    private void performObsidianAttack(LivingEntity caster, Player target, double obsidianDamage, double obsidianSpeed, int slownessDuration) {
        try {
            // 播放黑曜石攻击音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.BLOCK_ANVIL_LAND, 0.8f, 0.8f);

            // 发射黑曜石延伸攻击（类似草方块攻击）
            Location start = caster.getLocation().add(0, 1.5, 0);
            org.bukkit.util.Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
            org.bukkit.World world = start.getWorld();

            // 创建黑曜石延伸路径
            java.util.List<Location> obsidianBlockLocations = new java.util.ArrayList<>();
            final double maxDistance = start.distance(target.getLocation()) + 5;

            new org.bukkit.scheduler.BukkitRunnable() {
                Location currentLoc = start.clone();
                double traveled = 0;
                int step = 0;

                @Override
                public void run() {
                    if (!isAlive(caster) || traveled >= maxDistance) {
                        cleanupObsidianBlocks(obsidianBlockLocations);
                        cancel();
                        return;
                    }

                    // 移动到下一个位置
                    currentLoc.add(direction.clone().multiply(obsidianSpeed));
                    traveled += obsidianSpeed;
                    step++;

                    // 智能处理障碍物
                    Location adjustedLoc = handleObstacles(currentLoc, direction, world);
                    if (adjustedLoc != null) {
                        currentLoc = adjustedLoc;
                    }

                    // 放置黑曜石（每隔一定距离放置一个）
                    if (step % Math.max(1, (int)(1.0 / obsidianSpeed)) == 0) {
                        Location obsidianLoc = findAirLocationForObsidian(currentLoc, world);
                        if (obsidianLoc != null) {
                            org.bukkit.block.Block block = obsidianLoc.getBlock();
                            if (block.getType() == org.bukkit.Material.AIR) {
                                // 只在空气中放置黑曜石
                                block.setType(org.bukkit.Material.OBSIDIAN);
                                obsidianBlockLocations.add(obsidianLoc.clone());

                                // 显示黑曜石生成粒子
                                world.spawnParticle(org.bukkit.Particle.BLOCK, obsidianLoc.clone().add(0.5, 1, 0.5),
                                                  8, 0.3, 0.3, 0.3, 0.1, org.bukkit.Material.OBSIDIAN.createBlockData());
                            }
                        }
                    }

                    // 检测是否击中玩家
                    for (org.bukkit.entity.Entity entity : world.getNearbyEntities(currentLoc, 1.5, 1.5, 1.5)) {
                        if (entity instanceof Player && entity != caster) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(obsidianDamage, caster);

                            // 禁止移动（通过给予100级缓慢效果实现）
                            hitPlayer.addPotionEffect(new org.bukkit.potion.PotionEffect(
                                org.bukkit.potion.PotionEffectType.SLOWNESS, slownessDuration, 100));

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR,
                                hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                            logger.info(String.format("IDC20黑曜石攻击命中玩家: %s，伤害: %.2f，缓慢%d tick",
                                       hitPlayer.getName(), obsidianDamage, slownessDuration));

                            // 延迟清理所有黑曜石
                            cleanupObsidianBlocks(obsidianBlockLocations);
                            cancel();
                            return;
                        }
                    }

                    // 如果到达最大距离，清理黑曜石
                    if (traveled >= maxDistance) {
                        cleanupObsidianBlocks(obsidianBlockLocations);
                    }
                }
            }.runTaskTimer(plugin, 0L, 1L);

        } catch (Exception e) {
            logger.warning("执行黑曜石方块攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 执行IDC20风格的击飞黑曜石柱攻击
     */
    private void performKnockupPillar(LivingEntity caster, Player target, double knockupStrength, double pillarDamage, int pillarHeight) {
        try {
            // 播放击飞音效
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 0.6f);

            Location playerLoc = target.getLocation();
            org.bukkit.World world = playerLoc.getWorld();

            // 向上击飞玩家
            org.bukkit.util.Vector knockupVector = new org.bukkit.util.Vector(0, knockupStrength, 0);
            target.setVelocity(knockupVector);

            // 造成击飞伤害
            target.damage(pillarDamage, caster);

            // 播放击飞音效和粒子效果
            world.playSound(playerLoc, org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
            world.spawnParticle(org.bukkit.Particle.EXPLOSION, playerLoc, 10, 1.0, 1.0, 1.0, 0.2);
            world.spawnParticle(org.bukkit.Particle.SOUL_FIRE_FLAME, playerLoc, 20, 1.0, 1.0, 1.0, 0.1);

            // 在玩家脚下生成黑曜石柱
            java.util.List<Location> pillarLocations = new java.util.ArrayList<>();
            Location baseLoc = playerLoc.clone();
            baseLoc.setY(Math.floor(baseLoc.getY())); // 对齐到方块

            // 生成垂直黑曜石柱
            for (int y = 0; y < pillarHeight; y++) {
                Location pillarLoc = baseLoc.clone().add(0, y, 0);
                if (pillarLoc.getBlock().getType() == org.bukkit.Material.AIR) {
                    pillarLoc.getBlock().setType(org.bukkit.Material.OBSIDIAN);
                    pillarLocations.add(pillarLoc.clone());

                    // 显示黑曜石柱生成粒子
                    world.spawnParticle(org.bukkit.Particle.BLOCK, pillarLoc.clone().add(0.5, 1, 0.5),
                                      5, 0.2, 0.2, 0.2, 0.1, org.bukkit.Material.OBSIDIAN.createBlockData());
                }
            }

            // 延迟清理黑曜石柱
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    for (Location loc : pillarLocations) {
                        try {
                            org.bukkit.block.Block block = loc.getBlock();
                            if (block.getType() == org.bukkit.Material.OBSIDIAN) {
                                block.setType(org.bukkit.Material.AIR);

                                // 显示黑曜石消失粒子
                                loc.getWorld().spawnParticle(org.bukkit.Particle.BLOCK, loc.clone().add(0.5, 1, 0.5),
                                                           3, 0.2, 0.2, 0.2, 0.1, org.bukkit.Material.OBSIDIAN.createBlockData());
                            }
                        } catch (Exception e) {
                            // 忽略清理错误
                        }
                    }
                }
            }.runTaskLater(plugin, 100L); // 5秒后清理黑曜石柱

            logger.info(String.format("IDC20击飞黑曜石柱: %s 击飞玩家 %s，击飞强度: %.2f，伤害: %.2f，柱高: %d",
                       caster.getType(), target.getName(), knockupStrength, pillarDamage, pillarHeight));

        } catch (Exception e) {
            logger.warning("执行击飞黑曜石柱时出错: " + e.getMessage());
        }
    }

    /**
     * 寻找空气位置放置黑曜石（不破坏原有方块）
     */
    private Location findAirLocationForObsidian(Location baseLoc, org.bukkit.World world) {
        // 优先使用原位置（如果是空气）
        if (baseLoc.getBlock().getType() == org.bukkit.Material.AIR) {
            return baseLoc;
        }

        // 尝试周围的空气位置
        Location[] candidates = {
            baseLoc.clone().add(0, 1, 0),   // 上方
            baseLoc.clone().add(0, -1, 0),  // 下方
            baseLoc.clone().add(1, 0, 0),   // 东
            baseLoc.clone().add(-1, 0, 0),  // 西
            baseLoc.clone().add(0, 0, 1),   // 南
            baseLoc.clone().add(0, 0, -1),  // 北
        };

        for (Location candidate : candidates) {
            if (candidate.getBlock().getType() == org.bukkit.Material.AIR) {
                return candidate;
            }
        }

        return null; // 找不到合适的空气位置
    }

    /**
     * 清理黑曜石延伸路径（恢复为空气）
     */
    private void cleanupObsidianBlocks(java.util.List<Location> obsidianBlockLocations) {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                for (Location loc : obsidianBlockLocations) {
                    try {
                        org.bukkit.block.Block block = loc.getBlock();
                        if (block.getType() == org.bukkit.Material.OBSIDIAN) {
                            // 恢复为空气（因为我们只在空气中放置黑曜石）
                            block.setType(org.bukkit.Material.AIR);

                            // 显示黑曜石消失粒子
                            loc.getWorld().spawnParticle(org.bukkit.Particle.BLOCK, loc.clone().add(0.5, 1, 0.5),
                                                       3, 0.2, 0.2, 0.2, 0.1, org.bukkit.Material.OBSIDIAN.createBlockData());
                        }
                    } catch (Exception e) {
                        // 忽略清理错误
                    }
                }
            }
        }.runTaskLater(plugin, 60L); // 3秒后清理所有黑曜石
    }
}
