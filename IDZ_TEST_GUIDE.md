# IDZ怪物生成系统测试指南

## 🧪 测试准备

### 1. 编译和部署
```bash
# 确保插件已编译
mvn package

# 将插件复制到服务器
cp target/deathzombiev4-1.2.jar /path/to/server/plugins/
```

### 2. 服务器启动
- 启动Minecraft服务器
- 确认插件加载成功
- 查看控制台日志确认IDZ系统初始化

## 🎯 测试方案

### 方案A：使用简化测试命令（推荐）

#### 1. 注册测试命令
在plugin.yml中添加：
```yaml
commands:
  idztest:
    description: IDZ怪物测试命令
    usage: /idztest <类型>
    permission: deathzombie.admin
```

#### 2. 测试步骤
```bash
# 1. 测试基础怪物
/idztest basic

# 2. 测试战士怪物（装备系统）
/idztest warrior

# 3. 测试法师怪物（粒子效果）
/idztest mage

# 4. 查看状态
/idztest status

# 5. 清理测试
/idztest cleanup
```

### 方案B：使用现有CZM命令

#### 1. 创建IDZ怪物配置
```bash
# 创建基础测试怪物
/czm idz create test_monster

# 编辑怪物属性（通过GUI）
/czm idz edit test_monster
```

#### 2. 生成怪物
```bash
# 生成单个怪物
/czm idz spawn test_monster

# 生成多个怪物
/czm idz spawn test_monster 3
```

## 🔍 测试检查点

### 1. 基础功能测试

#### ✅ 怪物生成
- [ ] 怪物能正常生成在指定位置
- [ ] 怪物显示名称正确
- [ ] 怪物类型正确（ZOMBIE/SKELETON等）

#### ✅ 属性应用
- [ ] 血量设置正确（检查血量条）
- [ ] 攻击伤害正确（被攻击时测试）
- [ ] 移动速度正确（观察移动）
- [ ] 护甲值正确（受到伤害时测试）

#### ✅ 装备系统
- [ ] 主手武器正确装备
- [ ] 头盔正确装备
- [ ] 胸甲正确装备
- [ ] 装备不会掉落

### 2. 高级功能测试

#### ✅ 粒子效果
- [ ] 粒子类型正确显示
- [ ] 粒子数量符合配置
- [ ] 粒子间隔时间正确
- [ ] 多种粒子类型同时显示

#### ✅ 实体管理
- [ ] 怪物被正确标记为IDZ怪物
- [ ] 实体追踪系统工作正常
- [ ] 清理功能正常工作

### 3. 错误处理测试

#### ✅ 异常情况
- [ ] 无效怪物ID的处理
- [ ] 无效位置的处理
- [ ] 权限不足的处理
- [ ] 服务器重启后的状态

## 📊 预期结果

### 基础怪物 (basic)
```
实体类型: ZOMBIE
血量: 30.0/30.0
攻击伤害: 5.0
移动速度: 0.25
粒子效果: FLAME (3个粒子，40tick间隔)
```

### 战士怪物 (warrior)
```
实体类型: ZOMBIE
血量: 60.0/60.0
攻击伤害: 8.0
移动速度: 0.3
护甲: 5.0
装备: 铁剑、铁头盔、铁胸甲
粒子效果: ENCHANTED_HIT (5个粒子，60tick间隔)
```

### 法师怪物 (mage)
```
实体类型: SKELETON
血量: 40.0/40.0
攻击伤害: 6.0
移动速度: 0.2
装备: 烈焰棒、皮革头盔
粒子效果: REVERSE_PORTAL + ENCHANT (8个粒子，30tick间隔)
```

## 🐛 常见问题排查

### 1. 怪物不生成
- 检查控制台错误日志
- 确认IDZMonsterManager初始化成功
- 检查生成位置是否安全
- 验证怪物配置是否存在

### 2. 属性不正确
- 检查Bukkit版本兼容性
- 确认Attribute枚举名称正确
- 查看属性应用日志

### 3. 粒子效果不显示
- 确认粒子类型映射正确
- 检查粒子生成任务是否启动
- 验证粒子参数配置

### 4. 装备不显示
- 确认EntityEquipment不为null
- 检查物品创建是否成功
- 验证掉落几率设置

## 📝 测试报告模板

```
=== IDZ怪物生成系统测试报告 ===

测试时间: [日期时间]
服务器版本: [Bukkit/Spigot/Paper版本]
插件版本: DeathZombieV4 v1.2

基础功能测试:
- 怪物生成: [✅/❌]
- 属性应用: [✅/❌]
- 装备系统: [✅/❌]

高级功能测试:
- 粒子效果: [✅/❌]
- 实体管理: [✅/❌]

错误处理测试:
- 异常处理: [✅/❌]

发现的问题:
1. [问题描述]
2. [问题描述]

总体评价: [优秀/良好/需改进]
```

## 🚀 性能测试

### 批量生成测试
```bash
# 测试批量生成性能
/idztest warrior  # 生成1个
/czm idz spawn test_monster 5   # 生成5个
/czm idz spawn test_monster 10  # 生成10个

# 观察服务器性能
- TPS变化
- 内存使用
- CPU占用
```

### 长期运行测试
- 生成怪物后等待10分钟
- 检查粒子效果是否持续
- 验证实体是否仍然存在
- 测试服务器重启后的状态

## 📞 问题反馈

如果测试中发现问题，请提供：
1. 详细的错误信息
2. 服务器控制台日志
3. 复现步骤
4. 服务器环境信息

---

**祝测试顺利！** 🎆
