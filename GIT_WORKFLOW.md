# DeathZombieV4 Git 工作流程

## 🌳 分支结构

### 主要分支
- **`main`** - 主分支，稳定版本代码
- **`dev`** - 开发分支，日常开发工作 ⭐ **当前分支**
- **`release`** - 发布分支，准备发布的代码
- **`hotfix`** - 热修复分支，紧急bug修复

### 功能分支
- **`feature/粒子系统重构`** - 粒子效果相关功能
- **`feature/GUI优化`** - 用户界面改进
- **`feature/性能优化`** - 性能相关改进
- **`bugfix/粒子无效果`** - ✅ 已修复

## 🚀 工作流程

### 1. 日常开发流程
```bash
# 在dev分支进行开发
git checkout dev
git pull origin dev

# 创建功能分支
git checkout -b feature/新功能名称

# 开发完成后合并回dev
git checkout dev
git merge feature/新功能名称
git push origin dev
```

### 2. 发布流程
```bash
# 从dev创建release分支
git checkout -b release/v1.3 dev

# 测试和修复
# ... 测试过程 ...

# 合并到main和dev
git checkout main
git merge release/v1.3
git tag v1.3
git checkout dev
git merge release/v1.3
```

### 3. 热修复流程
```bash
# 从main创建hotfix分支
git checkout -b hotfix/紧急修复 main

# 修复完成后合并到main和dev
git checkout main
git merge hotfix/紧急修复
git tag v1.2.1
git checkout dev
git merge hotfix/紧急修复
```

## 📋 提交规范

### 提交消息格式
```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

### 类型说明
- **🎆 feat**: 新功能
- **🔧 fix**: Bug修复
- **📚 docs**: 文档更新
- **💄 style**: 代码格式化
- **♻️ refactor**: 代码重构
- **⚡ perf**: 性能优化
- **✅ test**: 测试相关
- **🔨 build**: 构建相关
- **👷 ci**: CI/CD相关

### 示例提交
```bash
git commit -m "🎆 feat(粒子): 添加80+种粒子类型支持

- 扩展getBukkitParticle()方法映射
- 修复REVERSE_PORTAL、ENCHANTED_HIT等粒子无效果
- 添加智能兼容性检查和安全回退机制
- 支持特殊粒子类型(DUST、BLOCK、ITEM等)

Closes #123"
```

## 🏷️ 版本标签

### 当前版本
- **v1.2** - 当前稳定版本
- **v1.2.1** - 粒子效果bug修复版本 ✅ **最新**

### 版本规划
- **v1.3** - GUI界面优化版本
- **v1.4** - 性能优化版本
- **v2.0** - 重大功能更新版本

## 📊 分支状态

| 分支 | 状态 | 最后更新 | 描述 |
|------|------|----------|------|
| main | 🟢 稳定 | v1.2 | 生产环境代码 |
| dev | 🟡 开发中 | 最新 | 粒子系统修复完成 |
| release | 🔵 准备中 | - | 待发布版本 |
| hotfix | ⚪ 空闲 | - | 紧急修复待命 |

## 🔄 合并策略

### Dev → Main
- 通过Release分支
- 需要完整测试
- 创建版本标签

### Hotfix → Main/Dev
- 直接合并
- 紧急修复优先
- 立即创建补丁版本

### Feature → Dev
- 功能完成后合并
- 代码审查通过
- 测试验证通过

## 📝 最近更新

### 2025-08-07 粒子系统重构 ✅
- ✅ 修复粒子映射不完整问题
- ✅ 添加80+种粒子类型支持
- ✅ 实现多粒子独立参数配置
- ✅ 修复编译兼容性问题
- ✅ 添加智能错误处理机制

### 下一步计划
- [ ] GUI界面优化
- [ ] 性能监控和优化
- [ ] 单元测试覆盖
- [ ] 文档完善

## 🛠️ 开发工具

### 推荐命令
```bash
# 查看分支图
git log --oneline --graph --all

# 查看文件变更
git status

# 查看提交历史
git log --oneline -10

# 创建并切换分支
git checkout -b feature/新功能

# 推送新分支
git push -u origin feature/新功能
```

### Git别名配置
```bash
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.lg "log --oneline --graph --all"
```
