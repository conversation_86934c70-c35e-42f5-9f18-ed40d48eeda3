# IDZ怪物生成系统 - 快速测试

## 🚀 插件状态
- ✅ **编译成功**: BUILD SUCCESS
- ✅ **打包完成**: deathzombiev4-1.2.jar
- ✅ **自动部署**: 已复制到TestServer/plugins/

## 🧪 快速测试步骤

### 1. 启动测试服务器
```bash
# 进入测试服务器目录
cd TestServer

# 启动服务器
java -jar spigot-1.20.1.jar
```

### 2. 验证插件加载
在服务器控制台查看：
```
[INFO] [DeathZombieV4] IDZ怪物管理器初始化完成
[INFO] [DeathZombieV4] IDZ怪物生成器初始化完成
```

### 3. 进入游戏测试

#### 方法A：使用现有CZM命令
```bash
# 1. 创建IDZ怪物配置
/czm idz create test_basic

# 2. 编辑怪物属性（打开GUI）
/czm idz edit test_basic

# 3. 在GUI中设置：
#    - 实体类型: ZOMBIE
#    - 血量: 50
#    - 攻击伤害: 8
#    - 移动速度: 0.3
#    - 粒子类型: FLAME

# 4. 生成怪物
/czm idz spawn test_basic
```

#### 方法B：使用测试命令（需要先注册）
如果你想使用简化的测试命令，需要在plugin.yml中添加：
```yaml
commands:
  idztest:
    description: IDZ怪物测试命令
    usage: /idztest <类型>
    permission: deathzombie.admin
```

然后重启服务器，使用：
```bash
/idztest basic    # 创建并生成基础怪物
/idztest warrior  # 创建并生成战士怪物
/idztest mage     # 创建并生成法师怪物
/idztest status   # 查看状态
/idztest cleanup  # 清理测试怪物
```

## 🔍 测试检查点

### ✅ 基础功能验证
1. **怪物生成**
   - [ ] 怪物出现在玩家前方3格位置
   - [ ] 怪物显示自定义名称
   - [ ] 怪物类型正确（ZOMBIE等）

2. **属性应用**
   - [ ] 血量条显示正确数值
   - [ ] 攻击伤害符合设置（被攻击测试）
   - [ ] 移动速度明显不同于普通怪物

3. **装备系统**（战士怪物）
   - [ ] 手持铁剑
   - [ ] 穿戴铁头盔和胸甲
   - [ ] 装备不会掉落

4. **粒子效果**
   - [ ] 怪物周围出现设定的粒子
   - [ ] 粒子按设定间隔持续出现
   - [ ] 多种粒子类型同时显示（法师怪物）

### ✅ 高级功能验证
1. **实体管理**
   - [ ] 使用 `/idztest status` 能看到生成的怪物数量
   - [ ] 怪物被正确标记为IDZ怪物
   - [ ] 清理功能正常工作

2. **错误处理**
   - [ ] 无效怪物ID时显示错误信息
   - [ ] 权限不足时正确拒绝
   - [ ] 异常情况有详细日志

## 📊 预期测试结果

### 基础怪物 (test_basic)
```
✅ 实体类型: ZOMBIE
✅ 显示名称: "测试基础怪物"
✅ 血量: 30.0/30.0
✅ 攻击伤害: 5.0
✅ 移动速度: 0.25
✅ 粒子效果: FLAME (每2秒3个粒子)
```

### 战士怪物 (test_warrior)
```
✅ 实体类型: ZOMBIE
✅ 显示名称: "测试战士"
✅ 血量: 60.0/60.0
✅ 攻击伤害: 8.0
✅ 移动速度: 0.3
✅ 护甲: 5.0
✅ 装备: 铁剑 + 铁头盔 + 铁胸甲
✅ 粒子效果: ENCHANTED_HIT (每3秒5个粒子)
```

### 法师怪物 (test_mage)
```
✅ 实体类型: SKELETON
✅ 显示名称: "测试法师"
✅ 血量: 40.0/40.0
✅ 攻击伤害: 6.0
✅ 移动速度: 0.2
✅ 装备: 烈焰棒 + 皮革头盔
✅ 粒子效果: REVERSE_PORTAL + ENCHANT (每1.5秒8个粒子)
```

## 🐛 常见问题解决

### 问题1: 插件加载失败
**症状**: 控制台显示插件加载错误
**解决**: 
- 检查Java版本兼容性
- 确认Bukkit/Spigot版本支持
- 查看详细错误日志

### 问题2: 怪物不生成
**症状**: 命令执行但没有怪物出现
**解决**:
- 检查控制台错误日志
- 确认生成位置安全（不在墙内）
- 验证怪物配置存在

### 问题3: 属性不正确
**症状**: 怪物血量/攻击力不符合设置
**解决**:
- 检查Bukkit版本的Attribute支持
- 查看属性应用日志
- 确认配置值合理

### 问题4: 粒子效果不显示
**症状**: 怪物生成但没有粒子
**解决**:
- 确认客户端粒子设置开启
- 检查粒子类型兼容性
- 验证粒子任务启动日志

## 📝 测试报告

请在测试后填写：

```
=== IDZ怪物生成系统测试报告 ===
测试时间: [填写日期时间]
测试环境: [服务器版本]

基础功能测试:
□ 怪物生成: [正常/异常]
□ 属性应用: [正常/异常]  
□ 装备系统: [正常/异常]
□ 粒子效果: [正常/异常]

高级功能测试:
□ 实体管理: [正常/异常]
□ 错误处理: [正常/异常]

发现的问题:
1. [问题描述]
2. [问题描述]

总体评价: [优秀/良好/需改进]
建议改进: [具体建议]
```

## 🎯 性能测试（可选）

如果基础功能正常，可以进行性能测试：

```bash
# 批量生成测试
/czm idz spawn test_basic 5   # 生成5个基础怪物
/czm idz spawn test_warrior 3 # 生成3个战士怪物

# 观察服务器性能
- TPS是否下降
- 内存使用是否正常
- 粒子效果是否流畅
```

## 📞 问题反馈

如果测试中遇到问题，请提供：
1. 详细的错误信息
2. 服务器控制台完整日志
3. 具体的复现步骤
4. 服务器环境信息（版本、插件列表等）

---

**准备好开始测试了吗？** 🎆

插件已经编译完成并部署到TestServer，所有基础功能都已实现。现在就可以启动服务器进行测试了！
